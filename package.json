{"name": "mossbets_bo", "version": "1.0.0", "scripts": {"dev": "vite --port 1313 --host", "build": "vite build", "build-staging": "vite build && cp -R dist/* ../mossbets_bo_v2_pro", "preview": "vite preview --port 4141"}, "dependencies": {"@vuepic/vue-datepicker": "^3.6.8", "accounting": "^0.4.1", "axios": "^1.8.2", "chart.js": "^3.9.1", "country-list-js": "^3.1.7", "date-fns": "^4.1.0", "dropzone-vue3": "^1.0.2", "jquery": "^3.6.1", "md5": "^2.3.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "numeral": "^2.0.6", "sass": "^1.54.4", "sweetalert2": "^11.17.2", "vue": "^3.2.37", "vue-cookies": "^1.8.1", "vue-loaders": "^4.1.4", "vue-loading-overlay": "^5.0.3", "vue-moment": "^4.1.0", "vue-pincode-input": "^0.4.0", "vue-router": "^4.4.5", "vue-select": "^4.0.0-beta.3", "vue-sweetalert2": "^5.0.11", "vue3-datepicker": "^0.4.0", "vue3-daterange-picker": "^1.0.1", "vuejs3-datepicker": "^1.1.3", "vuetable-2": "^1.7.5", "vuex": "^4.1.0", "vuex-persistedstate": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "autoprefixer": "^10.4.8", "postcss": "8.4.38", "tailwindcss": "^3.1.8", "vite": "5.4.6"}, "description": "This template should help get you started developing with Vue 3 in Vite.", "main": "postcss.config.js", "repository": {"type": "git", "url": "git+https://<EMAIL>/gichuwil/expresso-web.git"}, "author": "", "license": "ISC", "homepage": ""}