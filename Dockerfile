# Stage 1: Build the Vue.js application
FROM node:18-alpine as build-stage

WORKDIR /app

# Copy only dependency files to leverage Docker cache
COPY package*.json ./
RUN npm install

# Copy the rest of your application code (exclude your local .env file)
COPY . .

# Build the application for production (output in the 'dist' folder)
RUN npm run build

# Stage 2: Serve the application with Nginx
FROM nginx:stable-alpine as production-stage

# Copy built assets to Nginx's public folder
COPY --from=build-stage /app/dist /usr/share/nginx/html

COPY nginx/nginx.conf /etc/nginx/nginx.conf

# Copy the entrypoint script for runtime environment variable substitution
COPY subst.sh /subst.sh
RUN chmod +x /subst.sh

# Expose port 80
EXPOSE 80

# Run our custom entrypoint script
ENTRYPOINT ["/subst.sh"]
