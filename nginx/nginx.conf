# user nginx;
# worker_processes auto;
# error_log /var/log/nginx/error.log;
# pid /run/nginx.pid;

# # Load dynamic modules. See /usr/share/nginx/README.dynamic.
# include /usr/share/nginx/modules/*.conf;

# worker_rlimit_nofile 4096;

# events {
#     use epoll;
#     worker_connections 4096;
#     multi_accept on;
#     accept_mutex off;
# }

# http {
#     # Include MIME type mappings and set default type
#     include /etc/nginx/mime.types;
#     default_type application/octet-stream;

#     log_format main '$remote_addr - $remote_user [$time_local] "$request" '
#                     '$status $body_bytes_sent "$http_referer" '
#                     '"$http_user_agent" "$http_x_forwarded_for"';
#     access_log /var/log/nginx/access.log main;

#     ## Additional settings (gzip, timeouts, etc.)
#     sendfile            on;
#     tcp_nopush          on;
#     tcp_nodelay         on;
#     keepalive_timeout   65;
#     send_timeout        10;

#     gzip on;
#     gzip_vary on;
#     gzip_buffers 16 8k;
#     gzip_comp_level 3;
#     gzip_min_length 1024;
#     gzip_disable "MSIE [1-6]\.(?!.*SV1)";
#     gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml application/json;

#     # Open files cache
#     open_file_cache max=1024 inactive=20s;
#     open_file_cache_valid 120s;
#     open_file_cache_min_uses 2;
#     open_file_cache_errors off;

#     server_names_hash_bucket_size 128;

#     ## If you need fastcgi cache (for a backend), keep this. For a Vue app it is not necessary.
#     # fastcgi_cache_path /tmp/nginx_cache levels=1:2 keys_zone=ZONE_1:100m inactive=60m;
#     # fastcgi_cache_key "$scheme$request_method$host$request_uri";
#     # add_header X-Cache $upstream_cache_status;
#     # add_header Web 'WEB-2';

#     ##
#     # Primary server block for dash.prod.new.mossbets.bet
#     ##
#     server {
#         #server_name dash.prod.new.mossbets.bet;

#         # Uncomment the following if you want to force HTTPS.
#         # if ($scheme = http) {
#         #     return 301 https://$server_name$request_uri;
#         # }

#         root /usr/share/nginx/html;
#         index index.html;

#         # Main location: serve static files and fallback to index.html for SPA routing
#         location / {
#             try_files $uri $uri/ /index.html;
#         }

#         # Cache static assets
#         location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
#             expires max;
#             log_not_found off;
#             access_log off;
#         }

#         # Deny access to hidden files (like .git, .env, etc.)
#         location ~ /\.(?!well-known) {
#             deny all;
#         }
#     }
# }

# For more information on configuration, see:
#   * Official English Documentation: http://nginx.org/en/docs/
#   * Official Russian Documentation: http://nginx.org/ru/docs/

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

worker_rlimit_nofile 4096;
events {
    use epoll;
    worker_connections 4096;
    multi_accept on;
    accept_mutex off;
}

http {
     log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    ##request tracing using custom format
    # log_format custom '$remote_addr - $remote_user [$time_local] '
    #                   '"$request" | status="$status" | "$http_referer"'
    #                   ' rt="$request_time" | uct="$upstream_connect_time" | uht="$upstream_header_time" | "tat="$upstream_response_time" ';

    ##this uses the our custom log format
    access_log /var/log/nginx/custom.log;

    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    types_hash_max_size 2048;
    keepalive_timeout 65;
    send_timeout 10;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;


    client_body_buffer_size 32k;
    client_body_in_single_buffer on;
    client_body_timeout 180s;
    client_header_timeout 180s;
    client_header_buffer_size 32k;
    large_client_header_buffers 4 32k;

    # Gzip Settings
    gzip on;
    gzip_vary on;
    gzip_buffers 16 8k;
    gzip_comp_level 3;
    gzip_min_length 1024;
    gzip_disable "MSIE [1-6]\.(?!.*SV1)";
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml application/json;

    #Open files cache
    open_file_cache max=1024 inactive=20s;
    open_file_cache_valid 120s;
    open_file_cache_min_uses 2;
    open_file_cache_errors off;

    # Load modular configuration files from the /etc/nginx/conf.d directory.
    # See http://nginx.org/en/docs/ngx_core_module.html#include
    # for more information.
    include /etc/nginx/conf.d/*.conf;

    server_names_hash_bucket_size 128;

    # Configure microcache (fastcgi)
    fastcgi_cache_path /tmp/nginx_cache levels=1:2 keys_zone=ZONE_1:100m inactive=60m;
    fastcgi_cache_key "$scheme$request_method$host$request_uri";
    add_header X-Cache $upstream_cache_status;
    add_header Web 'WEB-2';

    server_tokens off;

    ##
    # SSL Settings
    ##
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2 TLSv1.3; # Dropping SSLv3, ref: POODLE
    ssl_ciphers 'ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-DSS-AES128-GCM-SHA256:kEDH+AESGCM:ECDHE-RSA-AES128-SHA256:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES128-SHA:ECDHE-ECDSA-AES128-SHA:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES256-SHA384:ECDHE-RSA-AES256-SHA:ECDHE-ECDSA-AES256-SHA:DHE-RSA-AES128-SHA256:DHE-RSA-AES128-SHA:DHE-DSS-AES128-SHA256:DHE-RSA-AES256-SHA256:DHE-DSS-AES256-SHA:DHE-RSA-AES256-SHA:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!3DES:!MD5:!PSK';
    ssl_prefer_server_ciphers on;

    server {
        listen       80;
        listen       [::]:80;
        server_name  _;
        root         /usr/share/nginx/html;

        # Load configuration files for the default server block.
        include /etc/nginx/default.d/*.conf;

        error_page 404 /404.html;
            location = /404.html {
        }

        error_page 500 502 503 504 /50x.html;
            location = /50x.html {
        }
    }

    server {
                server_name dash.prod.new.mossbets.bet;
                #server_name dev.v1.web.mossbets.co.ke;
                root  /usr/share/nginx/html;
                index index.php index.html;

                charset utf-8;
                # Load configuration files for the default server block.
                include /etc/nginx/default.d/*.conf;

                # Cache by default
                set $no_cache 0;

                # Check for cache bypass
                if ($request_method = POST) {
                        set $no_cache 1;
                }

                # Check for cache bypass
                if ($arg_skipcache = 1) {
                        set $no_cache 1;
                }

                #try_files $uri $uri/ @rewrite;
                #location @rewrite {
                #    rewrite ^/(.*)$ /index.php?_url=/$1;
                #}

                location /{
                        try_files $uri $uri/ /index.html;
                }

                location ~ \.php$ {
                        fastcgi_pass  unix:/run/php-fpm/www.sock;
                        fastcgi_index index.php;

                        include fastcgi_params;
                        include /etc/nginx/fastcgi_params;
                        fastcgi_split_path_info       ^(.+\.php)(/.+)$;
                        fastcgi_param PATH_INFO       $fastcgi_path_info;
                        fastcgi_param PATH_TRANSLATED $document_root$fastcgi_path_info;
                        fastcgi_param SCRIPT_FILENAME $request_filename;

                        # Enable cache
                        fastcgi_cache ZONE_1;
                        fastcgi_cache_valid 200 6s;
                        fastcgi_cache_bypass $no_cache;
                        fastcgi_no_cache $no_cache;

                        # max request size
                        client_max_body_size 10;
                }

                location ~* \.(js|css|png|jpg|jpeg|gif|ico|mp3)$ {
                        expires       max;
                        log_not_found off;
                        access_log    off;
                }

                location ~* \.(eot|ttf|woff|woff2)$ {
                        add_header Access-Control-Allow-Origin *;
                }

                #deny access to .git
                location ~ (?:^|/)\. {
                        deny all;
                }

                location ~ /\.hta {
                    deny all;
                }

}

server {
    if ($host = dash.prod.new.mossbets.bet) {
        return 301 https://dash.prod.new.mossbets.bet$request_uri;
    } # managed by Certbot

    listen 80;
    listen [::]:80;

    server_name dash.prod.new.mossbets.bet;
    return 404; # managed by Certbot
   }
}
    