# AutoTable Component Usage Guide

## Overview
The AutoTable component automatically generates table columns from your data objects and provides simple features like column exclusion, column arrangement, custom styling, and improved pagination.

## Key Features

### 1. Exclude Columns
Use the `excludeColumns` prop to hide specific data keys from being displayed as columns.

```vue
<auto-table
  :data="userData"
  :exclude-columns="['password', 'internal_id', 'temp_field']"
/>
```

**Example Data:**
```javascript
const userData = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'secret123',      // This will be hidden
    internal_id: 'INT_001',     // This will be hidden
    temp_field: 'temporary',    // This will be hidden
    created_at: '2024-01-01'
  }
];
```

**Result:** Only `id`, `name`, `email`, and `created_at` columns will be displayed.

### 2. Column Arrangement
Use the `colArrangement` prop to define the order of columns from left to right. Any columns not specified will follow in their original object order.

```vue
<auto-table
  :data="userData"
  :col-arrangement="['name', 'email', 'created_at']"
/>
```

**Example Data:**
```javascript
const userData = [
  {
    id: 1,
    name: '<PERSON> Doe',
    email: '<EMAIL>',
    created_at: '2024-01-01',
    status: 'active'
  }
];
```

**Result:** Columns will be displayed as: `name`, `email`, `created_at`, `id`, `status` (arranged columns first, then remaining in original order).

**Note:** You can combine `colArrangement` with `excludeColumns`:
```vue
<auto-table
  :data="userData"
  :col-arrangement="['name', 'email', 'created_at']"
  :exclude-columns="['id']"
/>
```
**Result:** `name`, `email`, `created_at`, `status` (id excluded, status follows in original order)

### 3. Improved Pagination
The component now features numbered pagination buttons with smart ellipsis handling.

#### Pagination Features:
- **Page Limit Selector**: Dropdown to choose items per page (10, 20, 50, 100, etc.)
- **Numbered Pages**: Shows page numbers like "Prev 1 2 3 ... 89 Next"
- **Smart Ellipsis**: Automatically shows "..." for large page ranges
- **Current Page Highlighting**: Active page is highlighted in blue

#### Pagination Props:
```vue
<auto-table
  :data="items"
  :pagination="true"
  :total-items="1000"
  :items-per-page="25"
  :current-page-prop="currentPage"
  :items-per-page-options="[10, 25, 50, 100, 200]"
  @page-change="handlePageChange"
  @items-per-page-change="handleItemsPerPageChange"
/>
```

### 3. Dropdown Actions
The AutoTable now uses the same ActionDropdown and ActionItem components as DataTable for consistent styling and behavior.

#### Action Object Structure:
```javascript
{
  label: 'Action Name',        // Required: Text to display
  action: () => doSomething(), // Required: Function to execute
  icon: 'fas fa-edit',         // Optional: FontAwesome icon class
  color: 'blue'                // Optional: Color theme (auto-detected if not provided)
}
```

#### Auto-detected Colors:
The component automatically detects appropriate colors based on action labels:
- **Blue**: edit, update, modify
- **Red**: delete, remove, ban
- **Indigo**: view, show, details
- **Green**: activate, enable, approve
- **Yellow**: deactivate, disable, suspend
- **Default**: gray for other actions

#### Example Actions:
```javascript
getRowActions(item) {
  return [
    {
      label: 'View Details',      // Auto-detected as 'indigo'
      action: () => this.viewItem(item),
      icon: 'fas fa-eye'
    },
    {
      label: 'Edit Permission',   // Auto-detected as 'blue'
      action: () => this.editItem(item),
      icon: 'fas fa-edit'
    },
    {
      label: 'Delete',           // Auto-detected as 'red'
      action: () => this.deleteItem(item),
      icon: 'fas fa-trash'
    },
    {
      label: 'Custom Action',
      action: () => this.customAction(item),
      icon: 'fas fa-cog',
      color: 'indigo'           // Explicit color override
    }
  ];
}
```

### 4. Complete Example

```vue
<template>
  <auto-table
    :data="permissions"
    :exclude-columns="['password_hash', 'internal_notes', 'temp_data']"
    :col-arrangement="['name', 'email', 'role', 'status', 'created_at']"
    :has-actions="true"
    :get-actions="getRowActions"
    :column-styles="columnStyles"
    :header-styles="headerStyles"
    :pagination="true"
    :total-items="total"
    :items-per-page="limit"
    :current-page-prop="currentPage"
    :items-per-page-options="[10, 25, 50, 100, 200]"
    @page-change="handlePageChange"
    @items-per-page-change="handleItemsPerPageChange"
  />
</template>

<script>
export default {
  data() {
    return {
      permissions: [],
      total: 0,
      limit: 25,
      currentPage: 1,
      
      // Custom styling for columns
      columnStyles: {
        id: 'text-center font-bold text-blue-600',
        name: 'font-semibold text-gray-800',
        status: 'text-center'
      },
      
      // Custom styling for headers
      headerStyles: {
        id: 'text-center font-bold text-blue-700 bg-blue-50',
        name: 'font-bold text-gray-700 bg-gray-50',
        status: 'text-center font-bold text-green-700 bg-green-50'
      }
    };
  },
  
  methods: {
    handlePageChange(page) {
      this.currentPage = page;
      this.loadData();
    },
    
    handleItemsPerPageChange(newLimit) {
      this.limit = newLimit;
      this.currentPage = 1; // Reset to first page
      this.loadData();
    },
    
    getRowActions(item) {
      return [
        {
          label: 'Edit',
          action: () => this.editItem(item),
          icon: 'fas fa-edit'
        },
        {
          label: 'Delete',
          action: () => this.deleteItem(item),
          icon: 'fas fa-trash'
        }
      ];
    }
  }
};
</script>
```

## Pagination Logic

The pagination component intelligently displays page numbers:

- **≤ 7 pages**: Shows all page numbers
- **> 7 pages**: Uses smart ellipsis
  - Current page ≤ 4: `1 2 3 4 5 ... last`
  - Current page ≥ total-3: `1 ... last-4 last-3 last-2 last-1 last`
  - Middle pages: `1 ... current-1 current current+1 ... last`

## Props Reference

### Core Props
- `data` / `items`: Array of data objects
- `excludeColumns`: Array of column keys to exclude
- `colArrangement`: Array of column keys to define column order
- `hasActions`: Boolean to show actions column
- `getActions`: Function returning actions for each row

### Pagination Props
- `pagination`: Boolean to enable pagination
- `totalItems`: Total number of items
- `itemsPerPage`: Items per page
- `currentPageProp`: Current page number
- `itemsPerPageOptions`: Array of page size options
- `serverSidePagination`: Boolean for server-side pagination

### Styling Props
- `columnStyles`: Object with CSS classes for body cells
- `headerStyles`: Object with CSS classes for headers
- `emptyMessage`: Message when no data available

### Events
- `@page-change`: Emitted when page changes
- `@items-per-page-change`: Emitted when page size changes

## Best Practices

1. **Always exclude sensitive data**: Use `excludeColumns` for passwords, tokens, internal IDs
2. **Use server-side pagination**: For large datasets, enable `serverSidePagination`
3. **Provide meaningful actions**: Use `getActions` to provide relevant row actions
4. **Style consistently**: Use `columnStyles` and `headerStyles` for consistent appearance
5. **Handle page changes**: Always implement `@page-change` and `@items-per-page-change` handlers
