<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Virtual" pageSubtitle="Bets" />
    <div class="px-4 mb-5">
      <div class="bg-white rounded-lg shadow p-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium text-gray-700">Filters</h3>
          <div class="flex gap-1">
            <button @click="applyFilters()" class="px-3 py-1 text-sm bg-indigo-600 text-white rounded-md hover:bg-indigo-700 flex items-center">
              <i class="fas fa-search mr-1"></i> Search
            </button>
            <button @click="resetFilters()" class="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
              Reset
            </button>
          </div>
        </div>

        <!-- First row of filters - 4 groups -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 mb-3">

          <!-- Bet Identification -->
          <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
            <h4 class="font-medium text-gray-700 mb-1 text-sm">Bet Identification</h4>
            <div class="space-y-2">
              <div>
                <label class="block text-xs text-gray-600 mb-1">Phone Number</label>
                <input type="number" placeholder="Enter phone number"
                       class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                       v-model="moreParams.mobile_number"
                       @keyup.enter="setBets()">
              </div>
              <div>
                <label class="block text-xs text-gray-600 mb-1">Bet Reference</label>
                <input type="text" placeholder="Enter bet reference"
                       class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                       v-model="moreParams.bet_reference"
                       @keyup.enter="setBets()">
              </div>
              <div>
                <label class="block text-xs text-gray-600 mb-1">Game Name</label>
                <input type="text" placeholder="Enter game name"
                       class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                       v-model="moreParams.game_name"
                       @keyup.enter="setBets()">
              </div>
            </div>
          </div>

          <!-- Amount Ranges -->
          <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
            <h4 class="font-medium text-gray-700 mb-1 text-sm">Amount Ranges</h4>
            <div class="space-y-2">
              <div>
                <label class="block text-xs text-gray-600 mb-1">Bet Amount</label>
                <div class="flex gap-1">
                  <input type="number" placeholder="Min"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.stake_amount_min"
                         @keyup.enter="setBets()">
                  <input type="number" placeholder="Max"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.stake_amount_max"
                         @keyup.enter="setBets()">
                </div>
              </div>
              <div>
                <label class="block text-xs text-gray-600 mb-1">Possible Win</label>
                <div class="flex gap-1">
                  <input type="number" placeholder="Min"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.winning_amount_min"
                         @keyup.enter="setBets()">
                  <input type="number" placeholder="Max"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.winning_amount_max"
                         @keyup.enter="setBets()">
                </div>
              </div>

              <div>
                <label class="block text-xs text-gray-600 mb-1">Bet Type</label>
                <select class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500" v-model="selected_bet_type">
                  <option value="" disabled selected>Select Bet Type</option>
                  <option v-for="type in bet_types" :value="type.value">
                    {{ type.text }}
                  </option>
                </select>
              </div>
            </div>
          </div>

          <!-- Bet Details -->
          <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
            <h4 class="font-medium text-gray-700 mb-1 text-sm">Bet Details</h4>
            <div class="space-y-2">
              <div>
                <label class="block text-xs text-gray-600 mb-1">Odds</label>
                <div class="flex gap-1">
                  <input type="number" placeholder="Min"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.odds_min"
                         @keyup.enter="setBets()">
                  <input type="number" placeholder="Max"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.odds_max"
                         @keyup.enter="setBets()">
                </div>
              </div>
              <div>
                <label class="block text-xs text-gray-600 mb-1">Selections</label>
                <div class="flex gap-1">
                  <input type="number" placeholder="Min"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.selections_min"
                         @keyup.enter="setBets()">
                  <input type="number" placeholder="Max"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.selections_max"
                         @keyup.enter="setBets()">
                </div>
              </div>

              <!-- <div>
                <label class="block text-xs text-gray-600 mb-1">Selection Type</label>
                <select class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500" v-model="selected_selection_type">
                  <option value="" disabled selected>Select Selection Type</option>
                  <option v-for="type in selection_types" :value="type.value">
                    {{ type.text }}
                  </option>
                </select>
              </div> -->
            </div>
          </div>

           <!-- Date -->
           <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
            <h4 class="font-medium text-gray-700 mb-1 text-sm">Date</h4>
            <div class="space-y-2">
              <div>
                <label class="block text-xs text-gray-600 mb-1">Bets Placed On</label>
                <VueDatePicker v-model="date" range :preset-ranges="presetRanges" position="center" :clearable="false"
                             :enable-time-picker="false" @closed="selectDate"
                             class="w-full text-xs">
                  <template #yearly="{ label, range, presetDateRange }">
                    <span @click="presetDateRange(range)">{{ label }}</span>
                  </template>
                </VueDatePicker>
              </div>
              <div>
                <label class="block text-xs text-gray-600 mb-1">Bet Status</label>
                <select class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500" v-model="selected_bet_status">
                  <option value="" disabled selected>Select Bet Status</option>
                  <option v-for="type in bet_statuses" :value="type.value">
                    {{ type.text }}
                  </option>
                </select>
              </div>
            </div>
          </div>

        </div>


      </div>
    </div>


    <!-- Summary Cards - Only show if there is data -->
    <div v-if="bets.length > 0" class="px-4 mb-5">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Total Bet Amount Card -->
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg p-4 text-white">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-blue-100 text-sm font-medium">Total Bet Amount</p>
              <p class="text-2xl font-bold">{{ formatNumberWithCommas(totalBetAmount) }}</p>
            </div>
            <div class="bg-blue-400 bg-opacity-30 rounded-full p-3">
              <i class="fas fa-coins text-xl"></i>
            </div>
          </div>
        </div>

    

        <!-- Total Possible Win Card -->
        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-lg p-4 text-white">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-green-100 text-sm font-medium">Total Possible Win</p>
              <p class="text-2xl font-bold">{{ formatNumberWithCommas(totalPossibleWin) }}</p>
            </div>
            <div class="bg-green-400 bg-opacity-30 rounded-full p-3">
              <i class="fas fa-trophy text-xl"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <auto-table
      :headers="tableHeaders"
      :data="bets"
      :has-actions="true"
      :get-actions="getRowActions"
      :total-items="total"
      :items-per-page="limit"
      :current-page-prop="offset"
      :server-side-pagination="true"
      :pagination="total > limit"
      :show-items-count="true"
      :items-per-page-options="[10, 25, 50, 100]"
      @page-change="gotToPage"
      @items-per-page-change="changeLimit"
    >
      <!-- Bet ID Column -->
      <template #bet_id="{ item }">
        <div>{{ item.bet_id }}</div>
      </template>

      <!-- Bet Reference Column -->
      <template #bet_reference="{ item }">
        <div>{{ item.bet_reference }}</div>
      </template>

      <template #ip="{ item }">
        <div>{{  getExtraDataValue(item,'msisdn') }}</div>
        <div>{{  getExtraDataValue(item,'ip') }}</div>
        
      </template>

      <!-- Customer Column -->
      <template #created_by="{ item }">
        <!-- <div>{{ item.created_by }}</div> -->
        <div class="badge-container">
          <span class="game-badge"
                :class="{
                  'bg-cyan-500': getExtraDataValue(item, 'gameName') === 'Virtual League',
                  'bg-teal-500': getExtraDataValue(item, 'gameName') === 'Virtual Turbo League',
                  'bg-orange-500': getExtraDataValue(item, 'gameName') === 'JetX'
                }">
            {{ getExtraDataValue(item, "gameName") }}
          </span>
        </div>
      </template>

      <!-- Game Name Column -->
      <template #game_name="{ item }">
        <div class="badge-container">
          <span class="game-badge"
                :class="{
                  'bg-cyan-500': getExtraDataValue(item, 'gameName') === 'Virtual League',
                  'bg-teal-500': getExtraDataValue(item, 'gameName') === 'Virtual Turbo League',
                  'bg-orange-500': getExtraDataValue(item, 'gameName') === 'JetX',
                  'bg-indigo-500': getExtraDataValue(item, 'gameName') === 'Aviatrix',
                  'bg-purple-500': getExtraDataValue(item, 'gameName') === 'Pick A Box',
                  'bg-yellow-500': getExtraDataValue(item, 'gameName') === 'Pragmatic'

                }">
            {{ getExtraDataValue(item, "gameName") }}
          </span>
        </div>
      </template>

      <!-- Stake Column -->
      <template #bet_amount="{ item }">
        <div>Ksh.{{ formatNumber(item.bet_amount) }}</div>
      </template>

      <!-- Possible Win Column -->
      <template #possible_win="{ item }">
        <div>
          <strong>Ksh.{{ formatNumber(item.possible_win) }}</strong>
          <!-- <br> -->
          <!-- <span style="font-size: 11px;">W.Tax: Ksh.{{ formatNumber(item.witholding_tax) }}</span> -->
        </div>
      </template>

      <!-- Bet Type Column -->
      <template #bet_type="{ item }">
        <div class="bet-type-badge" :class="getBetTypeClass(item.bet_type)">
          {{ getBetTypeText(item.bet_type) }}
        </div>
      </template>

      <!-- Total Games Column -->
      <template #total_games="{ item }">
        <div class="text-center">{{ item.total_games }}</div>
      </template>

      <!-- Total Odd Column -->
      <template #total_odd="{ item }">
        <div class="text-center">{{ formatNumber(item.total_odd) }}</div>   
        </template>

      <!-- Status Column -->
      <template #status="{ item }">
        <div class="status-badge" :class="getStatusClass(item.status)">
          {{ getStatusText(item.status) }}
        </div>
      </template>

      <!-- Date Column -->
      <template #created_at="{ item }">
        <div>{{ moment(item.created_at).format('llll') }}</div>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item, index }">
        <div class="relative inline-block">
          <button v-if="(parseInt(item.match_status) === 0) || (parseInt(item.match_status) === null)"
                  class="px-3 py-1 flex items-center space-x-1">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                 stroke="#808080" class="w-6 h-6">
              <path stroke-linecap="round" stroke-linejoin="round"
                    d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </button>
          <action-dropdown v-else>
            <action-item @click="viewExtraData(item, index)">
              View Extra Data
            </action-item>
          </action-dropdown>
        </div>
      </template>
    </auto-table>

    <!-- Modal Wrapper -->
    <div v-if="isModalOpen" class="fixed inset-0 bg-gray-500 bg-opacity-50 z-50 flex items-center justify-center">
      <!-- Modal Content -->
      <div class="bg-white rounded-lg shadow-lg overflow-hidden w-full max-w-4xl p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-bold">Extra Data <span class="font-normal">
            {{ extraData.created_by }} ({{ extraData.bet_reference }}) </span>
          </h3>
          <button @click="isModalOpen=false" class="text-gray-500 hover:text-gray-700">&times;</button>
        </div>

        <div class="mb-4">
          <span class="text-sm font-bold">Phone number:</span>
          <pre>{{ getExtraDataValue(extraData,"msisdn") }}</pre>
        </div>

        <div class="mb-4">
          <span class="text-sm font-bold">Game Name:</span>
          <pre>{{ getExtraDataValue(extraData, "gameName") }}</pre>
        </div>

        <!-- Table Inside Modal -->
      </div>
    </div>


  </div>
</template>

<script>
import moment from "moment";
import {mapActions} from "vuex";
import {endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths} from "date-fns";
import VueDatePicker from "@vuepic/vue-datepicker";
import { AutoTable, ActionDropdown, ActionItem, CustomLoading } from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
    VueDatePicker,
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      checkboxes: {
        winning_amount: false,
        odds: false,
        selections: false,
        bet_amount: false,
      },
      extraData:{},
      showDropdown: [],
      viewModelOpen: false,
      //
      isModalOpen: false, // This will control modal visibility
      bet_slips: [], // Your data
      modal_total: 0,
      modal_limit: 10,
      modal_offset: 1,

      //
      selectBet: {},
      bet_slip_params: {
        bet_id: '',
        sport_id: '',
        status: '',
        start: '',
        end: '',
        page: '',
        limit: "10",
        timestamp: 'timestamp',
        skip_cache: '',
      },

      //
      bets: [],
      //
      moreParams: {
        stake_amount_min: '',
        stake_amount_max: '',
        winning_amount_min: '',
        winning_amount_max: '',
        odds_min: '',
        odds_max: '',
        selections_min: '',
        selections_max: '',
        bet_type: '',
        selection_type: '',
        profile_id: '',
        mobile_number: '',
        bet_reference: '',
        game_name: '',
        status: '',
        sort: '',
        start: '',
        end: '',
        page: '',
        limit: "100", // This should match the limit property above
        timestamp: 'timestamp',
        skip_cache: '',
      },

      selected_game_type: {},
      game_types: [
        {name: 'sports', value: 'sports'},
        {name: 'Virtual', value: 'virtual'},
        {name: 'Instant', value: 'instant'},
        {name: 'Casino', value: 'casino'},
      ],

      selected_selection_type: {},
      selection_types: [
        {text: 'Single Bet', value: 1},
        {text: 'Multi Bet', value: 2},
      ],

      selected_bet_type: {},
      bet_types: [
        {text: 'Cash Bet', value: 0},
        {text: 'Bonus Bet', value: 1},
        {text: 'Free Bet', value: 2},
      ],

      selected_is_live: {},
      is_live_types: [
        {text: 'PreMatch', value: 1},
        {text: 'Live', value: 2},
      ],

      selected_bet_status: {text: 'All', value: ''},
      bet_statuses: [
        {text: 'All', value: ''},
        {text: 'Pending', value: 0},
        {text: 'Won ', value: 1},
        {text: 'Lost ', value: 3},
        {text: 'Canceled ', value: 9},
      ],

      date: null,
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],

      tableHeaders: [
        { key: 'ip', label: 'Customer', align: 'left' },
        // { key: 'bet_id', label: 'Bet ID', align: 'center' },
        { key: 'bet_reference', label: 'Bet Reference', align: 'left' },
        // { key: 'created_by', label: 'Customer', align: 'center' },
        { key: 'game_name', label: 'Game Name', align: 'center' },
        { key: 'bet_amount', label: 'Stake', align: 'center' },
        { key: 'total_odd', label: 'Total Odd', align: 'center' },
        { key: 'possible_win', label: 'Possible Win', align: 'center' },
        { key: 'bet_type', label: 'Bet Type', align: 'center' },
        // { key: 'total_games', label: 'Total Games', align: 'center' },
        { key: 'status', label: 'Status', align: 'center' },
        { key: 'created_at', label: 'Date', align: 'center' },
      ],

      rowHighlightConditions: {
        '0': 'bg-neutral-100',
        '1': 'bg-green-100',
        '3': 'bg-red-100',
      },
    }
  },
  computed: {
    hasActiveNumericFilters() {
      return Object.values(this.checkboxes).some(value => value === true);
    },

    // Calculate total bet amount from all bets
    totalBetAmount() {
      return this.bets.reduce((total, bet) => {
        const amount = parseFloat(bet.bet_amount) || 0;
        return total + amount;
      }, 0);
    },

    // Calculate total odds from all bets
    totalOdds() {
      return this.bets.reduce((total, bet) => {
        const odds = parseFloat(bet.total_odd) || 0;
        return total + odds;
      }, 0);
    },

    // Calculate total possible win from all bets
    totalPossibleWin() {
      return this.bets.reduce((total, bet) => {
        const possibleWin = parseFloat(bet.possible_win) || 0;
        return total + possibleWin;
      }, 0);
    }
  },
  watch: {
    // watch if moreParams.bet_type changes
    selected_game_type(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.moreParams.game_type = newVal
        this.setBets()
      }
    },

    // selected_selection_type
    selected_selection_type(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.moreParams.selection_type = newVal
        this.setBets()
      }
    },

    // selected_bet_type
    selected_bet_type(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.moreParams.bet_type = newVal
        this.setBets()
      }
    },

    // selected_bet_status
    selected_bet_status(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.moreParams.status = newVal
        this.setBets()
      }
    },

  },
  mounted() {
    this.setBets()
  },
  methods: {
    ...mapActions(["getVirtualBets", "getBetSlips", "repostDeposit", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    getRowActions(item) {
      return [
        {
          label: 'View Bet Slip',
          action: () => this.viewBetSlip(item),
          icon: 'eye',
          class: 'text-blue-600'
        },
        {
          label: 'View Extra Data',
          action: () => this.openModal(item),
          icon: 'info',
          class: 'text-green-600'
        }
      ];
    },

    async selectDate() {
      let vm = this

      // If date is null (cleared), reset date filters and fetch data
      if (!this.date) {
        console.log('Date filter cleared, resetting and fetching data...');
        vm.moreParams.start = '';
        vm.moreParams.end = '';
        vm.moreParams.timestamp = Date.now();
        await vm.setBets();
        return;
      }

      // If date range is incomplete, return without doing anything
      if (!this.date[0] || !this.date[1]) return;

      // Update date filter values
      vm.moreParams.start = vm.formatDate(this.date[0]);
      vm.moreParams.end = vm.formatDate(this.date[1]);
      vm.moreParams.timestamp = Date.now();

      // Log that date filter was updated and fetch data
      console.log('Date filter updated, fetching data with new date range...');
      await vm.setBets();
    },

    formatDate(date) {
      var d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    // Pagination and dropdowns
    gotToPage(page) {
      let vm = this
      vm.moreParams.page = page
      vm.offset = page
      vm.setBets()
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    toggleDetails(data) {
      this.viewModelOpen = true;
      this.deposit = data;
    },

    async viewBetSlip(data) {
      let app = this
      app.closeDropdown()
      // route to bet slip
      app.$router.push({name: 'bet-slip', params: {id: data.bet_id}});
    },

    applyFilters(filters) {
      this.moreParams.page = ''
      this.moreParams.offset = ''
      this.setBets()
    },

    resetFilters() {
      // Reset all filter parameters
      this.moreParams = {
        stake_amount_min: '',
        stake_amount_max: '',
        winning_amount_min: '',
        winning_amount_max: '',
        odds_min: '',
        odds_max: '',
        selections_min: '',
        selections_max: '',
        bet_type: '',
        selection_type: '',
        profile_id: '',
        mobile_number: '',
        bet_reference: '',
        game_name: '',
        status: '',
        sort: '',
        start: '',
        end: '',
        page: '',
        limit: "100",
        timestamp: 'timestamp',
        skip_cache: '',
      };

      // Reset dropdown selections
      this.selected_bet_status = { text: 'All', value: ''};
      this.selected_bet_type = {};
      this.selected_selection_type = {};
      this.selected_game_type = {};

      // Reset date picker
      this.date = null;

      // Fetch data with reset filters
      this.setBets();
    },

    async setBets() {
      let app = this
      app.isLoading = true

      // Ensure limit is set in params
      app.moreParams.limit = app.limit.toString();

      let response = await this.getVirtualBets(app.moreParams)

      // console.log("Bets OK: " + JSON.stringify(response))
      app.bets = []
      app.total = 0
      if (response.status === 200) {
        app.bets = response.message.result

        if (response.message.record_count !== 0) {
          app.total = response.message.record_count
        }

        app.showDropdown = []
        for (let i = 0; i < app.bets.length; i++) {
          app.showDropdown.push(false)
        }
      } else {
        app.bets = [];
        app.total = 0;
      }
      app.isLoading = false
    },

    async viewExtraData(bet,index) {
      let app = this
      app.showDropdown[index] = false
      app.extraData = bet
      // console.log("extraData:::> ", JSON.stringify(app.extraData));
      app.isModalOpen = true
    },

    // Format currency with commas and put negative figures in brackets (standardized from master)
    formatCurrency(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0.00';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

    // Format numbers with commas (2 decimals for amounts) and put negative figures in brackets
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0.00';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

    // Format number with commas for totals (standardized from master)
    formatNumberWithCommas(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0.00';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

    getExtraDataValue(data, key) {
      // console.log("extraData::: ", data);
      if (!data.extra_data) return null; // Check if extra_data exists

      try {
        const extraData = JSON.parse(data.extra_data); // Parse JSON string
        // console.log("extraData", extraData);
        return extraData[key] ?? null; // Return the requested key or null if not found
      } catch (error) {
        console.error("Error parsing extra_data:", error);
        return null;
      }
    },

    getBetTypeClass(betType) {
      const typeMap = {
        '0': 'bet-type-cash',
        '1': 'bet-type-bonus',
        '2': 'bet-type-free'
      };

      return typeMap[betType] || 'bet-type-default';
    },

    getBetTypeText(betType) {
      const typeMap = {
        '0': 'Cash Bet',
        '1': 'Bonus Bet',
        '2': 'Free Bet'
      };

      return typeMap[betType] || 'Unknown';
    },

    getStatusClass(status) {
      const statusMap = {
        '0': 'status-pending', // Pending - gray
        '1': 'status-won',     // Won - green
        '3': 'status-lost'     // Lost - orange
      };
      return statusMap[status] || 'status-default';
    },

    getStatusText(status) {
      const statusMap = {
        '0': 'Pending',
        '1': 'Won',
        '3': 'Lost',
        '7': 'Canceled'
      };
      return statusMap[status] || 'Unknown';
    },

    changeLimit(newLimit) {
      this.limit = newLimit;
      this.offset = 1;
      this.moreParams.limit = newLimit;
      this.moreParams.page = 1;
      this.setBets();
    },
    formatFilterName(key) {
      // Convert snake_case to Title Case with spaces
      return key.split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    },
  },
}
</script>

<style scoped>
/* Add these styles for bet type and status badges */
.bet-type-badge {
  display: inline-block;
  padding: 6px 12px;
  min-width: 90px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
}

.bet-type-cash {
  background-color: #3498DB; /* Blue */
}

.bet-type-bonus {
  background-color: #9b59b6; /* Purple */
}

.bet-type-free {
  background-color: #2ecc71; /* Green */
}

.bet-type-default {
  background-color: #95a5a6; /* Gray */
}

/* Status badges with updated colors */
.status-badge {
  display: inline-block;
  padding: 6px 12px;
  min-width: 90px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

.status-won {
  background-color: #2ecc71; /* Green for Won */
}

.status-lost {
  background-color: #f39c12; /* Orange for Lost */
}

.status-pending {
  background-color: #95a5a6; /* Gray for Pending */
}

.status-default {
  background-color: #7f8c8d; /* Darker Gray for default */
}

/* Add game badge styling */
.game-badge {
  display: inline-block;
  padding: 6px 12px;
  min-width: 90px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
}

/* Filter styles */
.filter-input {
  display: block;
  width: 100%;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.filter-select {
  width: 100%;
  display: block;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border: 1px solid #e2e8f0;
  background-color: white;
  border-radius: 0.375rem;
  outline: none;
  cursor: pointer;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.filter-select {
  width: 100%;
  display: block;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border: 1px solid #e2e8f0;
  background-color: white;
  border-radius: 0.375rem;
  outline: none;
}

/* Make date picker smaller */
:deep(.dp__main) {
  font-size: 0.75rem;
}

:deep(.dp__input) {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}
</style>
