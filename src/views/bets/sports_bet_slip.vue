<template>
  <div class="container mx-auto px-4 py-8">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
    
    <!-- Bet Details Header -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-gray-800">Bet Slip Details</h1>
        <button @click="goBack" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
          <i class="fas fa-arrow-left mr-2"></i> Back
        </button>
      </div>
      
      <!-- Bet Summary -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div class="bg-gray-50 p-4 rounded-md">
          <h3 class="text-sm font-medium text-gray-500 mb-1">Bet ID</h3>
          <p class="text-lg font-semibold">{{ betDetails.bet_id }}</p>
        </div>
        <div class="bg-gray-50 p-4 rounded-md">
          <h3 class="text-sm font-medium text-gray-500 mb-1">Stake</h3>
          <p class="text-lg font-semibold">{{ formatNumber(betDetails.bet_amount) }}</p>
        </div>
        <div class="bg-gray-50 p-4 rounded-md">
          <h3 class="text-sm font-medium text-gray-500 mb-1">Possible Win</h3>
          <p class="text-lg font-semibold">{{ formatNumber(betDetails.possible_win) }}</p>
        </div>
      </div>
      
      <div class="flex items-center">
        <h3 class="text-sm font-medium text-gray-500 mr-2">Status:</h3>
        <span class="status-badge" :class="getStatusClass(betDetails.bet_status)">
          {{ getStatusText(betDetails.bet_status) }} {{ betDetails.resulting_type }}
        </span>
      </div>
    </div>
    
    <!-- Bet Slip Items Table -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <auto-table
        :headers="tableHeaders"
        :data="bet_slips"
        :total-items="total"
        :items-per-page="limit"
        :current-page-prop="offset"
        :server-side-pagination="true"
        :pagination="total > limit"
        :show-items-count="true"
        @page-change="goToPage"
      >
        <!-- Slip ID Column -->
        <template #slip_id="{ item }">
          <div class="font-mono text-sm">{{ item.slip_id }}</div>
        </template>
        
        <!-- Teams Column -->
        <template #teams="{ item }">
          <div v-if="item.extra_data">
            <div class="font-medium">
              {{ getExtraDataValues(item).competitor1 }}
              <br>
              <span class="text-gray-500 mx-1">vs</span>
              <br>
              {{ getExtraDataValues(item).competitor2 }}
            </div>
          </div>
        </template>
        
        <!-- Tournament Column -->
        <template #tournament="{ item }">
          <div v-if="item.extra_data">
            <div class="text-sm">
              {{ getExtraDataValues(item).tournament }}
              <div class="text-xs text-gray-500">{{ getExtraDataValues(item).country }}</div>
            </div>
          </div>
        </template>
        
        <!-- Bet Type Column -->
        <template #bet_type="{ item }">
          <div class="text-center">
            <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
              {{ item.live_bet === '1' ? 'Live' : 'Pre-match' }}
            </span>
          </div>
        </template>
        
        <!-- Pick Column -->
        <template #pick="{ item }">
          <div>{{ item.pick }}</div>
          <div>{{ item.pick_name }}</div>
        </template>
      
        <!-- Odds Column -->
        <template #odd_value="{ item }">
          <div class="text-center font-medium">{{ formatNumber(item.odd_value) }}</div>
        </template>
        
        <!-- Winning Outcome Column -->
        <template #winning_outcome="{ item }">
          <div class="text-center">{{ item.winning_outcome || 'N/A' }}</div>
        </template>
        
        <!-- Scores Column -->
        <template #scores="{ item }">
          <div class="text-center">
            <span v-if="item.ft_scores" class="font-medium">{{ item.ft_scores }}</span>
            <span v-else class="text-gray-400">-</span>
          </div>
        </template>

        <template #outcome_name="{ item }">
        <div class="max-w-md">
          <p class="whitespace-pre-line text-sm message-text">{{ item.outcome_name }}</p>
        </div>
      </template>
        
        <!-- Status Column -->
        <template #bet_status="{ item }">
          <div class="text-center">
            <span class="status-badge" :class="getStatusClass(item.status)">
              {{ getStatusText(item.status) }}
            </span>
          </div>
        </template>
        
        <!-- Start Time Column -->
        <template #start_time="{ item }">
          <div class="text-sm text-gray-600">
            {{ formatDateTime(item.start_time) }}
          </div>
        </template>
      </auto-table>
    </div>
  </div>
</template>

<script>
import {mapActions} from "vuex";
import { AutoTable, CustomLoading } from '@/components/common';
import moment from 'moment-timezone';

export default {
  components: {
    AutoTable,
    CustomLoading,
  },
  data() {
    return {
      isLoading: false,
      betReference: '',
      betDetails: {}, // this.$store.state.betDetails,
      bet_slips: [
      
      ],
      total: 1,
      limit: 10,
      offset: 1,
      tableHeaders: [
        { key: "slip_id", label: "Slip ID", align: "center" },
        { key: "teams", label: "Teams", align: "center" },
        { key: "bet_type", label: "Bet Type", align: "center" },
        { key: "pick", label: "Pick", align: "center" },
        { key: "odd_value", label: "Odds", align: "center" },
        { key: "winning_outcome", label: "Winning Outcome", align: "center" },
        { key: "scores", label: "Scores", align: "center" },
        // { key: "outcome_name", label: "outcome", align: "center" },
        { key: "bet_status", label: "Status", align: "center" },
        // { key: "start_time", label: "Start Time", align: "center" }
      ],

     
    };
  },
  mounted() {
    
    this.betDetails = this.$store.state.betDetails;
    console.log("betDetails: " + JSON.stringify(this.betDetails));

    this.fetchBetSlipDetails();
  },
  methods: {
    ...mapActions(["getSportsBetSlips"]),
    
    goBack() {
      this.$router.go(-1);
    },
    
    goToPage(page) {
      this.offset = page;
      this.fetchBetSlipDetails();
    },

    async fetchBetSlipDetails() {
      this.isLoading = true;
      
      try {
        const params = {
          bet_id: this.betDetails.bet_id,
          page: this.offset.toString(),
          limit: this.limit.toString(),
          timestamp: Date.now().toString()
        };

        let queryString = Object.keys(params)
          .map(key => `${key}=${encodeURIComponent(params[key])}`)
          .join('&');

          console.log(queryString);
        
        const response = await this.getSportsBetSlips(queryString);
        
        if (response.status === 200 && response.message) {
          // Set bet details
          if (response.message.bet) {
            this.betDetails = response.message.bet;
            this.betReference = this.betDetails.bet_reference || '';
          }
          
          // Set bet slips
          this.bet_slips = response.message.result || [];
          this.total = parseInt(response.message.record_count || response.message.total_count || 0);
        } else {
          this.bet_slips = [];
          this.total = 0;
          this.$swal.fire('Error', 'Failed to load bet slip details', 'error');
        }
      } catch (error) {
        console.error('Error fetching bet details:', error);
        this.$swal.fire('Error', 'An unexpected error occurred', 'error');
      } finally {
        this.isLoading = false;
      }
    },
    
    formatDateTime(dateTime) {
      return moment(dateTime).format('MMM D, YYYY HH:mm');
    },
    
    getExtraDataValues(item) {
      try {
        if (!item.extra_data) return {};
        return typeof item.extra_data === 'string' 
          ? JSON.parse(item.extra_data) 
          : item.extra_data;
      } catch (e) {
        console.error("Error parsing extra_data:", e);
        return {};
      }
    },
    
    getStatusText(status) {
      const statusMap = {
        '0': 'Pending',
        '1': 'Won',
        '3': 'Lost',
        '7': 'Voided',
        '9': 'Canceled'
      };
      return statusMap[status] || 'Unknown';
    },
    
    getStatusClass(status) {
      const classMap = {
        '0': 'status-pending', // Pending - yellow
        '1': 'status-won',     // Won - green
        '3': 'status-lost',    // Lost - red
        '7': 'status-voided',  // Voided - gray
        '9': 'status-canceled' // Canceled - gray
      };
      return classMap[status] || 'status-default';
    },

    // 2 decimals
    formatNumber(number) {
      const num = parseFloat(number);  // Convert the string to a number
      return isNaN(num) ? '0.00' : num.toFixed(2);  // Return the formatted number or default to '0.00'
    },
  }
};
</script>

<style scoped>
.status-badge {
  display: inline-block;
  padding: 6px 12px;
  min-width: 90px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.status-won {
  background-color: #2ecc71; /* Green */
}

.status-lost {
  background-color: #e74c3c; /* Red */
}

.status-pending {
  background-color: #f39c12; /* Orange/Yellow */
}

.status-voided, .status-canceled {
  background-color: #95a5a6; /* Gray */
}

.status-default {
  background-color: #7f8c8d; /* Darker Gray */
}

.whitespace-pre-line {
  white-space: pre-line;
}
/* Message text */
.message-text {
  max-height: 60px;
  overflow-y: auto;
  padding: 8px;
  border-radius: 8px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

</style>
