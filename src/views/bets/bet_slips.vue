<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Bet" pageSubtitle="Slips" />

    <div class="block py-4 bg-white rounded-lg shadow-lg mx-3 border overflow-x-auto">
      <!-- Bet Slip Details -->
      <div class="container mx-auto px-4 py-6">
        <div class="bg-white rounded-lg overflow-hidden">
          <!-- Header -->
          <div class="flex justify-between items-center p-4 border-b border-gray-200">
            <div>
              <h1 class="text-xl font-bold text-gray-800">Bet Slip Details</h1>
              <p class="text-sm text-gray-600">Reference: {{ betReference }}</p>
            </div>
            <button 
              @click="goBack" 
              class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg flex items-center"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              Back
            </button>
          </div>

          <!-- Loading state -->
          <custom-loading :active="isLoading" :is-full-page="false" />

          <!-- Content -->
          <div v-if="!isLoading">
            <!-- Bet summary -->
            <div class="p-4 bg-gray-50 border-b border-gray-200">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="p-3 bg-white rounded-lg shadow-sm">
                  <p class="text-sm text-gray-500">Stake</p>
                  <p class="text-lg font-bold text-gray-800">{{ formatNumber(betDetails.bet_amount) }}</p>
                </div>
                <div class="p-3 bg-white rounded-lg shadow-sm">
                  <p class="text-sm text-gray-500">Possible Win</p>
                  <p class="text-lg font-bold text-indigo-600">{{ formatNumber(betDetails.possible_win) }}</p>
                </div>
                <div class="p-3 bg-white rounded-lg shadow-sm">
                  <p class="text-sm text-gray-500">Status</p>
                  <div class="mt-1">
                    <span 
                      class="px-3 py-1 text-xs font-medium rounded-full"
                      :class="getStatusClass(betDetails.bet_status)"
                    >
                      {{ getStatusText(betDetails.bet_status) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Bet slip items table -->
            <auto-table
              :headers="tableHeaders"
              :data="bet_slips"
              :total-items="total"
              :items-per-page="limit"
              :current-page-prop="offset"
              :server-side-pagination="true"
              :pagination="total > limit"
              :show-items-count="true"
              :no-styling="true"
              @page-change="gotToPage"
              @limit-change="changeLimit"
            >
              <!-- Slip ID Column -->
              <template #slip_id="{ item }">
                <div class="font-mono text-sm text-gray-700">{{ item.slip_id }}</div>
              </template>

              <!-- Teams Column -->
              <template #teams="{ item }">
                <div v-if="item.extra_data" class="min-w-[180px]">
                  <div class="flex flex-col space-y-1">
                    <span class="font-semibold text-gray-800">{{ getExtraDataValues(item).competitor1 }}</span>
                    <span class="text-xs text-gray-500 font-medium">vs</span>
                    <span class="font-semibold text-gray-800">{{ getExtraDataValues(item).competitor2 }}</span>
                  </div>
                </div>
              </template>

              <!-- Tournament Column -->
              <template #tournament="{ item }">
                <div v-if="item.extra_data" class="min-w-[150px]">
                  <span class="text-sm font-medium text-gray-700">{{ getExtraDataValues(item).tournament }}</span>
                  <br>
                  <small class="text-xs text-gray-500">{{ getExtraDataValues(item).country }}</small>
                </div>
              </template>

              <!-- Live Bet Column -->
              <template #bet_type="{ item }">
                <div class="text-center">
                  <span 
                    class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium"
                    :class="{
                      'bg-blue-100 text-blue-800': item.live_bet === '1',
                      'bg-gray-100 text-gray-800': item.live_bet === '0'
                    }">
                    <span v-if="item.live_bet === '1'" class="w-2 h-2 bg-blue-500 rounded-full mr-1.5 animate-pulse"></span>
                    {{ item.live_bet === "1" ? "Live" : "Pre-match" }}
                  </span>
                </div>
              </template>

              <!-- Pick Column -->
              <template #pick="{ item }">
                <div class="font-medium text-gray-800 min-w-[100px]">{{ item.pick }}</div>
              </template>

              <!-- Market Column -->
              <template #pick_name="{ item }">
                <div class="text-sm text-gray-700 min-w-[120px]">
                  {{ item.pick_name ? item.pick_name.split(';')[1] : "" }}
                </div>
              </template>

              <!-- Odds Column -->
              <template #odd_value="{ item }">
                <div class="font-bold text-indigo-600">{{ formatNumber(item.odd_value) }}</div>
              </template>

              <!-- Winning Outcome Column -->
              <template #winning_outcome="{ item }">
                <div class="min-w-[100px]">
                  <span v-if="item.winning_outcome" class="font-medium text-gray-800">
                    {{ item.winning_outcome }}
                  </span>
                  <span v-else class="text-sm text-gray-500 italic">
                    Not Resulted
                  </span>
                </div>
              </template>

              <!-- Scores Column -->
              <template #scores="{ item }">
                <div class="space-y-1 min-w-[100px]">
                  <div v-if="item.ht_scores" class="text-sm">
                    <span class="text-xs text-gray-500 mr-1">HT:</span>
                    <span class="font-medium">{{ item.ht_scores }}</span>
                  </div>
                  <div v-if="item.ft_scores" class="text-sm">
                    <span class="text-xs text-gray-500 mr-1">FT:</span>
                    <span class="font-medium">{{ item.ft_scores }}</span>
                  </div>
                  <div v-if="item.et_scores" class="text-sm">
                    <span class="text-xs text-gray-500 mr-1">ET:</span>
                    <span class="font-medium">{{ item.et_scores }}</span>
                  </div>
                  <div v-if="!item.ht_scores && !item.ft_scores && !item.et_scores" class="text-xs text-gray-500 italic">
                    No scores available
                  </div>
                </div>
              </template>

              <!-- Status Column -->
              <template #status="{ item }">
                <button
                  class="px-4 py-1 text-white rounded-sm text-xs font-medium"
                  :class="{
                    'bg-gray-500': item.bet_status === '0',
                    'bg-green-500': item.bet_status === '1',
                    'bg-orange-500': item.bet_status === '3',
                    'bg-blue-600': isMatchFinished(item) && item.bet_status !== '1' && item.bet_status !== '3'
                  }">
                  {{ getStatusDisplay(item) }}
                </button>
              </template>

              <!-- Start Time Column -->
              <template #start_time="{ item }">
                <div class="text-xs text-gray-600">
                  <div class="font-medium">{{ moment(item.start_time).format('DD MMM YYYY') }}</div>
                  <div>{{ moment(item.start_time).format('HH:mm') }}</div>
                  <div v-if="isMatchFinished(item)" class="text-xs mt-1 italic text-gray-500">
                    (Match completed)
                  </div>
                </div>
              </template>
            </auto-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import { mapActions } from "vuex";
import { endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths } from "date-fns";
import VueDatePicker from "@vuepic/vue-datepicker";
import { AutoTable, CustomLoading } from '@/components/common';

export default {
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      betId: null,
      betReference: '',
      betDetails: {},
      bet_slips: [],
      
      tableHeaders: [
        { key: "slip_id", label: "Slip ID", align: "left" },
        { key: "teams", label: "Teams", align: "left" },
        { key: "tournament", label: "Tournament", align: "left" },
        { key: "bet_type", label: "Bet Type", align: "center" },
        { key: "pick", label: "Pick", align: "left" },
        { key: "pick_name", label: "Market", align: "left" },
        { key: "odd_value", label: "Odds", align: "center" },
        { key: "winning_outcome", label: "Winning Outcome", align: "center" },
        { key: "scores", label: "Scores", align: "center" },
        { key: "bet_status", label: "Status", align: "center" },
        { key: "start_time", label: "Start Time", align: "center" }
      ],

      //
      moreParams: {
        bet_id: '',
        sport_id: '',
        status: '',
        start: '',
        end: '',
        page: '',
        limit: "10",
        timestamp: 'timestamp',
        skip_cache: '',
      },

      date: null,
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],
    }
  },
  components: {
    VueDatePicker,
    Loading,
    AutoTable,
    CustomLoading
  },
  mounted() {
    // get id from params
    this.betId = this.$route.params.id ?? "";
    this.moreParams.bet_id = this.betId;
    this.fetchBetDetails();
  },
  methods: {
    ...mapActions(["getBetSlips", "repostDeposit", "toggleSideMenu"]),
    
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    async fetchBetDetails() {
      this.isLoading = true;
      
      try {
        const params = {
          bet_id: this.betId,
          page: this.offset.toString(),
          limit: this.limit.toString(),
          timestamp: Date.now().toString()
        };

        let queryString = Object.keys(params)
          .map(key => `${key}=${encodeURIComponent(params[key])}`)
          .join('&');
        
        const response = await this.getBetSlips(queryString);
        
        if (response.status === 200 && response.message) {
          // Set bet details
          if (response.message.bet) {
            this.betDetails = response.message.bet;
            this.betReference = this.betDetails.bet_reference || '';
          }
          
          // Set bet slips
          this.bet_slips = response.message.result || [];
          this.total = parseInt(response.message.record_count || response.message.total_count || 0);
        } else {
          this.bet_slips = [];
          this.total = 0;
          this.$swal.fire('Error', 'Failed to load bet slip details', 'error');
        }
      } catch (error) {
        console.error('Error fetching bet details:', error);
        this.$swal.fire('Error', 'An unexpected error occurred', 'error');
      } finally {
        this.isLoading = false;
      }
    },
    
    gotToPage(page) {
      this.offset = page;
      this.fetchBetDetails();
    },
    
    changeLimit(limit) {
      this.limit = limit;
      this.offset = 1;
      this.fetchBetDetails();
    },
    
    formatNumber(number) {
      const num = parseFloat(number);
      return isNaN(num) ? '0.00' : num.toFixed(2);
    },
    
    getExtraDataValues(item) {
      try {
        if (!item.extra_data) return { competitor1: '', competitor2: '', tournament: '', country: '' };
        
        const extraData = typeof item.extra_data === 'string' 
          ? JSON.parse(item.extra_data) 
          : item.extra_data;
          
        return {
          competitor1: extraData.competitor1 || '',
          competitor2: extraData.competitor2 || '',
          tournament: extraData.tournament || '',
          country: extraData.country || ''
        };
      } catch (e) {
        return { competitor1: '', competitor2: '', tournament: '', country: '' };
      }
    },
    
    isMatchFinished(item) {
      if (!item.start_time) return false;
      
      const startTime = new Date(item.start_time);
      const currentTime = new Date();
      const hoursDifference = (currentTime - startTime) / (1000 * 60 * 60);
      
      return hoursDifference > 2.5;
    },
    
    getStatusDisplay(item) {
      if (this.isMatchFinished(item) && item.bet_status !== '1' && item.bet_status !== '3') {
        return "Finished";
      }
      return this.getStatusText(item.bet_status);
    },
    
    getStatusText(status) {
      const statusMap = {
        '0': 'Pending',
        '1': 'Won',
        '3': 'Lost'
      };
      return statusMap[status] || 'Unknown';
    },
    
    getStatusClass(status) {
      const classMap = {
        '0': 'bg-gray-100 text-gray-800',
        '1': 'bg-green-100 text-green-800',
        '3': 'bg-orange-100 text-orange-800'
      };
      return classMap[status] || 'bg-gray-100 text-gray-800';
    },
    
    async selectDate() {
      let vm = this
      vm.isLoading = true;
      vm.moreParams.start = vm.formatDate(this.date[0])
      vm.moreParams.end = vm.formatDate(this.date[1])
      vm.moreParams.timestamp = Date.now()

      await vm.fetchBetDetails()
    },

    formatDate(date) {
      var d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },
    
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },
    
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    }
  }
};
</script>

<style scoped>
@keyframes pulse {
  0% { opacity: 0.5; transform: scale(0.95); }
  50% { opacity: 1; transform: scale(1.05); }
  100% { opacity: 0.5; transform: scale(0.95); }
}

.animate-pulse {
  animation: pulse 1.5s infinite;
}
</style>
