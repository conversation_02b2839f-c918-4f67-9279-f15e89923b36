<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Casino" pageSubtitle="Betsd" />

    <div class="px-4 mb-5 grid grid-cols-1 gap-2 w-full sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 ">
      <div class="block">
        <label class="block text-xs text-gray-700">Customer Phone</label>
        <div class="flex gap-2">
          <input type="number" placeholder="Phone number"
                 class="block w-full px-3 py-2 text-sm text-gray-700 border rounded-md shadow-sm"
                 v-model="moreParams.mobile_number">
          <button class="px-3 py-2 mt-2 bg-indigo-500 text-white rounded-md " @click="applyFilters()">Apply</button>
        </div>
      </div>
    </div>

    <!--  Checkboxes -->
    <div class="px-4 mb-3">
      <label class="block text-sm text-gray-700">Filter by:</label>
      <div class="grid grid-cols-10 gap-4 ">
        <div class="block">
          <label>
            <input type="checkbox" v-model="checkboxes.bet_amount"> <span class="text-xs"> Bet Amount </span>
          </label>
        </div>
        <div class="block">
          <label>
            <input type="checkbox" v-model="checkboxes.winning_amount"> <span class="text-xs"> Possible Win </span>
          </label>
        </div>
        <div class="block">
          <label>
            <input type="checkbox" v-model="checkboxes.odds"> <span class="text-xs"> Odds </span>
          </label>
        </div>
        <div class="block">
          <label>
            <input type="checkbox" v-model="checkboxes.selections"> <span class="text-xs"> Selections </span>
          </label>
        </div>
        <!-- Add more checkboxes as needed -->
      </div>
    </div>

    <div class="px-4 pb-2 grid grid-cols-1 gap-2 w-full sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 ">
      <div v-if="checkboxes.bet_amount" class="grid grid-cols-1 gap-4 p-2 border rounded-md border-gray-400">
        <div class="block">
          <label class="block text-xs text-gray-700">Bet Amount</label>
          <div class="flex gap-2">
            <input type="number" placeholder="Min"
                   class="block w-full px-3 py-2 text-sm text-gray-700 border rounded-md shadow-sm"
                   v-model="moreParams.stake_amount_min">
            <input type="number" placeholder="Max"
                   class="block w-full px-3 py-2 text-sm text-gray-700 border rounded-md shadow-sm"
                   v-model="moreParams.stake_amount_max">
            <button class="px-3 py-2 mt-2 bg-indigo-500 text-white rounded-md " @click="applyFilters()">Apply</button>
          </div>
        </div>
      </div>

      <div v-if="checkboxes.winning_amount" class="grid grid-cols-1 gap-4 p-2 border rounded-md border-gray-400">
        <div class="block">
          <label class="block text-xs text-gray-700">Winning Amount</label>
          <div class="flex gap-2">
            <input type="number" placeholder="Min"
                   class="block w-full px-3 py-2 text-sm text-gray-700 border rounded-md shadow-sm"
                   v-model="moreParams.winning_amount_min">
            <input type="number" placeholder="Max"
                   class="block w-full px-3 py-2 text-sm text-gray-700 border rounded-md shadow-sm"
                   v-model="moreParams.winning_amount_max">
            <button class="px-3 py-2 mt-2 bg-indigo-500 text-white rounded-md " @click="applyFilters()">Apply</button>
          </div>
        </div>
      </div>

      <div v-if="checkboxes.odds" class="grid grid-cols-1 gap-4 p-2 border rounded-md border-gray-400">
        <div class="block">
          <label class="block text-xs text-gray-700">Odds</label>
          <div class="flex gap-2">
            <input type="number" placeholder="Min"
                   class="block w-full px-3 py-2 text-sm text-gray-700 border rounded-md shadow-sm"
                   v-model="moreParams.odds_min">
            <input type="number" placeholder="Max"
                   class="block w-full px-3 py-2 text-sm text-gray-700 border rounded-md shadow-sm"
                   v-model="moreParams.odds_max">
            <button class="px-3 py-2 mt-2 bg-indigo-500 text-white rounded-md " @click="applyFilters()">Apply</button>
          </div>
        </div>
      </div>

      <div v-if="checkboxes.selections" class="grid grid-cols-1 gap-4 p-2 border rounded-md border-gray-400">
        <div class="block">
          <label class="block text-xs text-gray-700">Selections</label>
          <div class="flex gap-2">
            <input type="number" placeholder="Min"
                   class="block w-full px-3 py-2 text-sm text-gray-700 border rounded-md shadow-sm"
                   v-model="moreParams.selections_min">
            <input type="number" placeholder="Max"
                   class="block w-full px-3 py-2 text-sm text-gray-700 border rounded-md shadow-sm"
                   v-model="moreParams.selections_max">
            <button class="px-3 py-2 mt-2 bg-indigo-500 text-white rounded-md " @click="applyFilters()">Apply</button>
          </div>
        </div>
      </div>
    </div>

    <div class="px-4 pb-2 grid grid-cols-1 gap-2 w-full sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 ">
      <div class="block">
        <label class="text-xs mb-1 block ">Bets Placed On</label>
        <VueDatePicker v-model="date" range :preset-ranges="presetRanges" position="center" :clearable="false"
                       :enable-time-picker="false" @closed="selectDate">
          <template #yearly="{ label, range, presetDateRange }">
            <span @click="presetDateRange(range)">{{ label }}</span>
          </template>
        </VueDatePicker>
      </div>

      <div class="block">
        <label class="text-xs mb-1 block ">Bet Placement Type</label>
        <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="selected_placement_type">
          <option value="" disabled selected>Select Type</option>
          <option v-for="type in bet_placement_types" :value="type.value">
            {{ type.text }}
          </option>
        </select>
      </div>

      <div class="block">
        <label class="text-xs mb-1 block ">Selection Type</label>
        <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="selected_selection_type">
          <option value="" disabled selected>Select Selection Type</option>
          <option v-for="type in selection_types" :value="type">
            {{ type.text }}
          </option>
        </select>
      </div>

      <div class="block">
        <label class="text-xs mb-1 block ">Bet Status</label>
        <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="selected_bet_status">
          <option value="" disabled selected>Select Bet Status</option>
          <option v-for="type in bet_statuses" :value="type.value">
            {{ type.text }}
          </option>
        </select>
      </div>

<!--      <div class="block">
        <label class="text-xs mb-1 block ">Game Type</label>
        <select class="block w-full px-3 py-2 text-sm text-gray-700 border rounded-md shadow-sm"
                v-model="selected_game_type">
          <option v-for="(item,index) in game_types" :key="index" :value="item">{{ item.name }}</option>
        </select>
      </div>-->
    </div>

    <div class="block py-4 bg-white rounded-lg shadow-lg mx-3 border overflow-x-auto">
      <table class="w-full mb-12 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2 pl-2">Bet ID</th>
          <th class="py-2 ">Bet Reference</th>
          <th class="py-2 pr-3 ">Stake</th>
          <th class="py-2">Bet Type</th>
          <th class="py-2">Total Games</th>
          <th class="py-2 pr-3 ">Total Odd</th>
          <th class="py-2 pr-3 ">Possible</th>
          <th class="py-2 pr-2">Risk State</th>
          <th class="py-2 pr-2">Processed</th>
          <th class="py-2 pr-2">Status</th>
          <th class="py-2 pr-2">Date</th>
          <th class="py-2 pr-2 text-center">Actions</th>
        </tr>
        </thead>
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(item,index) in bets" :key="item.id">

          <td class="py-2 pl-2 pr-2">{{ (item.bet_id) }}</td>

          <td class="py-2 pr-2 ">
            <span>{{ item.bet_reference }}</span>
            <br>
            <span style="font-size: 11px;"> {{ item.bet_attribution }} </span>
          </td>

          <td class="py-2 w-24">
            {{ formatNumber(item.bet_amount) }}
          </td>

          <td class="py-2 w-24 pr-2">
            <button
                class="px-4 py-1 text-white rounded-sm"
                :class="{
                      'bg-green-500': item.bet_type === '0',
                      'bg-blue-500': item.bet_type === '1',
                      'bg-cyan-500': item.bet_type === '2'
                    }">
              {{ betTypeText(item.bet_type) }}
            </button>
          </td>

          <td class="py-2 pr-3 text-center"> {{ item.total_games }} </td>

          <td class="py-2 pr-3 text-center"> {{ formatNumber(item.total_odd) }} </td>

          <td class="py-2 w-24">
            <strong> {{ formatNumber(item.possible_win) }} </strong>
            <br>
            <span style="font-size: 11px;">W.Tax:</span> {{ formatNumber(item.witholding_tax) }}
          </td>

          <td class="py-2  pr-3">
            <button
                class="px-4 py-1 text-white rounded-sm"
                :class="{
                      'bg-red-500': item.risk_state === '0',
                      'bg-blue-500': item.risk_state === '1',
                    }">
              {{ item.risk_state === "0" ? "Not Report" : "Reported" }}
            </button>
          </td>

<!--          <td class="py-2 w-24 pr-3"> {{ item.processed }}</td>-->
          <td class="py-2 w-24 pr-3 text-center">
            <button
                class="px-4 py-1 text-white rounded-sm"
                :class="{
                      'bg-neutral-500': item.processed === '0',
                      'bg-green-500': item.processed === '1',
                      'bg-red-500': item.processed === '3',
                      'bg-gray-500': item.processed === '7',
                      'bg-yellow-500': item.processed === '9',
                    }">
              {{ processedText(item.processed) }}
            </button>
          </td>

          <td class="py-2 w-24 pr-3 text-center">
            <button
                class="px-4 py-1 text-white rounded-sm"
                :class="{
                      'bg-neutral-500': item.status === '0',
                      'bg-green-500': item.status === '1',
                      'bg-red-500': item.status === '3',
                    }">
              {{ processedText(item.status) }}
            </button>
          </td>

          <td class="py-2 w-24 pr-3">
            <span style="font-size: 11px; color: grey">{{ moment(item.created).format('llll') }}</span>
          </td>

          <td class="py-2 text-center w-24">
            <div class="relative inline-block">
              <button
                  v-if="(parseInt(item.match_status) === 0) || (parseInt(item.match_status) === null)"
                  class="px-3 py-1 flex items-center space-x-1">

                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#808080" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <button v-else class="px-3 py-1 flex items-center space-x-1" @click="toggleDropdown(index)">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#000000" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <div v-if="showDropdown[index]" class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10"
                   style="width: 230px; text-align: left;">
                <ul class="py-2">

                  <li @click="setBetSlips(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-indigo-200">
                      Show Bet Slip
                    </a>
                  </li>


                </ul>
              </div>

            </div>
          </td>

        </tr>
        </tbody>

      </table>

      <!-- Pagination -->
      <div class="flex-grow text-center sm:text-right" v-show="total > limit">
        <div class="inline-block bg-white border rounded-md divide-x text-xs sm:text-sm">
          <button class="p-2 sm:p-3 px-3 sm:px-5" v-if="offset > 1" @click="gotToPage(offset - 1)">&larr;</button>
          <button class="p-2 sm:p-3 px-3 sm:px-5" :class="{'bg-gray-100': offset === 1}" @click="gotToPage(1)">1
          </button>
          <button class="p-2 sm:p-3 px-3 sm:px-5" :class="{'bg-gray-100': offset === 2}"
                  v-show="Math.ceil(total / limit) > 1"
                  @click="gotToPage(2)">2
          </button>
          <button class="p-2 sm:p-3 px-3 sm:px-5" v-show="Math.ceil(total / limit) > 3">...</button>
          <button class="p-2 sm:p-3 px-3 sm:px-5" :class="{'bg-gray-100': offset === offset}"
                  v-show="Math.ceil(total / limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
          </button>
          <button class="p-2 sm:p-3 px-3 sm:px-5" :class="{'bg-gray-100': offset === Math.ceil(total / limit)}"
                  v-show="Math.ceil(total / limit) > 4" @click="gotToPage(Math.ceil(total / limit))">
            {{ Math.ceil(total / limit) }}
          </button>
          <button class="p-2 sm:p-3 px-3 sm:px-5" v-show="(offset * limit) < total" @click="gotToPage(offset + 1)">
            &rarr;
          </button>
        </div>
      </div>

    </div>

    <!-- Modal Wrapper -->
    <div v-if="isModalOpen" class="fixed inset-0 bg-gray-500 bg-opacity-50 z-50 flex items-center justify-center">
      <!-- Modal Content -->
      <div class="bg-white rounded-lg shadow-lg overflow-hidden w-full max-w-4xl p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-bold">Bet Slip Details {{ selectBet.bet_reference }}</h3>
          <button @click="isModalOpen=false" class="text-gray-500 hover:text-gray-700">&times;</button>
        </div>

        <!-- Table Inside Modal -->
        <table class="w-full mb-12 table">
          <thead class="border-b-2 text-xs text-left">
          <tr class="table-row">
            <th class="py-2">ID</th>
            <th class="py-2">Teams</th>
            <th class="py-2">Bet Type</th>
            <th class="py-2 ">Pick</th>
            <th class="py-2 pr-2">Winning Outcome</th>
            <th class="py-2">Scores</th>
            <th class="py-2 ">Status</th>
            <th class="py-2">Start Time</th>
          </tr>
          </thead>
          <tbody class="text-xs text-gray-600 divide-y">
          <tr v-for="(item, index) in bet_slips" :key="item.bet_id">

            <td class="py-2 w-4 pr-3">
                <span>{{ item.slip_id }}</span>
            </td>

            <td class="py-2 w-80 pr-3">
              <div v-if="item.extra_data">
                <strong>{{ getExtraDataValues(item).competitor1 }}</strong>
              vs
                <strong> {{ getExtraDataValues(item).competitor2 }}</strong>
              </div>
            </td>

            <td class="py-2 w-24 pr-3 ">
              <button
                  class="px-4 py-1 text-white rounded-sm"
                  :class="{
                      'bg-blue-500': item.live_bet === '1',
                      'bg-neutral-500': item.live_bet == '0'
                    }">
                {{ item.live_bet === "1" ? "Yes" : "No" }}
              </button>
            </td>

            <td class="py-2 w-24 pr-3">
              <strong>
                {{ item.pick }}
              </strong>
            </td>

            <td class="py-2 w-36 pr-3">
              <span>{{ item.winning_outcome ? item.winning_outcome : "Not Resulted" }}</span>
            </td>

            <td class="py-2 w-24 pr-3">
              HT: {{ item.ht_scores ? item.ht_scores : " - : -" }}
              <br>
              FT: {{ item.ft_scores ? item.ft_scores : " - : -" }}
              <br>
              ET: {{ item.et_scores ? item.et_scores : " - : -" }}
            </td>

            <td class="py-2 w-40 pr-3">
              <button
                  class="px-4 py-1 text-white rounded-sm"
                  :class="{
                      'bg-red-500': item.status === '0',
                      'bg-green-500': item.status === '1',
                      'bg-yellow-500': item.status !== '0' && item.status !== '1'
                    }">
                {{ statusText(item.status) }}
              </button>
            </td>

            <!--            <td class="py-2 w-24 pr-3">
                          <div v-if="item.extra_data">
                            <span>Odd ID: {{ getExtraDataValues(item).odd_id }}</span>
                          </div>
                        </td>-->

            <td class="py-2 w-24 pr-3">
              <span style="font-size: 11px; color: grey">{{ moment(item.start_time).format('llll') }}</span>
            </td>

          </tr>
          </tbody>
        </table>

        <!-- Pagination -->
        <div class="flex-grow text-center sm:text-right" v-show="total > limit">
          <div class="inline-block bg-white border rounded-md divide-x text-xs sm:text-sm">
            <button class="p-2 sm:p-3 px-3 sm:px-5" v-if="offset > 1" @click="gotToPage(offset - 1)">&larr;</button>
            <button class="p-2 sm:p-3 px-3 sm:px-5" :class="{'bg-gray-100': offset === 1}" @click="gotToPage(1)">1
            </button>
            <button class="p-2 sm:p-3 px-3 sm:px-5" :class="{'bg-gray-100': offset === 2}"
                    v-show="Math.ceil(total / limit) > 1"
                    @click="gotToPage(2)">2
            </button>
            <button class="p-2 sm:p-3 px-3 sm:px-5" v-show="Math.ceil(total / limit) > 3">...</button>
            <button class="p-2 sm:p-3 px-3 sm:px-5" :class="{'bg-gray-100': offset === offset}"
                    v-show="Math.ceil(total / limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
            </button>
            <button class="p-2 sm:p-3 px-3 sm:px-5" :class="{'bg-gray-100': offset === Math.ceil(total / limit)}"
                    v-show="Math.ceil(total / limit) > 4" @click="gotToPage(Math.ceil(total / limit))">
              {{ Math.ceil(total / limit) }}
            </button>
            <button class="p-2 sm:p-3 px-3 sm:px-5" v-show="(offset * limit) < total" @click="gotToPage(offset + 1)">
              &rarr;
            </button>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script>

import moment from "moment";
// import numeral from "numeral"
import {mapActions} from "vuex";
import {endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths} from "date-fns";
import VueDatePicker from "@vuepic/vue-datepicker";
import { AutoTable, ActionDropdown, ActionItem, CustomLoading } from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
    VueDatePicker,
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      checkboxes: {
        winning_amount: false,
        odds: false,
        selections: false,
        bet_amount: false,
      },
      showDropdown: [],
      viewModelOpen: false,
      //
      isModalOpen: false, // This will control modal visibility
      bet_slips: [], // Your data
      modal_total: 0,
      modal_limit: 10,
      modal_offset: 1,

      //
      selectBet: {},
      bet_slip_params: {
        bet_id: '',
        sport_id: '',
        status: '',
        start: '',
        end: '',
        page: '',
        limit: "10",
        timestamp: 'timestamp',
        skip_cache: '',
      },

      //
      bets: [],
      //
      moreParams: {
        game_type: 'sports',
        stake_amount_min: '',
        stake_amount_max: '',
        winning_amount_min: '',
        winning_amount_max: '',
        odds_min: '',
        odds_max: '',
        selections_min: '',
        selections_max: '',
        bet_type: '',
        selection_type: '',
        profile_id: '',
        mobile_number: '',
        status: '',
        sort: '',
        start: '',
        end: '',
        page: '',
        limit: "10",
        timestamp: 'timestamp',
        skip_cache: '',
      },

      selected_game_type: {},
      game_types: [
        {name: 'sports', value: 'sports'},
        {name: 'Virtual', value: 'virtual'},
        {name: 'Instant', value: 'instant'},
        {name: 'Casino', value: 'casino'},
      ],

      selected_selection_type: {},
      selection_types: [
        {text: 'Single Bet', value: 1},
        {text: 'Multi Bet', value: 2},
      ],

      selected_placement_type: {},
      bet_placement_types: [
        {text: 'All', value: ''},
        {text: 'Cash Bet', value: 0},
        {text: 'Bonus Bet', value: 1},
        {text: 'Free Bet', value: 2},
      ],

      selected_is_live: {},
      is_live_types: [
        {text: 'PreMatch', value: 1},
        {text: 'Live', value: 2},
      ],

      selected_bet_status: {},
      bet_statuses: [
        {text: 'Active', value: 1},
        {text: 'Won ', value: 2},
        {text: 'Lost ', value: 3},
        {text: 'Rejected ', value: 4},
        {text: 'Returned ', value: 5},
      ],

      date: null,
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],

    }
  },
  watch: {
    // watch if moreParams.bet_type changes
    selected_game_type(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.moreParams.game_type = newVal.value
        this.setBets()
      }
    },

    // selected_selection_type
    selected_selection_type(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.moreParams.selection_type = newVal
        this.setBets()
      }
    },

    // selected_placement_type
    selected_placement_type(newVal, oldVal) {
      console.log("selected_placement_type", newVal, oldVal)
      if (newVal !== oldVal) {
        this.moreParams.bet_type = newVal.value
        this.setBets()
      }
    },

  },
  mounted() {
    this.setBets()
  },
  methods: {
    ...mapActions(["getSportsBets", "getBetSlips", "repostDeposit", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    async selectDate() {
      let vm = this

      // If date is null (cleared), reset date filters and fetch data
      if (!this.date) {
        console.log('Date filter cleared, resetting and fetching data...');
        vm.moreParams.start = '';
        vm.moreParams.end = '';
        vm.moreParams.timestamp = Date.now();
        await vm.setBets();
        return;
      }

      // If date range is incomplete, return without doing anything
      if (!this.date[0] || !this.date[1]) return;

      // Update date filter values
      vm.moreParams.start = vm.formatDate(this.date[0]);
      vm.moreParams.end = vm.formatDate(this.date[1]);
      vm.moreParams.timestamp = Date.now();

      // Log that date filter was updated and fetch data
      console.log('Date filter updated, fetching data with new date range...');
      await vm.setBets();
    },

    formatDate(date) {
      var d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    // Pagination and dropdowns
    gotToPage(page) {
      let vm = this
      vm.moreParams.page = page
      vm.offset = page
      vm.setBets()
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    toggleDetails(data) {
      this.viewModelOpen = true;
      this.deposit = data;
    },

    async viewBetSlip(data) {
      let app = this
      app.closeDropdown()
      // route to bet slip
      // console.log("afvsdf", data.bet_id)
      app.$router.push({name: 'bet-slip', params: {id: data.bet_id}});
    },

    applyFilters(filters) {
      this.moreParams.page = ''
      this.moreParams.offset = ''
      this.setBets()
    },

    async setBets() {
      let app = this
      app.isLoading = true

      let response = await this.getSportsBets(app.moreParams)

      // console.log("Bets OK: " + JSON.stringify(response))
      app.bets = []
      app.total = 0
      if (response.status === 200) {
        app.bets = response.message.result

        if (response.message.record_count !== 0) {
          // app.total = parseInt(app.sports_book[0].trx_count)
          app.total = response.message.record_count
        }

        app.showDropdown = []
        for (let i = 0; i < app.bets.length; i++) {
          app.showDropdown.push(false)
        }
      } else {
        app.bets = [];
        app.total = 0;
      }
      app.isLoading = false
    },

    async setBetSlips(bet) {
      let app = this
      app.isLoading = true

      const params = new URLSearchParams();
      app.bet_slip_params.bet_id = bet.bet_id
      app.selectBet = bet

      for (const key in app.bet_slip_params) {
        if (app.bet_slip_params.hasOwnProperty(key)) {
          params.append(key, app.bet_slip_params[key]);
        }
      }

      const queryString = params.toString();
      // console.log("Params: " + queryString);

      let response = await this.getBetSlips(queryString)

      // console.log("bet_slips OK: " + JSON.stringify(response))
      if (response.status === 200) {
        app.bet_slips = response.message.result
        app.total = parseInt(response.message.record_count)

        app.showDropdown = []
        for (let i = 0; i < app.bet_slips.length; i++) {
          app.showDropdown.push(false)
        }
        app.isModalOpen = true
      } else {

      }
      app.isLoading = false
    },

    // Format currency with commas and put negative figures in brackets (standardized from master)
    formatCurrency(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0.00';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

    // Format numbers with commas (2 decimals for amounts) and put negative figures in brackets
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0.00';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

    statusClass(status) {
      // Apply color based on the status
      if (status === "0") return "bg-red-500";
      if (status === "1") return "bg-green-500";
      return "bg-yellow-500"; // For other statuses
    },
    statusText(status) {
      if (status === "0") return "Not Started";
      if (status === "1") return "In Progress";
      return "Finished"; // For other statuses
    },
    betTypeText(status) {
      if (status === "0") return "Cash Bet";
      if (status === "1") return "Bonus Bet";
      if (status === "2") return "Free Bet";
    },
    processedText(status) {
      if (status === "0") return "PENDING";
      if (status === "1") return "WON";
      if (status === "3") return "LOST";
      if (status === "7") return "VOIDED";
      if (status === "9") return "CANCELED";
    },
    // Method to parse extra_data and return as an object
    parseExtraData(extraData) {
      try {
        return JSON.parse(extraData);  // Parse the extra_data JSON string into an object
      } catch (error) {
        console.error('Error parsing extra_data:', error);
        return {};  // Return an empty object if parsing fails
      }
    },
    // Method to extract and return specific values from parsed extra_data
    getExtraDataValues(item) {
      const parsedData = this.parseExtraData(item.extra_data);
      return parsedData; // You can return specific properties as needed, e.g., parsedData.competitor1, parsedData.odd_id
    }

    //
  },
}
</script>
