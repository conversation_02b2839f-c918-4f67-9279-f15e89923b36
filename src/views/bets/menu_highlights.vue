<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 p-3 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
    <page-header pageName="Menu" pageSubtitle="Highlights" />

    <!-- Menu Highlights Table using AutoTable component -->
    <auto-table
      :headers="tableHeaders"
      :data="dataList"
      :has-actions="true"
      :get-actions="getRowActions"
      :total-items="total"
      :items-per-page="limit"
      :current-page-prop="offset"
      :server-side-pagination="true"
      :pagination="total > limit"
      :show-items-count="true"
      :items-per-page-options="[10, 25, 50, 100]"
      @page-change="handlePageChange"
      @items-per-page-change="handleLimitChange"
    >

      <!-- Index Column -->
      <template #index="{ index }">
        <div class="text-center">{{ index + 1 + ((offset - 1) * limit) }}</div>
      </template>

      <!-- Bet Type Column -->
      <template #bet_type="{ item }">
        <div class="bet-type-badge w-full" :class="getBetTypeClass(item.bet_type)">
          {{ getBetTypeText(item.bet_type) }}
        </div>
      </template>

      <!-- Provider Column -->
      <template #bet_name="{ item }">
        <div class="text-center provider-badge w-full"
             :class="getProviderClass(item.bet_name)"
             :title="item.bet_name">
          {{ item.bet_name }}
        </div>
      </template>

      <!-- Status Column -->
      <template #status="{ item }">
        <div class="status-badge w-full text-center" :class="getStatusClass(item.status)">
          {{ getStatusText(item.status) }}
        </div>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item, index }">
        <action-dropdown button-text="Actions" :show-text="false">
          <!-- Add Edit Option -->
          <action-item
            text="Edit"
            color="blue"
            @click="openEditModal(item)"
          >
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </template>
          </action-item>
          
          <!-- Status Toggle Option -->
          <action-item
            v-if="item.status === '0'"
            text="Activate"
            color="green"
            @click="activateDeActivate(item, 1)"
          >
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-green-500 group-hover:text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </template>
          </action-item>

          <action-item
            v-if="item.status === '1'"
            text="Deactivate"
            color="red"
            @click="activateDeActivate(item, 0)"
          >
            <template #icon>
              <svg class="mr-3 h-5 w-5 text-red-500 group-hover:text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
              </svg>
            </template>
          </action-item>
        </action-dropdown>
      </template>
    </auto-table>

  </div>

  <!-- Edit Modal -->
  <div v-if="showEditModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl mx-auto">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium">Edit Menu Highlight</h3>
        <button @click="closeEditModal" class="text-gray-500 hover:text-gray-700">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">

        <div class="block">
          <label class="text-xs font-bold text-black block">Name</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" 
                 type="text" v-model="editForm.name">
        </div>
        
        <div class="block">
          <label class="text-xs font-bold text-black block">Description</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" 
                 type="text" v-model="editForm.page_desc">
        </div>
        
        <div class="block">
          <label class="text-xs font-bold text-black block">URL</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" 
                 type="text" v-model="editForm.page_url">
        </div>

        <div class="block">
          <label class="text-xs font-bold text-black block">Priority</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" 
                 type="number" v-model="editForm.priority" min="1">
        </div>

        </div>
        <div class="grid grid-cols-1 md:grid-cols-1 gap-4 mb-4">
        <div class="block">
          <label class="text-xs font-bold text-black block">Icon URL</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" 
                 type="text" v-model="editForm.icon_url">
        </div>
      </div>
      
      <div class="flex justify-end space-x-3">
        <button @click="closeEditModal" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
          Cancel
        </button>
        <button @click="updateMenuHighlightDetails" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
          Update
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import {mapActions} from "vuex";
import moment from "moment-timezone";
import { AutoTable, ActionDropdown, ActionItem, CustomLoading } from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],
      showEditModal: false,
      editForm: {
        id: null,
        name: null,
        priority: null,
        page_desc: "",
        page_url: "",
        icon_url: "",
      },
      moreParams: {
        status: "",
        start: "",
        end: "",
        page: 1,
        limit: 10,
        timestamp: Date.now(),
        skip_cache: false,
        sort:"DESC"
      },
      dataList: [],
      tableHeaders: [
        { key: "index", label: "#", align: "center" },
        { key: "name", label: "Name", align: "center" },
        { key: "page_desc", label: "Description", align: "center" },
        { key: "page_url", label: "URL", align: "center" },
        { key: "icon_url", label: "Icon", align: "center" },
        { key: "priority", label: "Priority", align: "center" },
        { key: "status", label: "Status", align: "center" }
      ],
      rowHighlightConditions: {
        '0': 'bg-red-50',  // Inactive rows with light red background
        '1': '',          // Active rows with default background
        '2': 'bg-yellow-50' // Pending rows with light yellow background
      },
      statuses: [
        {text: 'Active', value: 1},
        {text: 'Pending Approval', value: 2},
        {text: 'Suspended', value: 3},
      ],
    }

  },
  async mounted() {
    await this.setMenuHighlights()
  },

  methods: {
    ...mapActions(["getMenuHighlights","updateMenuHighlight", "fillPayBill", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    // Get row actions for AutoTable
    getRowActions(item) {
      const actions = [
        {
          label: 'Edit',
          action: () => this.openEditModal(item),
          icon: 'fas fa-edit'
        }
      ];

      if (item.status === '0') {
        actions.push({
          label: 'Activate',
          action: () => this.activateDeActivate(item, 1),
          icon: 'fas fa-check-circle'
        });
      }

      if (item.status === '1') {
        actions.push({
          label: 'Deactivate',
          action: () => this.activateDeActivate(item, 0),
          icon: 'fas fa-times-circle'
        });
      }

      return actions;
    },

    // Handle items per page change
    handleLimitChange(newLimit) {
      this.limit = newLimit;
      this.moreParams.limit = newLimit.toString();
      this.offset = 1;
      this.moreParams.page = '1';
      this.setMenuHighlights();
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    //
    async setMenuHighlights() {
      let app = this
      app.isLoading = true

      let response = await this.getMenuHighlights(app.moreParams)
      if (response.status === 200) {
        app.dataList = response.message.result

        // Set total count for pagination
        if (response.message.record_count) {
          app.total = parseInt(response.message.record_count)
        } else if (response.message.total_count) {
          app.total = parseInt(response.message.total_count)
        } else {
          app.total = app.dataList.length
        }
        // Initialize dropdown state array
        app.showDropdown = Array(app.dataList.length).fill(false)
        for (let i = 0; i < app.dataList.length; i++) {
          app.showDropdown.push(false)
        }
      }

      app.isLoading = false

    },


    async activateDeActivate(item, status) {
      let app = this;

      const payload = {
        "menu_id": item.id.toString(),
        "timestamp": Date.now(),
        "status": status.toString(),
      }

      const statusText = status === 1 ? 'activate' : 'deactivate';
      const statusAction = status === 1 ? 'Activation' : 'Deactivation';
      const newStatusText = status === 1 ? 'Active' : 'Inactive';

      app.$swal.fire({
        title: `${statusAction} Confirmation`,
        text: `Are you sure you want to ${statusText} this menu highlight for ${item.name}? Status will be changed to "${newStatusText}".`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          app.isLoading = true;
          return await this.updateMenuHighlight(payload);
        },
      })
      .then(async (result) => {
        app.isLoading = false;

        if (result.value && result.value.status === 200) {
          app.$swal.fire({
            title: 'Updated!',
            text: result.value.message,
            icon: 'success'
          });
          // Refresh the data after successful update
          await app.setMenuHighlights();
        } else if (result.value) {
          app.$swal.fire('Error!', result.value.message, 'error');
        }

        // Close all dropdowns
        app.closeDropdown();
      });
    },

    //
    toggleDetails(item) {
      console.log("paybills item: ", item)
      // this.$router.push({name: 'paybills-view', params: {id: item.id}})
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    handlePageChange(page) {
      this.offset = page;
      this.moreParams.page = page;
      this.moreParams.limit = this.limit;
      this.setMenuHighlights();
    },

    getProviderClass(providerName) {
      // Map provider names to CSS classes
      const classMap = {
        'GENIUS': 'provider-genius',
        'BETRADAR': 'provider-betradar',
        'BETGAMES': 'provider-betgames',
        'EVOLUTION': 'provider-evolution',
        'PRAGMATIC': 'provider-pragmatic',
        'SPORTY': 'provider-sporty',
        'KIRON': 'provider-kiron',
        'EZUGI': 'provider-ezugi',
        'GOLDENRACE': 'provider-goldenrace',
        'PLAYTECH': 'provider-playtech',
        'MICROGAMING': 'provider-microgaming',
        'NETENT': 'provider-netent',
        'QUICKSPIN': 'provider-quickspin',
        'SPINOMENAL': 'provider-spinomenal',
      };

      // Return the class name or default
      return classMap[providerName] || 'provider-default';
    },

    getBetTypeClass(betType) {
      const typeMap = {
        '0': 'bet-type-cash',
        '1': 'bet-type-bonus',
        '2': 'bet-type-free'
      };

      return typeMap[betType] || 'bet-type-default';
    },

    getBetTypeText(betType) {
      const typeMap = {
        '0': 'Cash Bet',
        '1': 'Bonus Bet',
        '2': 'Free Bet'
      };

      return typeMap[betType] || 'Unknown';
    },

    getStatusClass(status) {
      const statusMap = {
        '0': 'status-inactive',
        '1': 'status-active',
        '2': 'status-pending'
      };

      return statusMap[status] || 'status-default';
    },

    getStatusText(status) {
      const statusMap = {
        '0': 'Inactive',
        '1': 'Active',
        '2': 'Pending'
      };

      return statusMap[status] || 'Unknown';
    },
    openEditModal(item) {
      this.editForm = {
        id: item.id,
        name: item.name,
        priority: item.priority || 0,
        page_desc: item.page_desc || "",
        page_url: item.page_url || "",
        icon_url: item.icon_url || "",
      };
      this.showEditModal = true;
    },
    closeEditModal() {
      this.showEditModal = false;
      this.editForm = {
        id: null,
        name: null,
        priority: null,
        page_desc: "",
        page_url: "",
        icon_url: "",
      };
    },
    async updateMenuHighlightDetails() {
      let app = this;
      
      const payload = {
        "menu_id": this.editForm.id.toString(),
        "name": this.editForm.name,
        "timestamp": Date.now(),
        "priority": this.editForm.priority.toString(),
        "page_desc": this.editForm.page_desc,
        "page_url": this.editForm.page_url,
        "icon_url": this.editForm.icon_url
      };
      
      app.$swal.fire({
        title: 'Update Confirmation',
        text: 'Are you sure you want to update this menu highlight?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          app.isLoading = true;
          return await this.updateMenuHighlight(payload);
        },
      })
      .then(async (result) => {
        app.isLoading = false;
        
        if (result.value && result.value.status === 200) {
          app.$swal.fire({
            title: 'Updated!',
            text: result.value.message,
            icon: 'success'
          });
          // Close the modal
          app.closeEditModal();
          // Refresh the data after successful update
          await app.setMenuHighlights();
        } else if (result.value) {
          app.$swal.fire('Error!', result.value.message, 'error');
        }
      });
    },
  },
}
</script>

<style scoped>
.provider-badge {
  padding: 8px 12px;
  border-radius: 20px;
  color: white;
  cursor: pointer;
  background-color: #3498DB;
}

.provider-pragmatic {
  background-color: #F1C40F;
  color: #333; /* Dark text for better contrast on yellow */
}

.provider-sporty {
  background-color: #E74C3C;
}

.provider-kiron {
  background-color: #1ABC9C;
}

.provider-ezugi {
  background-color: #D35400;
}

.provider-goldenrace {
  background-color: #8E44AD;
}

.provider-playtech {
  background-color: #16A085;
}

.provider-microgaming {
  background-color: #2980B9;
}

.provider-netent {
  background-color: #C0392B;
}

.provider-quickspin {
  background-color: #27AE60;
}

.provider-spinomenal {
  background-color: #7D3C98;
}

.provider-default {
  background-color: #2c6dff;
}

/* Bet type badges */
.bet-type-badge {
  padding: 6px 10px;
  border-radius: 16px;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: inline-block;
  text-align: center;
  min-width: 100px;
  letter-spacing: 0.3px;
  font-size: 0.75rem;
}

.bet-type-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

.bet-type-cash {
  background-color: #2ecc71; /* Green */
}

.bet-type-bonus {
  background-color: #f39c12; /* Orange */
}

.bet-type-free {
  background-color: #3498db; /* Blue */
}

.bet-type-default {
  background-color: #95a5a6; /* Gray */
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 6px 12px;
  min-width: 90px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

.status-active {
  background-color: #2ecc71; /* Green */
}

.status-inactive {
  background-color: #e74c3c; /* Red */
}

.status-pending {
  background-color: #f39c12; /* Orange */
}

.status-default {
  background-color: #95a5a6; /* Gray */
}
</style>
