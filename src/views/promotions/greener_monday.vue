<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Greener" pageSubtitle="Monday" />

    <!-- Search Filters Panel -->
    <div class="bg-white rounded-lg shadow-lg mx-3 mb-4">
      <div class="p-4 border-b">
        <h3 class="text-lg font-medium text-gray-700">Filters</h3>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Customer Identification</h4>
          <div class="space-y-2">
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Mobile Number</label>
              <input type="text" v-model="payload.phone_number" placeholder="Enter mobile number"
                class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                @keyup.enter="applyFilters()">
            </div>
          </div>
        </div>


        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Date</h4>
          <div class="space-y-2">
            <div>
              <label class="block text-xs text-gray-600 mb-1">Select Date Range</label>
              <VueDatePicker v-model="date" range multi-calendars :enable-time-picker="false" :format="'yyyy-MM-dd'"
                :preset-ranges="presetRanges" placeholder="Select date range" class="w-full text-xs"
                @update:model-value="selectDate" />
            </div>
          </div>
        </div>


        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Limits</h4>
          <div class="space-y-2">
            <div>
              <label class="block text-xs text-gray-600 mb-1">Limits</label>
              <select
                class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                v-model="payload.limit" @change="applyFilters()">
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </select>
            </div>
          </div>
        </div>

      </div>

      <!-- Action Buttons -->
      <!-- <div class="mt-4 flex justify-end space-x-2">
        <button @click="exportToCSV"
          class="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors flex items-center">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
            </path>
          </svg>
          Export CSV
        </button>

        <button @click="exportToExcel"
          class="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
            </path>
          </svg>
          Export Excel
        </button> 
      </div> -->

      <!-- Balance Filters -->
      <div class="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">


      </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 mx-3 mb-4"
      v-if="summaryData.totalBalance || summaryData.totalDeposits || summaryData.totalWithdrawals || summaryData.netRevenue">
      <!-- Total Balance Card -->
      <!-- <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-md p-3 text-white">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-blue-100 text-xs font-medium">Total Balance</p>
            <p class="text-lg font-bold">KES {{ formatCurrency(summaryData.totalBalance) }}</p>
          </div>
          <div class="bg-blue-400 bg-opacity-30 rounded-full p-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
              </path>
            </svg>
          </div>
        </div>
      </div> -->

      <!-- Total Deposits Card -->
      <!-- <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-md p-3 text-white">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-green-100 text-xs font-medium">Total Deposits</p>
            <p class="text-lg font-bold">KES {{ formatCurrency(summaryData.totalDeposits) }}</p>
          </div>
          <div class="bg-green-400 bg-opacity-30 rounded-full p-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12">
              </path>
            </svg>
          </div>
        </div>
      </div> -->

      <!-- Total Withdrawals Card -->
      <!-- <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow-md p-3 text-white">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-orange-100 text-xs font-medium">Total Withdrawals</p>
            <p class="text-lg font-bold">KES {{ formatCurrency(summaryData.totalWithdrawals) }}</p>
          </div>
          <div class="bg-orange-400 bg-opacity-30 rounded-full p-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 13l-5 5m0 0l-5-5m5 5V6">
              </path>
            </svg>
          </div>
        </div>
      </div> -->

      <!-- Net Revenue Card -->
      <!-- <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-md p-3 text-white">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-purple-100 text-xs font-medium">Net Revenue</p>
            <p class="text-lg font-bold">KES {{ formatCurrency(summaryData.netRevenue) }}</p>
          </div>
          <div class="bg-purple-400 bg-opacity-30 rounded-full p-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
              </path>
            </svg>
          </div>
        </div>
      </div> -->
    </div>

    <div class="block p-3 bg-white overflow-x-auto">
      <auto-table :data="data" :has-actions="false"
       :loading="isLoading" :total-items="total" :decimal-places="decimalPlaces"
        :items-per-page="limit" :current-page-prop="offset" :format-date-columns="['created_at']"
        :server-side-pagination="true" :pagination="total > limit"
        :show-items-count="true" @page-change="gotToPage">
        <!-- Account Number Column -->
        <template #acc_number="{ item }">
          <div class="font-bold">{{ item.acc_number }}</div>
        </template>
      </auto-table>

    </div>
  </div>
</template>

<script>
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import { mapActions } from "vuex";
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
import $ from "jquery";
import { AutoTable, ActionDropdown, ActionItem, CustomLoading } from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';
import { startOfMonth, endOfMonth, subMonths, startOfYear, endOfYear } from 'date-fns';
import * as XLSX from 'xlsx';

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
    VueDatePicker,
  },
  data() {
    return {
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",
      //
      openModal: false,
      customer: null,
      isLoading: false,
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      payload: {
        phone_number: "",
        status: "",
        start: "",
        end: "",
        page: 1,
        limit: 100,
        skip_cache: "",
        timestamp: "",
        sort: "",
      },
      data: [],
      moment: moment,
      showDropdown: [],
      summaryData: {
        totalBalance: 0,
        totalDeposits: 0,
        totalWithdrawals: 0,
        netRevenue: 0
      },
      //
      date: null,
      dateFormat: 'MMM DD, YYYY',
      showAdvancedFilters: false,

      presetRanges: [
        { label: 'Today', range: [new Date(), new Date()] },
        { label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())] },
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        { label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())] },
      ],
      //

      //
      products: [],
      moreParams: {
        start: '',
        end: '',
        page: '1',
        limit: "100",
        timestamp: 'timestamp',
        skip_cache: '',
        product_status: '',
        product_name: '',
        product_id: '',
        export: '',
      },

      //cust
      form: {
        timestamp: "",
        msisdn: "",
        customer_name: "",
        email: "",
        id_type: "",
        national_id: "",
        group_id: "",
        product_id: "",
      },
      showDetailsModal: false,
      selectedCustomer: null,
      decimalPlaces: 
        {
          Total_odds: 2,
          Stake: 2,
          Win_amount: 2,
          Deposits: 2,
          Withdrawals: 2,
         },
    }
  },
  //
  async mounted() {
    await this.setGreenerMonday()
  },

  methods: {
    ...mapActions(["getGreenerMonday", "toggleSideMenu", "updateGreenerMonday"]),
    //
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    //
    async setGreenerMonday() {
      let app = this
      app.isLoading = true
      app.payload.timestamp = Date.now()

      let response = await this.getGreenerMonday(app.payload)
      if (response.status === 200) {
        app.data = response.message.result
        this.total = parseInt(response.message.record_count)

        app.showDropdown = []
        for (let i = 0; i < app.data.length; i++) {
          app.showDropdown.push(false)
        }

        // Calculate summary totals
        app.calculateSummaryTotals()
      } else {
        app.data = []
        app.total = 0
        // Reset summary data
        app.summaryData = {
          totalBalance: 0,
          totalDeposits: 0,
          totalWithdrawals: 0,
          netRevenue: 0
        }
      }

      app.isLoading = false
    },

    // Calculate summary totals from current data data
    calculateSummaryTotals() {
      this.summaryData = {
        totalBalance: 0,
        totalDeposits: 0,
        totalWithdrawals: 0,
        netRevenue: 0
      }

      if (this.data && this.data.length > 0) {
        this.data.forEach(profile => {
          this.summaryData.totalBalance += parseFloat(profile.balance || 0)
          this.summaryData.totalDeposits += parseFloat(profile.total_deposits || 0)
          this.summaryData.totalWithdrawals += parseFloat(profile.total_withdrawals || 0)
          this.summaryData.netRevenue += parseFloat(profile.net_revenue || 0)
        })
      }
    },


    // Apply filters
    async applyFilters() {
      this.offset = 1
      this.payload.page = 1
      await this.setGreenerMonday()
    },

    // Reset filters
    resetFilters() {
      this.payload = {
        status: "",
        mobile_number: "",
        acc_number: "",
        network: "",
        min_balance: "",
        max_balance: "",
        start: "",
        end: "",
        page: 1,
        limit: 10,
        skip_cache: "",
        timestamp: "",
        sort: "",
        product_id: "",
        group_id: "",
      }
      this.date = null
      this.offset = 1
      this.setGreenerMonday()
    },

    updateStatus(item, status) {
      const payload = {
        id: item.leaderboard_id,
        status: status,
        timestamp: Date.now()
      }
      this.updateLeaderboardSt(payload)
    },

    updateBlackistStatus(item, status) {
      const payload = {
        id: item.leaderboard_id,
        blacklist_status: status,
        timestamp: Date.now()
      }
      this.updateLeaderboardSt(payload)
    },

    //
    async updateLeaderboardSt(payload) {
      let app = this
      // const payload ={
      //   id: item.leaderboard_id,
      //   status: status,
      //   blacklist_status: blacklist_status??'',
      //   timestamp: Date.now()
      // }

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this will change the leaderboard status!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, Update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          app.loading = true
          $('#addMember').html(' Please Wait ...')
          return await this.updateLeaderboard(payload)
        },
      })
        .then(async (result) => {
          $('#addMember').html('Add');
          app.loading = false
          if (result.value.status === 200) {
            app.$swal.fire({
              title: 'Added!',
              text: result.value.message,
              icon: 'success'
            }).then(async (result) => {
              await this.setGreenerMonday()
            })

          } else {
            app.$swal.fire('Error!', result.value.message, 'error')
          }
        })
    },

    //
    gotToPage(page) {
      let vm = this
      vm.payload.page = page
      vm.offset = page
      vm.setGreenerMonday()
    },

    //
    async selectDate() {
      if (this.date) {
        let startDate = this.date[0]
        let endDate = this.date[1] || this.date[0]

        // Check if start date is behind today, if so switch with end date
        const today = new Date()
        today.setHours(0, 0, 0, 0) // Reset time to start of day for comparison

        if (startDate && new Date(startDate) < today && endDate && new Date(endDate) >= today) {
          // Switch dates if start is behind today but end is not
          const temp = startDate
          startDate = endDate
          endDate = temp
        }

        this.payload.start = this.formatDate(startDate)
        this.payload.end = this.formatDate(endDate)
      } else {
        this.payload.start = ''
        this.payload.end = ''
      }
      this.applyFilters();
    },

    //
    formatDate(date) {
      let d = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    //
    async viewReferrals(row) {
      this.closeDropdown()
      await this.$router.push({ name: 'referrals', query: { msisdn: row.msisdn } })
    },

    //
    toggleSearchDropdown() {
      // Toggle the value of searchDropdown
      this.searchDropdown = !this.searchDropdown;
    },

    //
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },
    viewCustomerDetails(customer) {
      this.selectedCustomer = customer;
      this.showDetailsModal = true;
      this.closeDropdown();
    },
    // Get status text based on status and blacklist_status
    getStatusText(item) {
      // Blacklist status takes priority
      if (item.blacklist_status === '1' || item.blacklist_status === 1) {
        return 'Blacklisted';
      }

      // Regular status
      if (item.status === '1' || item.status === 1) {
        return 'Active';
      }

      return 'Suspended';
    },

    // Get CSS class for status badge
    getStatusClass(item) {
      // Blacklist status takes priority - always blue
      if (item.blacklist_status === '1' || item.blacklist_status === 1) {
        return 'bg-blue-500';
      }

      // Regular status
      if (item.status === '1' || item.status === 1) {
        return 'bg-green-500';
      }

      return 'bg-red-500';
    },

    // Legacy method for backward compatibility
    getStatusTextLegacy(status) {
      const statusMap = {
        '1': 'Active',
        '3': 'Deactivated',
        '0': 'Inactive'
      };
      return statusMap[status] || 'Unknown';
    },
    getLoginStatusText(status) {
      const statusMap = {
        '1': 'Active',
        '2': 'Pending Verification',
        '3': 'Locked',
        '4': 'Suspended',
        '5': 'Deactivated',
        '6': 'New'
      };
      return statusMap[status] || 'Unknown';
    },

    // Format numbers with commas and put negative figures in brackets
    formatCurrency(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0.00';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

    // Export name and msisdn to CSV
    exportToCSV() {
      if (!this.data || this.data.length === 0) {
        this.$swal.fire({
          icon: 'warning',
          title: 'No Data',
          text: 'No data available to export.'
        });
        return;
      }

      // Prepare CSV data
      const csvData = [];

      // Add header row
      csvData.push(['Name', 'Phone Number']);

      // Add data rows
      this.data.forEach(profile => {
        csvData.push([
          profile.name || '',
          profile.msisdn || ''
        ]);
      });

      // Convert to CSV string
      const csvContent = csvData.map(row =>
        row.map(field => `"${field}"`).join(',')
      ).join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');

      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `leaderboard_contacts_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Show success message
        this.$swal.fire({
          icon: 'success',
          title: 'Export Successful',
          text: `Exported ${this.data.length} contacts to CSV file.`,
          timer: 2000,
          showConfirmButton: false
        });
      }
    },

    // Export name and msisdn to Excel
    exportToExcel() {
      if (!this.data || this.data.length === 0) {
        this.$swal.fire({
          icon: 'warning',
          title: 'No Data',
          text: 'No data available to export.'
        });
        return;
      }

      // Prepare Excel data
      const excelData = [];

      // Add header row
      excelData.push(['Name', 'Phone Number']);

      // Add data rows
      this.data.forEach(profile => {
        excelData.push([
          profile.name || '',
          profile.msisdn || ''
        ]);
      });

      // Create workbook and worksheet
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.aoa_to_sheet(excelData);

      // Set column widths for better formatting
      const columnWidths = [
        { wch: 25 }, // Name column
        { wch: 15 }  // Phone Number column
      ];
      worksheet['!cols'] = columnWidths;

      // Style the header row
      const headerStyle = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "4F46E5" } }, // Indigo background
        alignment: { horizontal: "center", vertical: "center" }
      };

      // Apply header styling
      if (worksheet['A1']) worksheet['A1'].s = headerStyle;
      if (worksheet['B1']) worksheet['B1'].s = headerStyle;

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Leaderboard Contacts');

      // Generate filename with current date
      const filename = `leaderboard_contacts_${new Date().toISOString().split('T')[0]}.xlsx`;

      // Write and download file
      XLSX.writeFile(workbook, filename);

      // Show success message
      this.$swal.fire({
        icon: 'success',
        title: 'Export Successful',
        text: `Exported ${this.data.length} contacts to Excel file.`,
        timer: 2000,
        showConfirmButton: false
      });
    },

    // Format numbers with commas (no decimal places for counts)
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '0';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    }
  }
}
</script>

<style scoped>
/* Add any additional styles here */
.modal {
  transition: opacity 0.25s ease;
}

/* Ensure dropdowns are on top */
:deep(.action-dropdown) {
  z-index: 50 !important;
}

:deep(.action-dropdown .absolute) {
  z-index: 50 !important;
}


/* Date picker styles */
:deep(.dp__input) {
  padding: 0.25rem 2rem;
  font-size: 0.75rem;
  line-height: 1rem;
  border-radius: 0.375rem;
}

:deep(.dp__main) {
  font-size: 0.75rem;
}
</style>
