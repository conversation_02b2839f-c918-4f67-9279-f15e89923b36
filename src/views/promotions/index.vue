<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Promotions" pageSubtitle="" />

    <div class="block px-6 py-3 mx-3 mb-4">
      <!-- Enhanced Filter Section -->
      <div class="filter-section p-4 mb-6">
        <h3 class="text-lg font-semibold mb-3 text-gray-700">Filter Promotions</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <!-- Promotion Type Filter -->
          <div class="block">
            <label class="text-xs font-medium text-gray-600 mb-1 block">Promotion Type</label>
            <div class="relative">
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="filter"
                @click="toggleSearchDropdown" :placeholder="searchDropdownPlaceholder || 'Select promotion type'" />
              <div class="absolute right-3 top-2.5 text-gray-400">
                <i class="fa fa-chevron-down"></i>
              </div>
              <ul v-if="searchDropdown"
                class="absolute left-0 mt-1 w-full bg-white border border-gray-300 rounded-md dropdown-list z-10 shadow-lg max-h-60 overflow-y-auto">
                <li v-for="item in promotion_filters" :key="item.type"
                  class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-sm" @click="setFilter(item)">
                  {{ item.type }}
                </li>
              </ul>
            </div>
          </div>

          <!-- Component Filter -->
          <div class="block">
            <label class="text-xs font-medium text-gray-600 mb-1 block">Component</label>
            <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-sm" v-model="componentFilter" @change="applyFilters">
              <option value="">All Components</option>
              <option value="SPORTS">Sports</option>
              <option value="DEPOSIT">Deposit</option>
              <option value="CASINO">Casino</option>
            </select>
          </div>

          <!-- Status Filter -->
          <div class="block">
            <label class="text-xs font-medium text-gray-600 mb-1 block">Status</label>
            <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-sm" v-model="statusFilter" @change="applyFilters">
              <option value="">All Statuses</option>
              <option value="1">Active</option>
              <option value="0">Inactive</option>
            </select>
          </div>

          <!-- Add Promotion Button -->
          <div class="flex items-end">
            <router-link class="w-full text-center text-sm px-4 py-2 rounded-md bg-primary text-white font-bold hover:bg-opacity-90 transition-all" :to="{ name: 'promotions-add' }">
              <i class="fa fa-plus mr-1"></i> Add Promotion
            </router-link>
          </div>
        </div>
      </div>

      <!-- Promotions Grid -->
      <div class="flex justify-center items-center">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 w-full">
          <div v-for="promo in filteredPromotions" :key="promo.promo_id"
            class="promo-card relative bg-white p-0 rounded-lg shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-all">
            <!-- Status Badge -->
            <div class="absolute top-3 right-3 z-10">
              <span class="px-2 py-1 text-xs font-bold rounded-full"
                :class="promo.promo_status === '1' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                {{ promo.promo_status === "1" ? "Active" : "Inactive" }}
              </span>
            </div>

            <!-- Component Badge -->
            <div class="absolute top-3 left-3 z-10">
              <span class="px-2 py-1 text-xs font-bold rounded-full"
                :class="{
                  'bg-blue-100 text-blue-800': promo.component === 'SPORTS',
                  'bg-purple-100 text-purple-800': promo.component === 'DEPOSIT',
                  'bg-yellow-100 text-yellow-800': promo.component === 'CASINO',
                  'bg-gray-100 text-gray-800': !['SPORTS', 'DEPOSIT', 'CASINO'].includes(promo.component)
                }">
                {{ promo.component }}
              </span>
            </div>

            <!-- Image -->
            <div class="relative h-48 bg-gradient-to-r from-indigo-500 to-purple-600">
              <img v-if="promo.promo_images" :src="promo.promo_images" alt="Promo Image" class="w-full h-full object-cover" />
              <div v-else class="w-full h-full flex items-center justify-center">
                <span class="text-white text-lg font-bold">{{ promo.promo_name }}</span>
              </div>
            </div>

            <!-- Content -->
            <div class="p-4">
              <h3 class="text-xl font-bold mb-2 text-gray-800 line-clamp-1">{{ promo.promo_name }}</h3>
              <p class="text-sm text-gray-500 mb-4">{{ promo.promo_type }}</p>

              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="font-semibold text-gray-600">Bonus {{promo.bonus_type === '1' ? 'Percentage' : 'Amount'}}:</span>
                  <span class="text-gray-800">{{promo.bonus_type === '1' ? `${promo.bonus_amount}%` : `KES. ${promo.bonus_amount}`}}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-semibold text-gray-600">Min Odds:</span>
                  <span class="text-gray-800">{{ promo.min_odds }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-semibold text-gray-600">Min Stake:</span>
                  <span class="text-gray-800">KES. {{ promo.min_stake }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-semibold text-gray-600">Expiry:</span>
                  <span class="text-gray-800">{{ promo.expiry_period_in_hours }} hours</span>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="mt-4 flex justify-between items-center">
                <button class="text-indigo-600 hover:text-indigo-800 flex items-center" @click="viewPromoDetails(promo)">
                  <i class="fa fa-eye mr-1"></i> View Details
                </button>
                <button class="bg-orange-400 text-white px-3 py-1.5 rounded-md hover:bg-orange-500 transition-colors text-sm" @click="editPromotion(promo)">
                  <i class="fa fa-edit mr-1"></i> Edit
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="filteredPromotions.length === 0" class="text-center py-10">
        <div class="text-gray-400 text-5xl mb-4">
          <i class="fa fa-ticket-alt"></i>
        </div>
        <h3 class="text-xl font-semibold text-gray-600 mb-2">No promotions found</h3>
        <p class="text-gray-500">Try adjusting your filters or add a new promotion</p>
      </div>
    </div>
  </div>

  <!-- Promotion Details Modal -->
  <promo-details-modal
    :show="showPromoModal"
    :promo="selectedPromo"
    @close="showPromoModal = false"
  />
</template>

<script>
import moment from "moment";
import { mapActions } from "vuex";
import { CustomLoading } from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';
import PromoDetailsModal from '@/components/promotions/PromoDetailsModal.vue';

export default {
  components: {
    CustomLoading,
    PageHeader,
    PromoDetailsModal
  },
  data() {
    return {
      moment: moment,
      //
      isLoading: false,
      //
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],
      //
      selectedFilter: {},
      filter: null,
      searchDropdown: false,
      searchDropdownPlaceholder: "",

      // Modal related
      showPromoModal: false,
      selectedPromo: null,

      // Additional filters
      componentFilter: "",
      statusFilter: "",

      // data Params
      promotions: [],
      dataParams: {
        timestamp: '',
        promo_name: '',
        promo_id: '',
        promo_component: '',
        amount: '',
        status: '',
        limit: '10',
        sort: '',
        page: '1',
        start: '',
        end: '',
        skipCache: '1',
      },

      // filterParams
      promotion_filters: [],
      filterParams: {
        timestamp: '',
        promo_name: '',
        component: '',
        type_id: '',
        status: '',
        limit: '',
        sort: '',
        page: '',
        start: '',
        end: '',
      },



    }
  },

  computed: {
    filteredPromotions() {
      let filtered = this.promotions;

      // Filter by component
      if (this.componentFilter) {
        filtered = filtered.filter(promo => promo.component === this.componentFilter);
      }

      // Filter by status
      if (this.statusFilter !== "") {
        filtered = filtered.filter(promo => promo.promo_status === this.statusFilter);
      }

      return filtered;
    }
  },

  async mounted() {
    await this.setPromotionFilters()
    await this.setPromotions()
  },
  methods: {
    ...mapActions(["getPromotions", "fillPromo", "getPromotionFilters", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    // Toggle search dropdown
    toggleSearchDropdown() {
      this.searchDropdown = !this.searchDropdown;
    },

    // View promotion details in modal
    viewPromoDetails(promo) {
      this.selectedPromo = promo;
      this.showPromoModal = true;
    },

    // Apply filters
    applyFilters() {
      // Update data params for server-side filtering
      this.dataParams.promo_component = this.componentFilter;
      this.dataParams.status = this.statusFilter;

      // Fetch promotions with new filters
      this.setPromotions();
    },

    // Fetch Promotion Filters
    async setPromotionFilters() {
      let app = this
      app.isLoading = true
      app.filterParams.timestamp = Date.now()

      let response = await this.getPromotionFilters(app.filterParams)

      if (response.status === 200) {
        app.promotion_filters = response.message.result
        app.total = parseInt(response.message.record_count)
      }

      app.isLoading = false
    },

    setFilter(item) {
      this.filter = item.type
      this.searchDropdown = false
      this.dataParams.promo_component = item.component

      // If the filter is related to a component, also set the component filter
      if (item.component) {
        this.componentFilter = item.component;
      }

      this.setPromotions()
    },

    // Fetch promotions from API
    async setPromotions() {
      let app = this
      app.isLoading = true
      app.promotions = []
      app.dataParams.timestamp = Date.now()

      let response = await this.getPromotions(app.dataParams)

      if (response.status === 200) {
        app.promotions = response.message.result
        app.total = parseInt(response.message.record_count)
        app.showDropdown = []
        for (let i = 0; i < app.promotion_filters.length; i++) {
          app.showDropdown.push(false)
        }
      } else {
        app.promotions = []
        app.total = 0
      }

      app.isLoading = false
    },

    formatDate(date) {
      let d = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    // Pagination and dropdowns
    gotToPage(page) {
      let vm = this
      vm.dataParams.page = page
      vm.offset = page
      vm.setPromotions()
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    // Edit promotion
    async editPromotion(promo) {
      await this.fillPromo(promo)
      await this.$router.push({ name: 'promotions-edit' })
    },
  },
}
</script>


<style scoped>
.promo-card {
  max-width: 350px;
  min-height: 400px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.promo-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Line clamp for text truncation */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Dropdown styling */
.dropdown-list {
  max-height: 200px;
  overflow-y: auto;
  z-index: 50;
}

/* Filter section styling */
.filter-section {
  background-color: #f9fafb;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
