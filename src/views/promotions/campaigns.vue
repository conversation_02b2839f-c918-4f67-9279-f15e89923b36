<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Campaigns" pageSubtitle="Origins" />

    <div class="block py-3 mx-2 mb-4">
      <!-- Filters Section -->
      <div class="mb-4 bg-white rounded-lg shadow">
        <div class="p-4">
          <h3 class="text-lg font-medium text-gray-700 mb-3">Filters</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">

            <!-- Filter Actions -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">Type</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Filter Type</label>
                  <select
                    class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="table_select" @change="handleTableChange">
                    <option v-for="(item, index) in table_types" :key="index" :value="item">
                      {{ item.text }}
                    </option>
                  </select>
                </div>
              </div>
            </div>


            <!-- Date Range Group -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">Date</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Select Date Range</label>
                  <VueDatePicker v-model="date" range multi-calendars :enable-time-picker="false" :format="'yyyy-MM-dd'"
                    :preset-ranges="presetRanges" placeholder="Select date range" class="w-full text-xs"
                    @update:model-value="selectDate" />
                </div>
              </div>
            </div>

            <!-- UTM Source Filter -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">UTM Source</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs font-bold text-gray-700 mb-1">Search Source</label>
                  <input 
                    type="text" 
                    v-model="searchSource" 
                    placeholder="Enter UTM source" 
                    class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    @keyup.enter="applyFilters()">
                </div>
              </div>
            </div>

            <!-- Report Options -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">Report Options</h4>
              <div class="space-y-2">
                <div class="flex items-center">
                  <input 
                    type="checkbox" 
                    id="reportToggle"
                    v-model="isReport" 
                    class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                    @change="handleReportToggle">
                  <label for="reportToggle" class="ml-2 block text-xs text-gray-700">
                    Enable Detailed Report
                  </label>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>


      <!-- DataTable -->
      <auto-table v-if="table_select.value === '1'" :headers="tableHeaders" :data="campaigns" :has-actions="true"
        :total-items="total" :items-per-page="limit" :current-page-prop="offset" :server-side-pagination="true"
        :pagination="total > limit" :show-items-count="true" :auto-size-columns="false" :table-margin-bottom="20"
        :decimal-places=decimalPlaces :exclude-columns="excludeColumns"
        @page-change="gotToPage">
        <!-- Index Column -->
        <template #index="{ index }">
          <div class="text-center">{{ index + 1 + ((offset - 1) * limit) }}</div>
        </template>

        <!-- Campaign ID Column -->
        <template #id="{ item }">
          <div>{{ item.id }}</div>
        </template>

        <!-- UTM Source Column -->
        <template #utm_source="{ item }">
          <div>{{ item.utm_source }}</div>
        </template>

        <!-- UTM Campaign Column -->
        <template #utm_campaign="{ item }">
          <div>{{ item.utm_campaign }}</div>
        </template>

        <!-- UTM Medium Column -->
        <template #utm_medium="{ item }">
          <div>{{ item.utm_medium }}</div>
        </template>

        <!-- UTM Content Column -->
        <template #utm_content="{ item }">
          <div>{{ item.utm_content }}</div>
        </template>

        <!-- Actions Column -->
        <template #actions="{ item }">
          <action-dropdown>
            <!-- view details -->
            <action-item @click="setCampaignTransactions(item)">
              <i class="fas fa-eye mr-2 text-blue-600"></i> View Campaign Activations
            </action-item>
          </action-dropdown>
        </template>
      </auto-table>

      <!-- Dynamic Table -->
      <auto-table
        v-if="table_select.value !== '1'"
        :data="campaigns"
         :total-items="total" :items-per-page="limit" :current-page-prop="offset" :server-side-pagination="true"
        :pagination="total > limit" :show-items-count="true" :auto-size-columns="false" :table-margin-bottom="20"
        :decimal-places=decimalPlaces :exclude-columns="excludeColumns"
        @page-change="gotToPage"
      />

      <!-- Modal displaying all details -->
      <div v-if="isModalOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-7xl max-h-[90vh] overflow-y-auto mx-4 my-4">
          <div class="max-h-[90vh] overflow-y-auto">
            <!-- close button -->
            <div class="flex justify-between items-center p-4 border-b">
              <div>
                <h4 class="text-xl font-bold">Campaign Activations</h4>
                <p v-if="selectedCampaign" class="text-gray-600 mt-1">
                  <span class="font-medium">Source:</span> {{ selectedCampaign.utm_source }} |
                  <span class="font-medium">Campaign:</span> {{ selectedCampaign.utm_campaign }} |
                  <span class="font-medium">Medium:</span> {{ selectedCampaign.utm_medium }}
                </p>
              </div>
              <button @click="isModalOpen = false" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
            </div>

            <!-- Transactions Table -->
            <div class="px-2 sm:px-4 pb-4 ">
              <div class="overflow-x-auto">
                <DataTable :headers="transactionsTableHeaders" :items="campaignTransactions" :has-actions="false"
                  :total-items="totalModal" :items-per-page="limitModal" :current-page-prop="offsetModal"
                  :server-side-pagination="true" :pagination="totalModal > limitModal" :show-items-count="true"
                  :auto-size-columns="true" :no-styling="false" :table-margin-bottom="20" :exclude-columns="excludeColumns" @page-change="gotToPageModal">
                <!-- Index Column -->
                <template #index="{ index }">
                  <div class="text-center">{{ index + 1 }}</div>
                </template>
                
                <!-- TCOS ID Column -->
                <template #tcos_id="{ item }">
                  <div class="text-center">{{ item.tcos_id }}</div>
                </template>

                <!-- Total Sign Ups Column -->
                <template #total_sign_ups="{ item }">
                  <div class="text-center font-medium">{{ item.total_sign_ups }}</div>
                </template>

                <!-- Total Success Sign Ups Column -->
                <template #total_success_sign_ups="{ item }">
                  <div class="text-center font-medium text-green-600">{{ item.total_success_sign_ups }}</div>
                </template>

                <!-- Total Unverified Sign Ups Column -->
                <template #total_unverified_sign_ups="{ item }">
                  <div class="text-center font-medium text-orange-500">{{ item.total_unverified_sign_ups }}</div>
                </template>

                <!-- Total Deposits Column -->
                <template #total_deposits="{ item }">
                  <div class="text-center font-medium text-blue-600">{{ item.total_deposits }}</div>
                </template>

                <!-- Total Bet Count Column -->
                <template #total_bet_count="{ item }">
                  <div class="text-center font-medium">{{ item.total_bet_count }}</div>
                </template>

                <!-- Stake Amount Column -->
                <template #stake_amount="{ item }">
                  <div class="text-center font-medium">{{ item.stake_amount }}</div>
                </template>

                <!-- Payouts Amount Column -->
                <template #payouts_amount="{ item }">
                  <div class="text-center font-medium">{{ item.payouts_amount }}</div>
                </template>

                <!-- Net Gross Revenue Column -->
                <template #net_gross_revenue="{ item }">
                  <div class="text-center font-bold"
                    :class="{ 'text-green-600': parseFloat(item.net_gross_revenue) > 0, 'text-red-600': parseFloat(item.net_gross_revenue) < 0 }">
                    {{ item.net_gross_revenue }}
                  </div>
                </template>

                <!-- Summary Date Column -->
                <template #summary_date="{ item }">
                  <div class="text-center text-gray-700">{{ item.summary_date }}</div>
                </template>

                <!-- Created At Column -->
                <template #tcos_created_at="{ item }">
                  <div class="text-center text-gray-700">{{ item.tcos_created_at }}</div>
                </template>
              </DataTable>

                <!-- Empty State -->
                <div v-if="campaignTransactions.length === 0" class="py-8 text-center text-gray-500 text-lg">
                  No transaction data available
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import moment from "moment";
import { mapActions } from "vuex";
import { endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths } from "date-fns";
import VueDatePicker from "@vuepic/vue-datepicker";
import { ActionDropdown, ActionItem, CustomLoading, AutoTable } from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';
import Chart from 'chart.js/auto';


export default {
  components: {
    VueDatePicker,
    CustomLoading,
    PageHeader,
    ActionDropdown,
    ActionItem,
    AutoTable,
  },
  data() {
    return {
      moment: moment,
      date: null,
      presetRanges: [
        { label: 'Today', range: [new Date(), new Date()] },
        { label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())] },
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        { label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())] },
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],
      //
      isLoading: false,
      loading: true,
      fullPage: true,
      //
      total: 0,
      limit: 100, // Set the limit to 100
      offset: 1,

      totalModal: 0,
      limitModal: 100,
      offsetModal: 1,
      //
      selectedFilter: {},
      filter: null,
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      isModalOpen: false,

      isReport: false,

      // Filter properties
      selectedSource: '',
      searchSource: '',
      utmSources: [{label: 'All Sources', value: ''}],

      // data Params
      campaigns: [],
      dataParams: {
        report: "",
        table_type: 1,
        timestamp: '',
        limit: '100',
        sort: '',
        page: '1',
        start: '',
        end: '',
        skipCache: '1',
        origin: '',
      },

      // table headers
      tableHeaders: [
        // { key: "id", label: "Campaign ID", align: "center " },
        { key: "utm_source", label: "UTM Source", align: "left" },
        { key: "utm_campaign", label: "UTM Campaign", align: "left" },
        { key: "utm_medium", label: "UTM Medium", align: "left" },
        { key: "utm_content", label: "UTM Content", align: "left" },
      ],

      //table headers for campaign transactions
      transactionsTableHeaders: [
        // { key: "trx_count", label: "Trx Count", align: "center", width: "80px" },
        { key: "tcos_id", label: "Trx Origin ID"},
        { key: "total_sign_ups", label: "Total Sign Ups"},
        { key: "total_success_sign_ups", label: "Success Sign Ups"},
        { key: "total_unverified_sign_ups", label: "Unverified Sign Ups"},
        { key: "total_deposits", label: "Total Deposits"},
        { key: "total_bet_count", label: "Total Bets"},
        { key: "stake_amount", label: "Stake Amount"},
        { key: "payouts_amount", label: "Payouts Amount"},
        { key: "net_gross_revenue", label: "Net Revenue"},
        { key: "summary_date", label: "Summary Date"},
        { key: "tcos_created_at", label: "Created At"},
      ],
      campaignTransactions: [],
      excludeColumns: [
        "trx_count",
        "campaign_origin_id",
        "raw_campaign_name",
      ],

      selectedCampaign: null,
      table_types: [
        { text: 'Activation Summary', value: '1' },
        { text: 'Sign Ups', value: '2' },
        { text: 'Sports Bets', value: '3' },
        { text: 'Virtual Bets', value: '4' },
        // { text: 'Profile Logins', value: '5' },
        { text: 'Deposits', value: '6' },
        { text: 'Payouts', value: '7' },
      ],
      table_select: { text: 'Activation Summary', value: '1' },
      
      decimalPlaces: {
        sports_stake_amount: 2,
        sports_witholding_tax: 2,
        sports_excise_tax: 2,
        virtual_stake_amount: 2,
        virtual_witholding_tax: 2,
        virtual_excise_tax: 2,
        deposit_amount: 2,
        payout_amount: 2,
      },

      // Chart properties
      isChartCollapsed: true,
      chartInstance: null,
      chartType: 'bar',
      chartMetric: 'total_sign_ups',
      chartGroupBy: 'utm_source',
      chartColors: [
        'rgba(54, 162, 235, 0.7)',
        'rgba(255, 99, 132, 0.7)',
        'rgba(75, 192, 192, 0.7)',
        'rgba(255, 206, 86, 0.7)',
        'rgba(153, 102, 255, 0.7)',
        'rgba(255, 159, 64, 0.7)',
        'rgba(199, 199, 199, 0.7)',
        'rgba(83, 102, 255, 0.7)',
        'rgba(40, 159, 64, 0.7)',
        'rgba(210, 199, 199, 0.7)',
      ]
    }
  },
  async mounted() {
    await this.setCampaigns()
  },

  updated() {
    // Initialize chart when component is updated and campaigns data is available
    if (this.campaigns.length > 0 && !this.chartInstance && !this.isChartCollapsed) {
      this.initChart()
    }
  },
  methods: {
    ...mapActions(["getCampaignOrigins", "getCampaignTransactions", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

  handleReportToggle() {
    this.dataParams.report = this.isReport ? "1" : "";
    this.setCampaigns(); // Refresh data with new report setting
  },

    //
    toggleSearchDropdown() {
      this.searchDropdown = !this.searchDropdown;
    },
    // Handle table type change and fetch data
    handleTableChange() {
      console.log('Table type changed to:', this.table_select.text);
      this.dataParams.table_type = parseInt(this.table_select.value);
      this.setCampaigns();
    },

    //
    async setCampaigns() {
      let app = this
      app.isLoading = true
      app.campaigns = []
      app.dataParams.timestamp = Date.now()

      console.log('Fetching campaigns with params:', JSON.stringify(app.dataParams))
      let response = await this.getCampaignOrigins(app.dataParams)
      console.log('Response:', JSON.stringify(response))
      if (response.status === 200) {
        app.campaigns = response.message.result
        app.total = parseInt(response.message.record_count)

        // Extract UTM sources as label and id as value from the fetched campaigns
        if(app.dataParams.table_type === 1){
          this.extractUtmSources(response.message.result)
        }

        // Update chart if it's visible
        if (!this.isChartCollapsed && this.chartInstance) {
          this.$nextTick(() => {
            this.updateChart();
          });
        }
      } else {
        app.campaigns = []
        app.total = 0
      }
      app.isLoading = false
    },

    // Extract unique UTM sources as label and id as value from campaigns data
    extractUtmSources(campaigns) {
      if (!campaigns || !campaigns.length) {
        return;
      }


      console.log('UTM sources:1', JSON.stringify(this.utmSources));
      // Extract unique UTM sources as label and id as value
      this.utmSources = campaigns.map(campaign => ({
        label:campaign.utm_source,
        value: campaign.id
      }));

      console.log('UTM sources:2', JSON.stringify(this.utmSources));

    },

    // Fetch campaign transactions without opening modal
    async setCampaignTransactions(campaign) {
      let app = this
      let campaign_id = campaign.id || campaign.campaign_id
      app.isLoading = true
      app.campaignTransactions = []

      let payload = {
        "campaign_id": campaign_id,
        "timestamp": Date.now(),
        "limit": app.limitModal.toString(),
        "page": "1",
      }

      // If selectedCampaign is not set, find it
      if (!app.selectedCampaign) {
        const campaign = app.campaigns.find(c => c.id === campaign_id || c.campaign_id === campaign_id);
        if (campaign) {
          app.selectedCampaign = campaign;
        }
      }

      let response = await this.getCampaignTransactions(payload)
      if (response.status === 200) {
        app.campaignTransactions = response.message.result
        app.totalModal = parseInt(response.message.record_count)
      } else {
        app.campaignTransactions = []
        app.totalModal = 0
      }

      app.isModalOpen = true;
      app.isLoading = false
    },

    // Update date filter values and fetch data
    selectDate() {
      // If date is null (cleared), reset date filters and fetch data
      if (!this.date) {
        console.log('Date filter cleared, resetting and fetching data...');
        this.dataParams.start = '';
        this.dataParams.end = '';
        this.setCampaigns();
        return;
      }

      // If date range is incomplete, return without doing anything
      if (!this.date[0] || !this.date[1]) return;

      // Update date filter values
      this.dataParams.start = this.formatDate(this.date[0]);
      this.dataParams.end = this.formatDate(this.date[1]);

      // Log that date filter was updated and fetch data
      console.log('Date filter updated, fetching data with new date range...');
      this.setCampaigns();
    },



    // Apply all filters
    async applyFilters() {
      this.isLoading = true;
      this.dataParams.timestamp = Date.now();
      console.log("sfsdfds", JSON.stringify(this.selectedSource))

      // Apply UTM source filter if selected
      if (this.selectedSource) {
        this.dataParams.origin = this.selectedSource;
      } else {
        this.dataParams.origin = '';
      }

      // Apply search source filter if entered
      if (this.searchSource) {
        this.dataParams.origin = this.searchSource;
      }

      // Reset to first page
      this.offset = 1;
      this.dataParams.page = '1';

      await this.setCampaigns();
    },

    // Reset all filters
    async resetFilters() {
      this.date = null;
      this.selectedSource = '';
      this.searchSource = '';
      this.dataParams.start = '';
      this.dataParams.end = '';
      this.dataParams.origin = '';
      this.dataParams.timestamp = Date.now();

      // Reset to first page
      this.offset = 1;
      this.dataParams.page = '1';

      await this.setCampaigns();
    },

    // Chart methods
    toggleChartCollapse() {
      this.isChartCollapsed = !this.isChartCollapsed;

      // Initialize chart when expanded for the first time
      if (!this.isChartCollapsed && !this.chartInstance && this.campaigns.length > 0) {
        this.$nextTick(() => {
          this.initChart();
        });
      }
    },

    initChart() {
      const ctx = this.$refs.campaignChart;
      if (!ctx) return;

      // Destroy existing chart if it exists
      if (this.chartInstance) {
        this.chartInstance.destroy();
      }

      // Prepare data for the chart
      const chartData = this.prepareChartData();

      // Create new chart
      this.chartInstance = new Chart(ctx, {
        type: this.chartType,
        data: chartData,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top',
            },
            title: {
              display: true,
              text: `Campaign Performance by ${this.formatLabel(this.chartGroupBy)}`
            },
            tooltip: {
              callbacks: {
                label: (context) => {
                  let label = context.dataset.label || '';
                  if (label) {
                    label += ': ';
                  }
                  if (context.parsed.y !== null) {
                    // Format currency values
                    if (['net_gross_revenue', 'stake_amount', 'payouts_amount'].includes(this.chartMetric)) {
                      label += new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'KES'
                      }).format(context.parsed.y);
                    }
                    // Format count values with thousand separators
                    else if (['total_sign_ups', 'total_success_sign_ups', 'total_unverified_sign_ups',
                             'total_deposits', 'total_bet_count'].includes(this.chartMetric)) {
                      label += new Intl.NumberFormat('en-US').format(context.parsed.y);
                    }
                    // Default formatting
                    else {
                      label += context.parsed.y;
                    }
                  }
                  return label;
                }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: this.formatLabel(this.chartMetric)
              },
              ticks: {
                callback: (value) => {
                  // Format currency values
                  if (['net_gross_revenue', 'stake_amount', 'payouts_amount'].includes(this.chartMetric)) {
                    return new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'KES',
                      maximumFractionDigits: 0
                    }).format(value);
                  }
                  // Format count values with thousand separators
                  else if (['total_sign_ups', 'total_success_sign_ups', 'total_unverified_sign_ups',
                           'total_deposits', 'total_bet_count'].includes(this.chartMetric)) {
                    return new Intl.NumberFormat('en-US').format(value);
                  }
                  // Default formatting
                  return value;
                }
              }
            }
          }
        }
      });
    },

    updateChart() {
      if (this.chartInstance) {
        this.chartInstance.destroy();
      }
      this.initChart();
    },

    prepareChartData() {
      // Group data by the selected groupBy field
      const groupedData = {};

      this.campaigns.forEach(campaign => {
        const groupKey = campaign[this.chartGroupBy] || 'Unknown';
        if (!groupedData[groupKey]) {
          groupedData[groupKey] = 0;
        }

        // Sum the metric value
        const metricValue = parseFloat(campaign[this.chartMetric]) || 0;
        groupedData[groupKey] += metricValue;
      });

      // Convert to chart.js format
      const labels = Object.keys(groupedData);
      const data = Object.values(groupedData);

      // Prepare colors based on metric type
      let metricColor;

      // Assign specific colors based on metric
      switch(this.chartMetric) {
        case 'total_sign_ups':
          metricColor = 'rgba(54, 162, 235, 0.7)'; // Blue
          break;
        case 'total_success_sign_ups':
          metricColor = 'rgba(75, 192, 192, 0.7)'; // Green
          break;
        case 'total_unverified_sign_ups':
          metricColor = 'rgba(255, 206, 86, 0.7)'; // Yellow
          break;
        case 'total_deposits':
          metricColor = 'rgba(153, 102, 255, 0.7)'; // Purple
          break;
        case 'total_bet_count':
          metricColor = 'rgba(255, 159, 64, 0.7)'; // Orange
          break;
        case 'stake_amount':
          metricColor = 'rgba(255, 99, 132, 0.7)'; // Red
          break;
        case 'payouts_amount':
          metricColor = 'rgba(199, 199, 199, 0.7)'; // Gray
          break;
        case 'net_gross_revenue':
          metricColor = 'rgba(40, 167, 69, 0.7)'; // Green
          break;
        default:
          metricColor = this.chartColors[0];
      }

      const backgroundColors = this.chartType === 'pie'
        ? this.chartColors.slice(0, labels.length)
        : metricColor;

      const borderColors = this.chartType === 'pie'
        ? this.chartColors.map(color => color.replace('0.7', '1'))
        : metricColor.replace('0.7', '1');

      return {
        labels,
        datasets: [{
          label: this.formatLabel(this.chartMetric),
          data,
          backgroundColor: backgroundColors,
          borderColor: borderColors,
          borderWidth: 1
        }]
      };
    },

    formatLabel(key) {
      return key
        .replace(/_/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase());
    },

    formatDate(date) {
      let d = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    // Pagination and dropdowns
    gotToPage(page) {
      let vm = this
      vm.dataParams.page = page.toString()
      vm.offset = page
      vm.setCampaigns()
    },

    gotToPageModal(page) {
      let vm = this
      vm.offsetModal = page

      // Create a payload for pagination
      let payload = {
        "campaign_id": vm.selectedCampaign.id || vm.selectedCampaign.campaign_id,
        "timestamp": Date.now(),
        "limit": vm.limitModal.toString(),
        "page": page.toString(),
      }

      // Fetch data with pagination
      vm.isLoading = true
      vm.getCampaignTransactions(payload).then(response => {
        if (response.status === 200) {
          vm.campaignTransactions = response.message.result
          vm.totalModal = parseInt(response.message.record_count)
        } else {
          vm.campaignTransactions = []
          vm.totalModal = 0
        }
        vm.isLoading = false
      })
    },


    //
    async editPromotion(promo) {
      await this.fillPromo(promo)
      await this.$router.push({ name: 'campaigns-edit' })
    },


  },
}
</script>


<style scoped>
.promo-card {
  max-width: 350px;
  min-height: 400px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.promo-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Line clamp for text truncation */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Dropdown styling */
.dropdown-list {
  max-height: 200px;
  overflow-y: auto;
  z-index: 50;
}

/* Filter section styling */
.filter-section {
  background-color: #f9fafb;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Chart section styling */
.chart-container {
  transition: all 0.3s ease;
}

.chart-container canvas {
  max-height: 400px;
}

/* Mobile responsive improvements */
@media (max-width: 768px) {
  .filter-card {
    margin-bottom: 0.5rem;
  }

  /* Ensure modal is properly sized on mobile */
  .fixed.inset-0 {
    padding: 1rem;
  }

  /* Make table headers smaller on mobile */
  .text-xs {
    font-size: 0.65rem;
  }
}

/* Tablet responsive improvements */
@media (min-width: 769px) and (max-width: 1024px) {
  .grid-cols-1.md\\:grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* Ensure modal has proper z-index and positioning */
.modal-overlay {
  z-index: 9999 !important;
}
</style>
