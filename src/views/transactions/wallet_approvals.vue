<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Wallet" pageSubtitle="Approvals" />

    <!-- Filters Section -->
    <div class="px-4 mb-5">
      <div class="bg-white rounded-md shadow-sm mb-4">
        <div class="p-4 border-b">
          <h3 class="text-lg font-medium text-gray-700">Filters</h3>
        </div>
        
        <div class="p-4">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-3">
            <!-- Transaction Identification Group -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">Transaction Identification</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs font-bold text-gray-700 mb-1">Transaction ID</label>
                  <input 
                    type="text" 
                    placeholder="Enter transaction ID"
                    class="w-full px-2 py-1.5 text-xs text-gray-700 border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="moreParams.transaction_id"
                    @keyup.enter="applyFilters()"
                  >
                </div>
              </div>
                <div>
                  <label class="block text-xs font-bold text-gray-700 mb-1 mt-2">Mobile Number</label>
                  <input 
                    type="text" 
                    placeholder="254XXXXXXXXX"
                    class="w-full px-2 py-1.5 text-xs text-gray-700 border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="moreParams.mobile_number"
                    @keyup.enter="applyFilters()"
                  >
                </div>
            </div>
            
            <!-- Customer Group -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">Customer</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs font-bold text-gray-700 mb-1">Amount Range</label>
                  <div class="grid grid-cols-2 gap-2">
                    <input 
                      type="number" 
                      placeholder="Min amount"
                      class="w-full px-2 py-1.5 text-xs text-gray-700 border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                      v-model="moreParams.min_amount"
                      @keyup.enter="applyFilters()"
                    >
                    <input 
                      type="number" 
                      placeholder="Max amount"
                      class="w-full px-2 py-1.5 text-xs text-gray-700 border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                      v-model="moreParams.max_amount"
                      @keyup.enter="applyFilters()"
                    >
                  </div>
                </div>
              
                <div>
                  <label class="block text-xs font-bold text-gray-700 mb-1 mt-2">Status</label>
                  <select 
                    class="w-full px-2 py-1.5 text-xs text-gray-700 border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="moreParams.status"
                    @change="applyFilters()"
                  >
                    <option value="">All Statuses</option>
                    <option value="0">Pending</option>
                    <option value="1">Approved</option>
                    <option value="3">Rejected</option>
                  </select>
                </div>

              </div>
            </div>
            
            <!-- Status and Source Group -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">Additional Filters</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs font-bold text-gray-700 mb-1">Source</label>
                  <select 
                    class="w-full px-2 py-1.5 text-xs text-gray-700 border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="selected_source"
                  >
                    <option value="" disabled>Select source</option>
                    <option v-for="item in sourceOptions" :key="item.value" :value="item">
                      {{ item.text }}
                    </option>
                  </select>
                </div>

                <div>
                  <label class="block text-xs text-gray-600 mb-1">Select Date Range</label>
                  <VueDatePicker
                    v-model="date"
                    range
                    multi-calendars
                    :enable-time-picker="false"
                    :format="'yyyy-MM-dd'"
                    :preset-ranges="presetRanges"
                    placeholder="Select date range"
                    class="w-full text-xs"
                    @update:model-value="selectDate"
                  />
                </div>
              </div>
            </div>
            
            <!-- Date Range Group -->              
                <div class="flex justify-end pt-2">
                  <button 
                    @click="resetFilters()" 
                    class="px-3 py-1 mr-2 bg-gray-200 text-gray-700 text-xs rounded-md hover:bg-gray-300 transition-colors"
                  >
                    Reset
                  </button>
                  <button 
                    @click="applyFilters()" 
                    class="px-3 py-1 bg-blue-600 text-white text-xs rounded-md hover:bg-blue-700 transition-colors"
                  >
                    Apply
                  </button>
                </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Table -->
    <auto-table
      :headers="tableHeaders"
      :data="walletApprovals"
      :loading="isLoading"
      :total-items="total"
      :items-per-page="limit"
      :current-page-prop="offset"
      :server-side-pagination="true"
      :pagination="total > limit"
      :show-items-count="true"
      @page-change="gotToPage"
    >
      <!-- Transaction ID Column -->
      <template #transaction_id="{ item }">
        <div class="badge-container">
          <span 
            class="badge transaction-badge" 
            @click="copyToClipboard(item.transaction_id)"
            title="Click to copy"
          >
            {{ item.transaction_id }}
          </span>
        </div>
      </template>

      <!-- Amount Column -->
      <template #amount="{ item }">
        <div class="badge-container">
          <span 
            class="badge amount-badge" 
            :class="getAmountClass(parseFloat(item.amount))"
            @click="copyToClipboard(item.amount)"
            title="Click to copy"
          >
            {{ item.currency }} {{ item.amount }}
          </span>
        </div>
      </template>

      <!-- Mobile Column -->
      <template #msisdn="{ item }">
        <div class="badge-container">
          <span 
            class="badge mobile-badge" 
            @click="copyToClipboard(item.msisdn)"
            title="Click to copy"
          >
            +{{ item.msisdn }}
          </span>
        </div>
      </template>

      <!-- Status Column -->
      <template #status="{ item }">
        <div class="badge-container">
          <span 
            class="badge status-badge" 
            :class="getStatusClass(item.status)"
          >
            {{ getStatusLabel(item.status) }}
          </span>
        </div>
      </template>

      <!-- Date Column -->
      <template #created_at="{ item }">
        <div class="badge-container">
          <span 
            class="badge date-badge"
            @click="copyToClipboard(moment(item.created_at).format('lll'))"
            title="Click to copy"
          >
            {{ moment(item.created_at).format('lll') }}
          </span>
        </div>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item, index }">
        <div class="relative z-50">
          <action-dropdown 
            button-text="Actions" 
            :show-text="false"
            button-class="z-50"
            menu-class="z-50 origin-top-right"
            :menu-width="48"
          >
            <action-item
              text="View Details"
              color="blue"
              @click="viewDetails(item)"
            >
              <template #icon>
                <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </template>
            </action-item>
            <action-item
              v-if="item.status !== '1'"
              text="Approve"
              color="green"
              @click="handleApprove(item)"
            >
              <template #icon>
                <svg class="mr-3 h-5 w-5 text-green-500 group-hover:text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
              </template>
            </action-item>
            <action-item
              v-if="item.status !== '3'"
              text="Reject"
              color="red"
              @click="handleReject(item, index)"
            >
              <template #icon>
                <svg class="mr-3 h-5 w-5 text-red-500 group-hover:text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </template>
            </action-item>
          </action-dropdown>
        </div>
      </template>
    </auto-table>

    <!-- Details Modal -->
    <div v-if="viewModelOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div class="p-6">
          <!-- Modal Header -->
          <div class="border-b pb-4 mb-4">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="text-xl font-bold">Wallet Approval Details</h3>
                <div class="mt-1 text-sm text-gray-600 flex flex-col space-y-2">
                  <div class="flex items-center">
                    <span class="font-medium w-32">Transaction ID:</span> 
                    <span 
                      class="badge transaction-badge" 
                      @click="copyToClipboard(selectedApproval?.transaction_id || 'N/A')"
                      title="Click to copy"
                    >
                      {{ selectedApproval?.transaction_id || 'N/A' }}
                    </span>
                  </div>
                  <div class="flex items-center">
                    <span class="font-medium w-32">Mobile:</span> 
                    <span 
                      class="badge mobile-badge" 
                      @click="copyToClipboard('+' + selectedApproval?.msisdn)"
                      title="Click to copy"
                    >
                      +{{ selectedApproval?.msisdn }}
                    </span>
                  </div>
                </div>
              </div>
              <button @click="viewModelOpen = false" class="text-gray-500 hover:text-gray-700">
                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
          
          <!-- Transaction Details -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <p class="text-sm text-gray-500">Amount</p>
              <span 
                class="badge amount-badge inline-block mt-1" 
                :class="selectedApproval ? getAmountClass(parseFloat(selectedApproval.amount)) : ''"
                @click="copyToClipboard(selectedApproval?.currency + ' ' + selectedApproval?.amount)"
                title="Click to copy"
              >
                {{ selectedApproval?.currency }} {{ selectedApproval?.amount }}
              </span>
            </div>
            <div>
              <p class="text-sm text-gray-500">Status</p>
              <span 
                class="badge status-badge inline-block mt-1" 
                :class="selectedApproval ? getStatusClass(selectedApproval.status) : ''"
              >
                {{ getStatusLabel(selectedApproval?.status) }}
              </span>
            </div>
            <div>
              <p class="text-sm text-gray-500">Source</p>
              <span 
                class="badge source-badge inline-block mt-1"
                @click="copyToClipboard(selectedApproval?.source || 'N/A')"
                title="Click to copy"
              >
                {{ selectedApproval?.source || 'N/A' }}
              </span>
            </div>
            <div>
              <p class="text-sm text-gray-500">Date</p>
              <span 
                class="badge date-badge inline-block mt-1"
                @click="copyToClipboard(moment(selectedApproval?.created_at).format('lll'))"
                title="Click to copy"
              >
                {{ moment(selectedApproval?.created_at).format('lll') }}
              </span>
            </div>
          </div>
          
          <!-- Description and Reason -->
          <div class="mt-4">
            <h4 class="font-semibold text-lg mb-2">Additional Information</h4>
            
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="mb-4">
                <p class="text-sm text-gray-500 mb-1">Description</p>
                <p 
                  class="text-sm text-gray-700 p-2 bg-white rounded border"
                  @click="copyToClipboard(selectedApproval?.description || 'N/A')"
                  title="Click to copy"
                >
                  {{ selectedApproval?.description || 'No description available' }}
                </p>
              </div>
              
              <div>
                <p class="text-sm text-gray-500 mb-1">Reason</p>
                <p 
                  class="text-sm text-gray-700 p-2 bg-white rounded border"
                  @click="copyToClipboard(selectedApproval?.reason || 'N/A')"
                  title="Click to copy"
                >
                  {{ selectedApproval?.reason || 'No reason provided' }}
                </p>
              </div>
            </div>
          </div>
          
          <!-- Extra Data Section (if available) -->
          <div v-if="selectedApproval?.extra_data" class="mt-4">
            <h4 class="font-semibold text-lg mb-2">Extra Data</h4>
            
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div v-for="(value, key) in extraDataValues" :key="key">
                  <div class="flex items-center space-x-2">
                    <div class="w-1/3">
                      <p class="text-sm text-gray-500">{{ formatKey(key) }}</p>
                    </div>
                    <div class="w-2/3">
                      <p 
                        class="text-sm text-gray-700 cursor-pointer hover:text-indigo-600"
                        @click="copyToClipboard(value)"
                        title="Click to copy"
                      >
                        {{ value }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Reject Modal -->
    <div v-if="showApproveModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 z-50">
      <div class="bg-white p-6 rounded-lg w-full max-w-md shadow-lg">
        <h2 class="text-xl font-semibold mb-4">Reject Transaction</h2>
        <p class="mb-2">Enter a reason for rejection:</p>
        <textarea
          v-model="approve_payload.reason"
          placeholder="Reason for rejection"
          rows="3"
          class="w-full p-2 border rounded focus:outline-none focus:ring focus:border-blue-400"></textarea>

        <div class="mt-4 flex justify-end space-x-3">
          <button
            @click="showApproveModal = false"
            class="px-4 py-2 text-sm text-gray-600 border border-gray-300 rounded hover:bg-gray-100">
            Cancel
          </button>
          <button
            @click="confirmReject"
            class="px-4 py-2 text-sm text-white bg-red-600 rounded hover:bg-red-700">
            Confirm Reject
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {AutoTable, ActionDropdown, ActionItem, CustomLoading} from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';
import moment from "moment";
import {mapActions} from "vuex";
import VueDatePicker from "@vuepic/vue-datepicker";
import '@vuepic/vue-datepicker/dist/main.css';
import {endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths} from "date-fns";

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
    VueDatePicker
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      fullPage: true,
      viewModelOpen: false,
      date: null,

      showApproveModal: false,
      approveReason: '',
      walletApproval: null,
      selectedApproval: null,
      extraDataValues: {},

      moreParams: {
        transaction_id: '',
        status: "",
        mobile_number: "",
        min_amount: "",
        max_amount: "",
        start: "",
        end: "",
        page: "",
        timestamp: Date.now().toString(),
        skip_cache: "",
        limit: "100",
        source: "",
      },
      approve_payload: {
        timestamp: Date.now(),
        id: "",
        status: "",
        reason: ""
      },

      walletApprovals: [],
      total: 0,
      limit: 100,
      offset: 1,
      showDropdown: [],
      selected_source: {text: 'ALL', value: ''},
      sourceOptions: [
        {text: 'ALL', value: ''},
        {text: 'AVIATRIX', value: 'AVIATRIX_PAYOUT'},
        {text: 'KIRON LITE', value: 'KIRON_LITE_PAYOUT'},
        {text: 'SMART SOFT GAMING', value: 'SMART_SOFT_GAMING_PAYOUT'},
      ],
      
      tableHeaders: [
        { key: 'transaction_id', label: 'Transaction ID', align: 'center' },
        { key: 'amount', label: 'Amount', align: 'center' },
        { key: 'msisdn', label: 'Mobile', align: 'center' },
        { key: 'source', label: 'Source', align: 'center' },
        { key: 'created_at', label: 'Date', align: 'center' },
        { key: 'status', label: 'Status', align: 'center' },
        { key: 'actions', label: 'Actions', align: 'center' },
      ],
      
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
      ],
    }
  },
  watch:{
    selected_source(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.moreParams.source = newVal.value
        this.setWalletApprovals()
      }
    },
  },
  mounted() {
    this.setWalletApprovals()
  },
  methods: {
    ...mapActions(["getWalletApprovals", "updateWalletApprovals", "toggleSideMenu"]),
    
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1);
    },

    // Date filtering
    selectDate() {
      if (this.date && this.date.length === 2) {
        const startDate = new Date(this.date[0]);
        const endDate = new Date(this.date[1]);
        
        this.moreParams.start = startDate.toISOString().split('T')[0];
        this.moreParams.end = endDate.toISOString().split('T')[0];
        this.setWalletApprovals();
      }
    },
    
    resetFilters() {
      this.date = null;
      this.moreParams = {
        transaction_id: '',
        status: "",
        mobile_number: "",
        min_amount: "",
        max_amount: "",
        start: "",
        end: "",
        page: "1",
        timestamp: Date.now().toString(),
        skip_cache: "",
        limit: "100",
        source: "",
      };
      this.selected_source = {text: 'ALL', value: ''};
      this.offset = 1;
      this.setWalletApprovals();
    },

    // Pagination and dropdowns
    gotToPage(page) {
      this.moreParams.page = page
      this.offset = page
      this.setWalletApprovals()
    },
    
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },
    
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    async setWalletApprovals() {
      this.isLoading = true

      const params = new URLSearchParams();
      for (const key in this.moreParams) {
        if (this.moreParams.hasOwnProperty(key)) {
          params.append(key, this.moreParams[key]);
        }
      }
      let queryString = params.toString();

      let response = await this.getWalletApprovals(queryString)

      if (response.status === 200) {
        this.walletApprovals = response.message.result
        this.total = parseInt(response.message.record_count)

        this.showDropdown = Array(this.walletApprovals.length).fill(false);
      } else {
        this.walletApprovals = [];
        this.total = 0
      }

      this.isLoading = false
    },

    viewDetails(item) {
      this.selectedApproval = item;
      this.viewModelOpen = true;
      this.parseExtraData();
    },
    
    parseExtraData() {
      this.extraDataValues = {};
      if (this.selectedApproval && this.selectedApproval.extra_data) {
        try {
          const extraData = typeof this.selectedApproval.extra_data === 'string' 
            ? JSON.parse(this.selectedApproval.extra_data) 
            : this.selectedApproval.extra_data;
          
          this.extraDataValues = extraData;
        } catch (error) {
          console.error("Error parsing extra_data:", error);
        }
      }
    },
    
    formatKey(key) {
      // Convert snake_case or camelCase to Title Case with spaces
      return key
        .replace(/_/g, ' ')
        .replace(/([A-Z])/g, ' $1')
        .replace(/^./, str => str.toUpperCase());
    },

    async handleApprove(item) {
      try {
        const result = await this.$swal.fire({
          title: 'Approve Transaction?',
          text: 'This will approve the selected transaction',
          icon: 'question',
          showCancelButton: true,
          confirmButtonText: 'Yes, Approve',
          cancelButtonText: 'Cancel',
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          buttonsStyling: true
        });
        
        if (result.isConfirmed) {
          const response = await this.submitApproval(item, 1);
          if (response && response.status === 200) {
            await this.setWalletApprovals();
            this.$swal.fire({
              title: 'Success!',
              text: response.message || 'Transaction approved successfully',
              icon: 'success',
              confirmButtonColor: '#3085d6'
            });
          }
        }
      } catch (error) {
        this.$swal.fire({
          title: 'Error!',
          text: error.message || 'Failed to approve transaction',
          icon: 'error',
          confirmButtonColor: '#d33'
        });
      }
    },
    
    handleReject(item) {
      this.walletApproval = item;
      this.approve_payload.id = item.id;
      this.approve_payload.reason = '';
      this.showApproveModal = true;
    },
    
    async confirmReject() {
      try {
        this.showApproveModal = false;
        this.isLoading = true;
        await this.submitApproval(this.walletApproval, 3);
        await this.setWalletApprovals();
      } catch (error) {
        this.$swal.fire({
          title: 'Error!',
          text: error.message || 'Failed to reject transaction',
          icon: 'error',
          confirmButtonColor: '#d33'
        });
      }

      this.isLoading = false;
    },
    
    async submitApproval(item, status) {
      this.isLoading = true;
      const payload = {
        timestamp: Date.now(),
        id: item.id,
        status: status.toString(),
        reason: this.approve_payload.reason
      };
    
      try {
        const response = await this.updateWalletApprovals(payload);
        if (response.status === 200) {
          this.$swal.fire({
            title: 'Success!',
            text: response.message || 'Operation completed successfully',
            icon: 'success'
          });
        } else {
          throw new Error(response.message || 'Operation failed');
        }

        this.isLoading = false
        return response;
      } catch (error) {
        this.isLoading = false
        this.$swal.fire({
          title: 'Error!',
          text: error.message,
          icon: 'error'
        });
        throw error;
      }
    },
    
    // Utility methods
    copyToClipboard(text) {
      navigator.clipboard.writeText(text)
        .then(() => {
          this.$toast.success('Copied to clipboard!', {
            position: 'top-right',
            duration: 2000
          });
        })
        .catch(err => {
          console.error('Failed to copy: ', err);
          this.$toast.error('Failed to copy text', {
            position: 'top-right',
            duration: 2000
          });
        });
    },
    
    getAmountClass(amount) {
      if (amount <= 100) return 'amount-low';
      if (amount <= 250) return 'amount-medium-low';
      if (amount <= 500) return 'amount-medium';
      if (amount <= 1000) return 'amount-medium-high';
      if (amount <= 5000) return 'amount-high';
      return 'amount-very-high';
    },
    
    getStatusClass(status) {
      const statusMap = {
        '0': 'status-pending',
        '1': 'status-approved',
        '3': 'status-rejected'
      };
      
      return statusMap[status] || 'status-default';
    },
    
    getStatusLabel(status) {
      const statusMap = {
        '0': 'Pending',
        '1': 'Approved',
        '3': 'Rejected'
      };
      
      return statusMap[status] || 'Unknown Status';
    },
  },
}
</script>

<style scoped>
.badge-container {
  @apply flex justify-center;
}

.badge {
  @apply px-2 py-1 text-xs rounded cursor-pointer transition-colors;
}

.transaction-badge {
  @apply bg-blue-100 text-blue-800 hover:bg-blue-200;
}

.amount-badge {
  @apply bg-gray-100 text-gray-800 hover:bg-gray-200;
}

.amount-low {
  @apply bg-green-100 text-green-800 hover:bg-green-200;
}

.amount-medium-low {
  @apply bg-emerald-100 text-emerald-800 hover:bg-emerald-200;
}

.amount-medium {
  @apply bg-teal-100 text-teal-800 hover:bg-teal-200;
}

.amount-medium-high {
  @apply bg-yellow-100 text-yellow-800 hover:bg-yellow-200;
}

.amount-high {
  @apply bg-orange-100 text-orange-800 hover:bg-orange-200;
}

.amount-very-high {
  @apply bg-red-100 text-red-800 hover:bg-red-200;
}

.mobile-badge {
  @apply bg-indigo-100 text-indigo-800 hover:bg-indigo-200;
}

.date-badge {
  @apply bg-purple-100 text-purple-800 hover:bg-purple-200;
}

.source-badge {
  @apply bg-gray-100 text-gray-800 hover:bg-gray-200;
}

.status-badge {
  @apply font-medium;
}

.status-pending {
  @apply bg-yellow-100 text-yellow-800;
}

.status-approved {
  @apply bg-green-100 text-green-800;
}

.status-rejected {
  @apply bg-red-100 text-red-800;
}

.status-default {
  @apply bg-gray-100 text-gray-800;
}

.filter-card {
  @apply bg-white shadow-sm;
}
</style>
