<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Withdrawals" pageSubtitle="" />

    <!-- Filters Section -->
    <div class="px-4 mb-5">
      <div class="bg-white rounded-md shadow-sm mb-4">
        <div class="p-4 border-b">
          <h3 class="text-lg font-medium text-gray-700">Filters</h3>
        </div>

        <div class="p-4">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
            <!-- Transaction Identification Group -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">Transaction Identification</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Reference ID</label>
                  <input type="text" placeholder="Enter reference ID"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.client_unique_id"
                         @keyup.enter="applyFilters()">
                </div>
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Receipt Number</label>
                  <input type="text" placeholder="Enter receipt number"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.receipt_number"
                         @keyup.enter="applyFilters()">
                </div>
              </div>
            </div>

            <!-- Customer Group -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">Customer</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Phone Number</label>
                  <input type="text" placeholder="254XXXXXXXXX"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.mobile_number"
                         @keyup.enter="applyFilters()">
                </div>
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Recipient Name</label>
                  <input type="text" placeholder="Enter recipient name"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.receiver_name"
                         @keyup.enter="applyFilters()">
                </div>
              </div>
            </div>

            <!-- Transaction Details Group -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">Transaction Details</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Status</label>
                  <select
                    class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="moreParams.status"
                    @change="applyFilters()">
                    <option value="">All Statuses</option>
                    <option value="0">Pending</option>
                    <option value="1">Completed</option>
                    <option value="2">Processing</option>
                    <option value="3">Failed</option>
                    <option value="4">Cancelled</option>
                    <option value="5">Refunded</option>
                  </select>
                </div>
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Amount</label>
                  <input type="text" placeholder="Enter amount"
                         class="w-full px-2 py-1 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                         v-model="moreParams.withdraw_amount"
                         @keyup.enter="applyFilters()">
                </div>
              </div>
            </div>

            <!-- Date Range Group -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">Date</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Select Date Range</label>
                  <VueDatePicker
                    v-model="date"
                    range
                    multi-calendars
                    :enable-time-picker="false"
                    :format="'yyyy-MM-dd'"
                    :preset-ranges="presetRanges"
                    placeholder="Select date range"
                    class="w-full text-xs"
                    @update:model-value="selectDate"
                  />
                </div>
                <div class="flex justify-end pt-2">
                  <button @click="resetFilters()"
                          class="px-3 py-1 mr-2 bg-gray-200 text-gray-700 text-xs rounded-md hover:bg-gray-300 transition-colors">
                    Reset
                  </button>
                  <button @click="applyFilters()"
                          class="px-3 py-1 bg-indigo-600 text-white text-xs rounded-md hover:bg-indigo-700 transition-colors">
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Table -->
    <auto-table
      :headers="tableHeaders"
      :data="withdrawals"
      :loading="isLoading"
      :total-items="total"
      :items-per-page="limit"
      :current-page-prop="offset"
      :server-side-pagination="true"
      :pagination="total > limit"
      :show-items-count="true"
      @page-change="gotToPage"
    >

      <!-- Mobile Column -->
      <template #msisdn="{ item }">
        <div class="badge-container">
          <span
            class="badge mobile-badge"
            @click="copyToClipboard(formatPhoneNumber(item.msisdn))"
            title="Click to copy"
          >
            {{ formatPhoneNumber(item.msisdn) }}
          </span>
        </div>
      </template>
      <!-- Receipt Number Column -->
      <template #receipt_number="{ item }">
        <div class="badge-container">
          <span
            class="badge transaction-badge"
            @click="copyToClipboard(item.receipt_number || 'N/A')"
            title="Click to copy"
          >
            {{ item.receipt_number || 'N/A' }}
          </span>
        </div>
      </template>

      <!-- Amount Column -->
      <template #withdraw_amount="{ item }">
        <div class="badge-container flex-col items-start">
          <span
            class="badge amount-badge"
            :class="getAmountClass(parseFloat(item.withdraw_amount))"
            @click="copyToClipboard(item.currency + '. ' + formatNumber(item.withdraw_amount))"
            title="Click to copy"
          >
            {{ item.currency }}. {{ formatNumber(item.withdraw_amount) }}
          </span>
          <span
            class="badge charges-badge mt-1"
            @click="copyToClipboard('Charges: ' + formatNumber(item.charges))"
            title="Click to copy"
          >
            Charges: {{ formatNumber(item.charges) }}
          </span>
        </div>
      </template>

      <!-- Recipient Column -->
      <template #receiver_name="{ item }">
        <div class="badge-container">
          <span
            class="badge customer-badge"
            @click="copyToClipboard(item.receiver_name || 'N/A')"
            title="Click to copy"
          >
            {{ item.receiver_name || 'N/A' }}
          </span>
        </div>
      </template>

      <!-- Unique/Ref ID Column -->
      <template #client_unique_id="{ item }">
        <div class="badge-container">
          <span
            class="badge source-badge"
            @click="copyToClipboard(item.reference_id || 'N/A')"
            title="Click to copy"
          >
            {{ item.reference_id || 'N/A' }}
          </span>
        </div>
      </template>

      <!-- Status Column -->
      <template #result_code="{ item }">
        <div class="badge-container">
          <span
            class="badge status-badge"
            :class="getStatusClass(item.result_code)"
            @click="copyToClipboard(getStatusLabel(item.result_code))"
            title="Click to copy"
          >
            {{ getStatusLabel(item.result_code) }}
          </span>
        </div>
      </template>

      <!-- Date Column -->
      <template #created_at="{ item }">
        <div class="badge-container">
          <span
            class="badge date-badge"
            @click="copyToClipboard(moment(item.created_at).format('lll'))"
            title="Click to copy"
          >
            {{ moment(item.created_at).format('lll') }}
          </span>
        </div>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item, index }">
        <div class="relative z-50">
          <action-dropdown
            button-text="Actions"
            :show-text="false"
            button-class="z-50"
            menu-class="z-50 origin-top-right"
            :menu-width="48"
          >
            <action-item
              text="View Details"
              color="indigo"
              @click="viewDetails(item)"
            >
              <template #icon>
                <svg class="mr-3 h-5 w-5 text-indigo-500 group-hover:text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </template>
            </action-item>
          </action-dropdown>
        </div>
      </template>
    </auto-table>

    <!-- Details Modal -->
    <div v-if="viewModelOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div class="p-6">
          <!-- Modal Header -->
          <div class="border-b pb-4 mb-4">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="text-xl font-bold">Withdrawal Details</h3>
                <div class="mt-1 text-sm text-gray-600 flex flex-col space-y-2">
                  <div class="flex items-center">
                    <span class="font-medium w-32">Reference ID:</span>
                    <span
                      class="badge source-badge"
                      @click="copyToClipboard(withdrawal?.reference_id || 'N/A')"
                      title="Click to copy"
                    >
                      {{ withdrawal?.reference_id || 'N/A' }}
                    </span>
                  </div>
                  <div class="flex items-center">
                    <span class="font-medium w-32">Receipt Number:</span>
                    <span
                      class="badge transaction-badge"
                      @click="copyToClipboard(withdrawal?.receipt_number || 'N/A')"
                      title="Click to copy"
                    >
                      {{ withdrawal?.receipt_number || 'N/A' }}
                    </span>
                  </div>
                  <div class="flex items-center">
                    <span class="font-medium w-32">Mobile:</span>
                    <span
                      class="badge mobile-badge"
                      @click="copyToClipboard(formatPhoneNumber(withdrawal?.msisdn))"
                      title="Click to copy"
                    >
                      {{ formatPhoneNumber(withdrawal?.msisdn) }}
                    </span>
                  </div>
                </div>
              </div>
              <button @click="viewModelOpen = false" class="text-gray-500 hover:text-gray-700">
                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          <!-- Transaction Details -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <p class="text-sm text-gray-500">Recipient</p>
              <span
                class="badge customer-badge inline-block mt-1"
                @click="copyToClipboard(withdrawal?.receiver_name || 'N/A')"
                title="Click to copy"
              >
                {{ withdrawal?.receiver_name || 'N/A' }}
              </span>
            </div>
            <div>
              <p class="text-sm text-gray-500">Amount</p>
              <span
                class="badge amount-badge inline-block mt-1"
                :class="withdrawal ? getAmountClass(parseFloat(withdrawal.withdraw_amount)) : ''"
                @click="copyToClipboard(withdrawal?.currency + '. ' + formatNumber(withdrawal?.withdraw_amount))"
                title="Click to copy"
              >
                {{ withdrawal?.currency }}. {{ formatNumber(withdrawal?.withdraw_amount) }}
              </span>
              <span
                class="badge charges-badge inline-block mt-1 ml-2"
                @click="copyToClipboard('Charges: ' + formatNumber(withdrawal?.charges))"
                title="Click to copy"
              >
                Charges: {{ formatNumber(withdrawal?.charges) }}
              </span>
            </div>
            <div>
              <p class="text-sm text-gray-500">Status</p>
              <span
                class="badge status-badge inline-block mt-1"
                :class="withdrawal ? getStatusClass(withdrawal.result_code) : ''"
                @click="copyToClipboard(getStatusLabel(withdrawal?.result_code))"
                title="Click to copy"
              >
                {{ getStatusLabel(withdrawal?.result_code) }}
              </span>
            </div>
            <div>
              <p class="text-sm text-gray-500">Request Channel</p>
              <span
                class="badge source-badge inline-block mt-1"
                @click="copyToClipboard(withdrawal?.request_channel || 'N/A')"
                title="Click to copy"
              >
                {{ withdrawal?.request_channel || 'N/A' }}
              </span>
            </div>
            <div>
              <p class="text-sm text-gray-500">Created Date</p>
              <span
                class="badge date-badge inline-block mt-1"
                @click="copyToClipboard(withdrawal ? moment(withdrawal.created_at).format('llll') : 'N/A')"
                title="Click to copy"
              >
                {{ withdrawal ? moment(withdrawal.created_at).format('llll') : 'N/A' }}
              </span>
            </div>
            <div v-if="withdrawal?.transaction_completed_date">
              <p class="text-sm text-gray-500">Completed Date</p>
              <span
                class="badge date-badge inline-block mt-1"
                @click="copyToClipboard(withdrawal ? moment(withdrawal.transaction_completed_date).format('llll') : 'N/A')"
                title="Click to copy"
              >
                {{ withdrawal ? moment(withdrawal.transaction_completed_date).format('llll') : 'N/A' }}
              </span>
            </div>

            <!-- Additional Fields -->
            <div>
              <p class="text-sm text-gray-500">Response Code</p>
              <span
                class="badge source-badge inline-block mt-1"
                @click="copyToClipboard(withdrawal?.response_code || 'N/A')"
                title="Click to copy"
              >
                {{ withdrawal?.response_code || 'N/A' }}
              </span>
            </div>
            <div>
              <p class="text-sm text-gray-500">Response Description</p>
              <span
                class="badge description-badge inline-block mt-1"
                @click="copyToClipboard(withdrawal?.response_desc || 'N/A')"
                title="Click to copy"
              >
                {{ withdrawal?.response_desc || 'N/A' }}
              </span>
            </div>
            <div>
              <p class="text-sm text-gray-500">Result Description</p>
              <span
                class="badge description-badge inline-block mt-1"
                @click="copyToClipboard(withdrawal?.result_desc || 'N/A')"
                title="Click to copy"
              >
                {{ withdrawal?.result_desc || 'N/A' }}
              </span>
            </div>

            <div>
              <p class="text-sm text-gray-500">Source IP</p>
              <span
                class="badge mobile-badge inline-block mt-1"
                @click="copyToClipboard(withdrawal?.source_ip || 'N/A')"
                title="Click to copy"
              >
                {{ withdrawal?.source_ip || 'N/A' }}
              </span>
            </div>
            <div>
              <p class="text-sm text-gray-500">DLR IP</p>
              <span
                class="badge mobile-badge inline-block mt-1"
                @click="copyToClipboard(withdrawal?.dlr_ip || 'N/A')"
                title="Click to copy"
              >
                {{ withdrawal?.dlr_ip || 'N/A' }}
              </span>
            </div>
            <div>
              <p class="text-sm text-gray-500">Transaction Count</p>
              <span
                class="badge amount-badge inline-block mt-1"
                @click="copyToClipboard(withdrawal?.trx_count || 'N/A')"
                title="Click to copy"
              >
                {{ withdrawal?.trx_count || 'N/A' }}
              </span>
            </div>
            <div>
              <p class="text-sm text-gray-500">Retries</p>
              <span
                class="badge amount-badge inline-block mt-1"
                @click="copyToClipboard(withdrawal?.retries || '0')"
                title="Click to copy"
              >
                {{ withdrawal?.retries || '0' }}
              </span>
            </div>
            <div v-if="withdrawal?.conversation_id">
              <p class="text-sm text-gray-500">Conversation ID</p>
              <span
                class="badge transaction-badge inline-block mt-1"
                @click="copyToClipboard(withdrawal?.conversation_id)"
                title="Click to copy"
              >
                {{ withdrawal?.conversation_id }}
              </span>
            </div>
            <div v-if="withdrawal?.originator_conversation_id">
              <p class="text-sm text-gray-500">Originator Conversation ID</p>
              <span
                class="badge transaction-badge inline-block mt-1"
                @click="copyToClipboard(withdrawal?.originator_conversation_id)"
                title="Click to copy"
              >
                {{ withdrawal?.originator_conversation_id }}
              </span>
            </div>
            <div>
              <p class="text-sm text-gray-500">Org Utility Balance</p>
              <span
                class="badge amount-badge inline-block mt-1"
                @click="copyToClipboard(withdrawal?.currency + '. ' + formatNumber(withdrawal?.org_utility_balance))"
                title="Click to copy"
              >
                {{ withdrawal?.currency }}. {{ formatNumber(withdrawal?.org_utility_balance) }}
              </span>
            </div>
          </div>

          <!-- Extra Data Section -->
          <div v-if="withdrawal?.trxn_extra_data" class="mt-4">
            <h4 class="font-semibold text-lg mb-2">Additional Information</h4>

            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div v-for="(value, key) in extraDataValues" :key="key">
                  <div class="flex items-center space-x-2">
                    <div class="w-1/3">
                      <p class="text-sm text-gray-500">{{ formatKey(key) }}</p>
                    </div>
                    <div class="w-2/3">
                      <p
                        class="text-sm text-gray-700 cursor-pointer hover:text-indigo-600"
                        @click="copyToClipboard(value)"
                        title="Click to copy"
                      >
                        {{ value }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import {mapActions} from "vuex";
import { AutoTable, ActionDropdown, ActionItem, CustomLoading } from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';
import VueDatePicker from "@vuepic/vue-datepicker";
import '@vuepic/vue-datepicker/dist/main.css';
import {endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths} from "date-fns";

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
    VueDatePicker
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      withdrawal: null,
      trx_comp_date: "",
      org_util_bal: "",
      withdrawals: [],
      date: null,
      extraDataValues: {},

      tableHeaders: [
        { key: 'client_unique_id', label: 'Unique - Ref Id', align: 'center' },
        { key: 'msisdn', label: 'Mobile', align: 'center' },
        { key: 'receipt_number', label: 'Receipt No', align: 'center' },
        { key: 'withdraw_amount', label: 'Amount', align: 'center' },
        // { key: 'receiver_name', label: 'Recipient', align: 'center' },
        { key: 'result_code', label: 'Status', align: 'center' },
        { key: 'created_at', label: 'Date', align: 'center' },
        { key: 'actions', label: 'Actions', align: 'center' },
      ],

      moreParams: {
        status: "",
        mobile_number: "",
        start: "",
        end: "",
        page: "1",
        timestamp: Date.now().toString(),
        skip_cache: "",
        limit: "100",
        withdraw_desc: "",
        client_unique_id: "",
        withdraw_amount: "",
        receipt_number: "",
      },

      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
      ],
    }
  },
  mounted() {
    this.setWithdrawals();
  },
  methods: {
    ...mapActions(["getWithdrawals", "toggleSideMenu"]),

    toggleSideM() {
      this.toggleSideMenu();
    },

    goBack() {
      this.$router.go(-1);
    },

    formatPhoneNumber(phone) {
      if (!phone) return 'N/A';
      return phone.startsWith('+') ? phone : `+${phone}`;
    },

    formatNumber(value) {
      if (!value) return '0.00';
      return parseFloat(value).toFixed(2);
    },

    getStatusClass(status) {
      const statusMap = {
        '0': 'status-active',    // Successful - green
        '500': 'status-inactive' // Failed - red
      };

      return statusMap[status] || 'status-default';
    },

    getStatusLabel(status) {
      const statusMap = {
        '0': 'Successful',
        '500': 'Failed'
      };

      return statusMap[status] || 'Unknown';
    },

    async selectDate() {
      // If date is null (cleared), reset date filters and fetch data
      if (!this.date) {
        console.log('Date filter cleared, resetting and fetching data...');
        this.moreParams.start = '';
        this.moreParams.end = '';
        this.applyFilters();
        return;
      }

      // If date range is incomplete, return without doing anything
      if (!Array.isArray(this.date) || this.date.length !== 2 || !this.date[0] || !this.date[1]) return;

      // Update date filter values
      this.moreParams.start = this.formatDate(this.date[0]);
      this.moreParams.end = this.formatDate(this.date[1]);

      // Log that date filter was updated and fetch data
      console.log('Date filter updated, fetching data with new date range...');
      this.applyFilters();
    },

    formatDate(date) {
      const d = new Date(date);
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const year = d.getFullYear();

      return [year, month, day].join('-');
    },

    applyFilters() {
      this.moreParams.page = '1';
      this.offset = 1;
      this.setWithdrawals();
    },

    resetFilters() {
      this.date = null;
      this.moreParams = {
        status: "",
        mobile_number: "",
        start: "",
        end: "",
        page: "1",
        timestamp: Date.now().toString(),
        skip_cache: "",
        limit: "10",
        withdraw_desc: "",
        client_unique_id: "",
        withdraw_amount: "",
        receipt_number: "",
      };
      this.offset = 1;
      this.setWithdrawals();
    },

    // Pagination
    gotToPage(page) {
      this.moreParams.page = page;
      this.offset = page;
      this.setWithdrawals();
    },

    async setWithdrawals() {
      this.isLoading = true;
      this.moreParams.timestamp = Date.now().toString();

      try {
        const params = new URLSearchParams();
        for (const key in this.moreParams) {
          if (this.moreParams.hasOwnProperty(key)) {
            params.append(key, this.moreParams[key]);
          }
        }
        const queryString = params.toString();

        const response = await this.getWithdrawals(queryString);

        if (response && response.status === 200) {
          this.withdrawals = response.message.result || [];
          this.total = parseInt(response.message.record_count || 0);
          this.showDropdown = Array(this.withdrawals.length).fill(false);
        } else {
          this.withdrawals = [];
          this.total = 0;
          this.showDropdown = [];
        }
      } catch (error) {
        console.error("Error fetching withdrawals:", error);
        this.withdrawals = [];
        this.total = 0;
      } finally {
        this.isLoading = false;
      }
    },

    viewDetails(item) {
      this.viewModelOpen = true;
      this.withdrawal = item;

      if (this.withdrawal != null) {
        this.trx_comp_date = moment(this.withdrawal.transaction_completed_date).format('lll');
        this.org_util_bal = this.withdrawal.currency + ". " + this.formatNumberWithCommas(this.withdrawal.org_utility_balance ?? 0);
        this.parseExtraData();
      }
    },

    parseExtraData() {
      this.extraDataValues = {};
      if (this.withdrawal && this.withdrawal.trxn_extra_data) {
        try {
          const extraData = typeof this.withdrawal.trxn_extra_data === 'string'
            ? JSON.parse(this.withdrawal.trxn_extra_data)
            : this.withdrawal.trxn_extra_data;

          this.extraDataValues = extraData;
        } catch (error) {
          console.error("Error parsing trxn_extra_data:", error);
        }
      }
    },

    formatKey(key) {
      return key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1');
    },

    formatNumberWithCommas(number) {
      return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    copyToClipboard(text) {
      navigator.clipboard.writeText(text)
        .then(() => {
          this.$toast.success('Copied to clipboard!', {
            position: 'top-right',
            duration: 2000
          });
        })
        .catch(err => {
          console.error('Failed to copy: ', err);
          this.$toast.error('Failed to copy text', {
            position: 'top-right',
            duration: 2000
          });
        });
    },
    getAmountClass(amount) {
      if (amount <= 100) return 'amount-low';
      if (amount <= 250) return 'amount-medium-low';
      if (amount <= 500) return 'amount-medium';
      if (amount <= 1000) return 'amount-medium-high';
      if (amount <= 5000) return 'amount-high';
      return 'amount-very-high';
    },
    getStatusClass(status) {
      const statusMap = {
        '0': 'status-active',    // Successful - green
        '500': 'status-inactive' // Failed - red
      };

      return statusMap[status] || 'status-default';
    },
  }
}
</script>

<style scoped>

/* Charges badge */
.charges-badge {
  background-color: #FFAB40; /* Light orange */
  color: #333;
  font-size: 0.75rem;
  min-width: 100px;
  text-align: center;
  padding: 4px 8px;
  border-radius: 16px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.charges-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

/* Date picker styles */
:deep(.dp__input) {
  padding: 0.25rem 2rem;
  font-size: 0.75rem;
  line-height: 1rem;
  border-radius: 0.375rem;
}

:deep(.dp__main) {
  font-size: 0.75rem;
}

</style>
