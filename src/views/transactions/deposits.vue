<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Deposits" pageSubtitle="" />

    <!-- Filters Section -->
    <div class="px-4 mb-5">
      <div class="bg-white rounded-md shadow-sm mb-4">
        <div class="p-4 border-b">
          <h3 class="text-lg font-medium text-gray-700">Filters</h3>
        </div>

        <div class="p-4">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-3">
            <!-- Transaction Identification Group -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">Transaction Identification</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs font-bold text-gray-700 mb-1">M-Pesa Code</label>
                  <input
                    type="text"
                    placeholder="Enter M-Pesa code"
                    class="w-full px-2 py-1.5 text-xs text-gray-700 border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="moreParams.trxn_code"
                    @keyup.enter="applyFilters()"
                  >
                </div>
                <div>
                  <label class="block text-xs font-bold text-gray-700 mb-1">Account Number</label>
                  <input
                    type="text"
                    placeholder="Enter account number"
                    class="w-full px-2 py-1.5 text-xs text-gray-700 border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="moreParams.account_number"
                    @keyup.enter="applyFilters()"
                  >
                </div>
              </div>
            </div>

            <!-- Customer Group -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">Customer</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs font-bold text-gray-700 mb-1">Mobile Number</label>
                  <input
                    type="text"
                    placeholder="254XXXXXXXXX"
                    class="w-full px-2 py-1.5 text-xs text-gray-700 border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="moreParams.mobile_number"
                    @keyup.enter="applyFilters()"
                  >
                </div>
                <div>
                  <label class="block text-xs font-bold text-gray-700 mb-1">Amount</label>
                  <input
                    type="text"
                    placeholder="Enter amount"
                    class="w-full px-2 py-1.5 text-xs text-gray-700 border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    v-model="moreParams.amount"
                    @keyup.enter="applyFilters()"
                  >
                </div>
              </div>
            </div>

            <!-- Date Range Group -->
            <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
              <h4 class="text-sm font-medium text-gray-700 mb-1">Date</h4>
              <div class="space-y-2">
                <div>
                  <label class="block text-xs text-gray-600 mb-1">Select Date Range</label>
                  <VueDatePicker
                    v-model="date"
                    range
                    multi-calendars
                    :enable-time-picker="false"
                    :format="'yyyy-MM-dd'"
                    :preset-ranges="presetRanges"
                    placeholder="Select date range"
                    class="w-full text-xs"
                    @update:model-value="selectDate"
                  />
                </div>
                <div class="flex justify-end pt-2">
                  <button
                    @click="resetFilters()"
                    class="px-3 py-1 mr-2 bg-gray-200 text-gray-700 text-xs rounded-md hover:bg-gray-300 transition-colors"
                  >
                    Reset
                  </button>
                  <button
                    @click="applyFilters()"
                    class="px-3 py-1 bg-indigo-600 text-white text-xs rounded-md hover:bg-indigo-700 transition-colors"
                  >
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Table -->
    <auto-table
      :headers="tableHeaders"
      :data="deposits"
      :loading="isLoading"
      :total-items="total"
      :items-per-page="limit"
      :current-page-prop="offset"
      :server-side-pagination="true"
      :pagination="total > limit"
      :show-items-count="true"
      @page-change="gotToPage"
    >
      <!-- Transaction Code Column -->
      <template #trxn_code="{ item }">
        <div class="badge-container">
          <span
            class="badge transaction-badge"
            @click="copyToClipboard(item.trxn_code)"
            title="Click to copy"
          >
            {{ item.trxn_code }}
          </span>
        </div>
      </template>

      <!-- Customer Column -->
      <template #trxn_sender="{ item }">
        <div>
          <span>{{ item.trxn_sender || 'N/A' }}</span>
        </div>
      </template>

      <!-- Mobile Column -->
      <template #trxn_msisdn="{ item }">
        <div class="badge-container">
          <span
            class="badge mobile-badge"
            @click="copyToClipboard(formatPhoneNumber(item.trxn_msisdn))"
            title="Click to copy"
          >
            {{ formatPhoneNumber(item.trxn_msisdn) }}
          </span>
        </div>
      </template>

      <!-- Amount Column -->
      <template #trxn_amount="{ item }">
        <div class="badge-container">
          <span
            class="badge amount-badge"
            :class="getAmountClass(parseFloat(item.trxn_amount))"
            @click="copyToClipboard(parseFloat(item.trxn_amount).toFixed(2))"
            title="Click to copy"
          >
            KES. {{ parseFloat(item.trxn_amount).toFixed(2) }}
          </span>
        </div>
      </template>

      <!-- Account Column -->
      <template #trxn_account="{ item }">
        <div class="badge-container">
          <span
            class="badge account-badge"
            @click="copyToClipboard(item.trxn_account)"
            title="Click to copy"
          >
            {{ item.trxn_account }}
          </span>
        </div>
      </template>

      <!-- Transaction Type Column -->
      <template #trxn_repayment_type="{ item }">
        <div>
          <span>
            {{ item.trxn_repayment_type }}
          </span>
        </div>
      </template>

      <!-- Date Column -->
      <template #created_at="{ item }">
        <div class="badge-container">
          <span class="badge date-badge">{{ moment(item.created_at).format('llll') }}</span>
        </div>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item, index }">
        <div class="relative z-50">
          <action-dropdown
            button-text="Actions"
            :show-text="false"
            button-class="z-50"
            menu-class="z-50 origin-top-right"
            :menu-width="48"
          >
            <action-item
              text="View Details"
              color="indigo"
              @click="viewDetails(item)"
            >
              <template #icon>
                <svg class="mr-3 h-5 w-5 text-indigo-500 group-hover:text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </template>
            </action-item>
          </action-dropdown>
        </div>
      </template>
    </auto-table>

    <!-- Details Modal -->
    <div v-if="viewModelOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div class="p-6">
          <!-- Modal Header -->
          <div class="border-b pb-4 mb-4">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="text-xl font-bold">Deposit Details</h3>
                <div class="mt-1 text-sm text-gray-600 flex flex-col space-y-2">
                  <div class="flex items-center">
                    <span class="font-medium w-32">Transaction Code:</span>
                    <span
                      class="badge transaction-badge"
                      @click="copyToClipboard(deposit?.trxn_code)"
                      title="Click to copy"
                    >
                      {{ deposit?.trxn_code }}
                    </span>
                  </div>
                  <div class="flex items-center">
                    <span class="font-medium w-32">Mobile:</span>
                    <span
                      class="badge mobile-badge"
                      @click="copyToClipboard(formatPhoneNumber(deposit?.trxn_msisdn))"
                      title="Click to copy"
                    >
                      {{ formatPhoneNumber(deposit?.trxn_msisdn) }}
                    </span>
                  </div>
                  <div class="flex items-center">
                    <span class="font-medium w-32">Account:</span>
                    <span
                      class="badge account-badge"
                      @click="copyToClipboard(deposit?.trxn_account)"
                      title="Click to copy"
                    >
                      {{ deposit?.trxn_account }}
                    </span>
                  </div>
                </div>
              </div>
              <button @click="viewModelOpen = false" class="text-gray-500 hover:text-gray-700">
                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          <!-- Transaction Details -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <p class="text-sm text-gray-500">Sender</p>
              <p class="font-bold">{{ deposit?.trxn_sender || 'N/A' }}</p>
            </div>
            <div>
              <p class="text-sm text-gray-500">Amount</p>
              <span
                class="badge amount-badge inline-block mt-1"
                :class="deposit ? getAmountClass(parseFloat(deposit.trxn_amount)) : ''"
                @click="copyToClipboard(deposit ? parseFloat(deposit.trxn_amount).toFixed(2) : '0.00')"
                title="Click to copy"
              >
                KES. {{ deposit ? parseFloat(deposit.trxn_amount).toFixed(2) : '0.00' }}
              </span>
            </div>
            <div>
              <p class="text-sm text-gray-500">Transaction Type</p>
              <p class="font-bold">{{ deposit?.trxn_repayment_type || 'N/A' }}</p>
            </div>
            <div>
              <p class="text-sm text-gray-500">Date</p>
              <p class="font-bold">{{ deposit ? moment(deposit.created_at).format('llll') : 'N/A' }}</p>
            </div>
          </div>

          <!-- Extra Data Section -->
          <div v-if="deposit?.trxn_extra_data" class="mt-4">
            <h4 class="font-semibold text-lg mb-2">Additional Information</h4>

            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div v-for="(value, key) in extraDataValues" :key="key">
                  <div class="flex items-center space-x-2">
                    <div class="w-1/3">
                      <p class="text-sm text-gray-500">{{ formatKey(key) }}</p>
                    </div>
                    <div class="w-2/3">
                      <p class="text-sm text-gray-700">{{ value }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import {mapActions} from "vuex";
import { AutoTable, ActionDropdown, ActionItem, CustomLoading } from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';
import VueDatePicker from "@vuepic/vue-datepicker";
import '@vuepic/vue-datepicker/dist/main.css';
import {endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths} from "date-fns";

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
    VueDatePicker
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      deposit: null,
      extraDataValues: {},
      deposits: [],
      date: null,

      tableHeaders: [
        { key: 'trxn_code', label: 'M-Pesa Code', align: 'center' },
        // { key: 'trxn_sender', label: 'Sender', align: 'center' },
        { key: 'trxn_msisdn', label: 'Mobile', align: 'center' },
        { key: 'trxn_account', label: 'Account', align: 'center' },
        { key: 'trxn_amount', label: 'Amount', align: 'center' },
        { key: 'trxn_repayment_type', label: 'Trx Type', align: 'center' },
        { key: 'created_at', label: 'Date', align: 'left' },
        { key: 'actions', label: 'Actions', align: 'center' }
      ],

      moreParams: {
        trxn_code: '',
        account_number: '',
        mobile_number: '',
        amount: '',
        start: '',
        end: '',
        page: '1',
        limit: "100",
        timestamp: Date.now().toString(),
        skip_cache: '',
      },

      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
      ],
    }
  },
  mounted() {
    this.setDeposits();
  },
  methods: {
    ...mapActions(["getDeposits", "toggleSideMenu", "repostDeposit"]),

    toggleSideM() {
      this.toggleSideMenu();
    },

    goBack() {
      this.$router.go(-1);
    },

    formatPhoneNumber(phone) {
      if (!phone) return 'N/A';
      return phone.startsWith('+') ? phone : `+${phone}`;
    },

    getTransactionTypeClass(type) {
      if (!type) return 'type-default';

      const typeMap = {
        'OD': 'type-overdraft',
        'Overdraft': 'type-overdraft',
        'Payment': 'type-payment',
        'Deposit': 'type-deposit',
        'Refund': 'type-refund'
      };

      for (const [key, value] of Object.entries(typeMap)) {
        if (type.includes(key)) return value;
      }

      return 'type-default';
    },

    async selectDate() {
      // If date is null (cleared), reset date filters and fetch data
      if (!this.date) {
        console.log('Date filter cleared, resetting and fetching data...');
        this.moreParams.start = '';
        this.moreParams.end = '';
        this.applyFilters();
        return;
      }

      // If date range is incomplete, return without doing anything
      if (!Array.isArray(this.date) || this.date.length !== 2 || !this.date[0] || !this.date[1]) return;

      // Update date filter values
      this.moreParams.start = this.formatDate(this.date[0]);
      this.moreParams.end = this.formatDate(this.date[1]);

      // Log that date filter was updated and fetch data
      console.log('Date filter updated, fetching data with new date range...');
      this.applyFilters();
    },

    formatDate(date) {
      const d = new Date(date);
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const year = d.getFullYear();

      return [year, month, day].join('-');
    },

    applyFilters() {
      this.moreParams.page = '1';
      this.offset = 1;
      this.setDeposits();
    },

    resetFilters() {
      this.date = null;
      this.moreParams = {
        trxn_code: '',
        account_number: '',
        mobile_number: '',
        amount: '',
        start: '',
        end: '',
        page: '1',
        limit: "100",
        timestamp: Date.now().toString(),
        skip_cache: '',
      };
      this.offset = 1;
      this.setDeposits();
    },

    // Pagination
    gotToPage(page) {
      this.moreParams.page = page;
      this.offset = page;
      this.setDeposits();
    },

    async setDeposits() {
      this.isLoading = true;
      this.moreParams.timestamp = Date.now().toString();

      try {
        const params = new URLSearchParams();
        for (const key in this.moreParams) {
          if (this.moreParams.hasOwnProperty(key)) {
            params.append(key, this.moreParams[key]);
          }
        }
        const queryString = params.toString();

        const response = await this.getDeposits(queryString);

        if (response && response.status === 200) {
          this.deposits = response.message.result || [];
          this.total = parseInt(response.message.record_count || 0);
          this.showDropdown = Array(this.deposits.length).fill(false);
        } else {
          this.deposits = [];
          this.total = 0;
          this.showDropdown = [];
        }
      } catch (error) {
        console.error("Error fetching deposits:", error);
        this.deposits = [];
        this.total = 0;
      } finally {
        this.isLoading = false;
      }
    },

    viewDetails(item) {
      this.viewModelOpen = true;
      this.deposit = item;
      this.parseExtraData();
    },

    parseExtraData() {
      this.extraDataValues = {};
      if (this.deposit && this.deposit.trxn_extra_data) {
        try {
          const extraData = typeof this.deposit.trxn_extra_data === 'string'
            ? JSON.parse(this.deposit.trxn_extra_data)
            : this.deposit.trxn_extra_data;

          this.extraDataValues = extraData;
        } catch (error) {
          console.error("Error parsing trxn_extra_data:", error);
        }
      }
    },

    formatKey(key) {
      return key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1');
    },

    copyToClipboard(text) {
      navigator.clipboard.writeText(text)
        .then(() => {
          this.$toast.success('Copied to clipboard!', {
            position: 'top-right',
            duration: 2000
          });
        })
        .catch(err => {
          console.error('Failed to copy: ', err);
          this.$toast.error('Failed to copy text', {
            position: 'top-right',
            duration: 2000
          });
        });
    },

    getAmountClass(amount) {
      if (amount <= 100) return 'amount-low';
      if (amount <= 250) return 'amount-medium-low';
      if (amount <= 500) return 'amount-medium';
      if (amount <= 1000) return 'amount-medium-high';
      if (amount <= 5000) return 'amount-high';
      return 'amount-very-high';
    },
  }
}
</script>

<style scoped>

/* Date picker styles */
:deep(.dp__input) {
  padding: 0.25rem 2rem;
  font-size: 0.75rem;
  line-height: 1rem;
  border-radius: 0.375rem;
}

:deep(.dp__main) {
  font-size: 0.75rem;
}

/* Transaction type styles */
.type-overdraft {
  color: rgb(220, 38, 38);
}

.type-payment {
  color: rgb(37, 99, 235);
}

.type-deposit {
  color: rgb(22, 163, 74);
}

.type-refund {
  color: rgb(147, 51, 234);
}

.type-default {
  color: rgb(75, 85, 99);
}
</style>
