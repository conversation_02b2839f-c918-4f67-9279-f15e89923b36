<template>
   <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Dashboard" pageSubtitle="" />

    <div class="p-2 sm:p-4">
      <div class="filter-card p-2 sm:p-3 mb-4 border rounded-md hover:border-indigo-300 transition-colors">
            <h4 class="text-sm font-medium text-gray-700 mb-1">Date</h4>
            <div class="space-y-2">
              <div>
                <label class="block text-xs text-gray-600 mb-1">Select Date Range</label>
                <VueDatePicker
                    v-model="date"
                    range
                    :multi-calendars="!isMobile"
                    :enable-time-picker="false"
                    :format="'yyyy-MM-dd'"
                    :preset-ranges="presetRanges"
                    placeholder="Select date range"
                    class="w-full text-xs responsive-datepicker"
                    @change="selectDate"
                    @update:model-value="selectDate"
                  />
              </div>

            </div>
          </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Profiles Card -->
        <div class="rounded-lg shadow p-6 border border-gray-100 bg-indigo-100 text-indigo-800">
          <h3 class="text-lg font-semibold mb-2">CUSTOMERS</h3>
          <h3 class="text-md font-semibold mb-2">New: {{ profileData.new_profiles || 0 }} </h3>
          <h3 class="text-sm mb-2">Total: {{ profileData.total_profiles || 0 }} </h3>
        </div>

        <!-- Existing Cards -->
        <div v-for="item in sortedSummary" :key="item.identifier_name" :class="['rounded-lg shadow p-6 border border-gray-100', getIdentifierColor(item.identifier_name)]">
          <h3 class="text-lg font-semibold mb-2">
            {{ item.identifier_name === 'PAYOUTS' ? 'WINS' : item.identifier_name }}
          </h3>
          <h3 class="text-md font-semibold mb-2">
            {{ item.currency?? 'KES. ' }} {{ formatAmount(item.total_amount) }}
          </h3>
          <!-- <div class="mb-2 border-t  border-gray-800 pt-2">
            <div class="flex justify-between text-sm">

            </div>
            <div class="flex flex-wrap text-xs text-gray-600">
              <span class="mr-4">Total Customers: {{ item.total_players }}</span>
              <span class="mr-4 ">Unique Customers: {{ item.total_unique_players }}</span>
            </div>
          </div> -->
        </div>

        <!-- Liabilities Card -->
        <div class="rounded-lg shadow p-6 border border-gray-100 bg-gray-100 text-gray-800">
          <h3 class="text-lg font-semibold mb-2">LIABILITIES</h3>
          <h5 class="text-md font-semibold mb-2">KES. {{ formatAmount(liabilities.total_balance) }}</h5>
          <h5 class="text-sm mb-2">BONUS: KES. {{ formatAmount(liabilities.total_bonus) }}</h5>
        </div>

      </div>
    </div>


  </div>
</template>

<script>
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';
import { mapActions } from "vuex";
import VueDatePicker from "@vuepic/vue-datepicker";
import { endOfMonth, startOfMonth, subMonths } from "date-fns";


export default {
  name: "DashboardDemo",
    components:{
      CustomLoading,
      PageHeader,
      VueDatePicker,
    },
  data() {
    return {
      indentifier_names: [
        "DEPOSIT",
        "WITHDRAW",
        "STAKE",
        "PAYOUTS",
        "BONUS_STAKE",
        "FREEBET_STAKE",
        "REFUNDS",
        "WITHDRAW_CHARGES",
        "TRANSFER",
        "BONUS",
        "REVERSAL",
        "VOID",
      ],
      summary: [],
      summary_averages: [],
      groupedSummary: {},
      groupedAverages: {},
      isLoading: false,
      params: {
        start: '',
        end: '',
        timestamp: Date.now()
      },
      windowWidth: window.innerWidth,
      date: null,
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'Yesterday', range: [new Date(Date.now() - 86400000), new Date(Date.now() - 86400000)]},
        {label: 'Last 7 days', range: [new Date(Date.now() - 604800000), new Date()]},
        {label: 'Last 14 days', range: [new Date(Date.now() - 1209600000), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        // {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        // {
        //   label: 'This year (slot)',
        //   range: [startOfYear(new Date()), endOfYear(new Date())],
        //   slot: 'yearly',
        // },
      ],
      profileData: {
        total_profiles: "0",
        new_profiles: "0"
      },
      liabilities: {
        total_balance: "0",
        total_bonus: "0",
        total_freebet: "0"
      }
    }
  },
  created() {
    this.params.start =  this.formatDate(Date.now());
    this.params.end = this.formatDate( Date.now());
    this.fetchSummaryData();
  },
  mounted() {
    // Add window resize listener for responsive behavior
    window.addEventListener('resize', this.handleResize);
  },
  beforeUnmount() {
    // Clean up event listener
    window.removeEventListener('resize', this.handleResize);
  },
  computed: {
    isMobile() {
      return this.windowWidth <= 768;
    },
    sortedSummary() {
      // Create a complete list with all identifiers, even those with no data
      const completeList = [...this.summary];

      // Add missing identifiers with zero values
      this.indentifier_names.forEach(name => {
        if (!completeList.some(item => item.identifier_name === name)) {
          completeList.push({
            identifier_name: name,
            total_amount: 0,
            currency: this.summary.length > 0 ? this.summary[0].currency : 'KES',
            total_players: 0,
            total_unique_players: 0
          });
        }
      });

      // Sort by the order in indentifier_names
      return completeList.sort((a, b) => {
        const indexA = this.indentifier_names.indexOf(a.identifier_name);
        const indexB = this.indentifier_names.indexOf(b.identifier_name);
        return indexA - indexB;
      });
    }
  },
  methods: {
    ...mapActions(["getDashSummaryReports"]),

    // Handle window resize for responsive behavior
    handleResize() {
      this.windowWidth = window.innerWidth;
    },

    // Fetch summary data
    async fetchSummaryData() {
      this.isLoading = true;
      this.params.timestamp = Date.now();
      const response = await this.getDashSummaryReports(this.params);
      if (response && response.status === 200) {
        this.summary = response.message.trxn_summary || [];
        this.summary_averages = response.message.trxn_summary_averages || [];
        this.profileData = response.message.summary_profiles || {
          total_profiles: "0",
          new_profiles: "0"
        };

        this.liabilities = response.message.summary_profiles.liabilities[0] || {
          total_balance: "0",
          total_bonus: "0",
          total_freebet: "0"
        };
      } else {
        this.summary = [];
        this.summary_averages = [];
        this.profileData = {
          total_profiles: "0",
          new_profiles: "0"
        };
      }
      this.isLoading = false;
    },

    // Select date range
    selectDate(date) {
      // If date is null (cleared), reset date filters and fetch data
      if (!date) {
        console.log('Date filter cleared, resetting and fetching data...');
        this.date = null;
        this.params.start = this.formatDate(Date.now());
        this.params.end = this.formatDate(Date.now());
        this.fetchSummaryData();
        return;
      }

      this.date = date;

      // If date range is incomplete, return without doing anything
      if (!date[0]) return;

      // Update date filter values
      this.params.start = this.formatDate(date[0]);
      this.params.end = this.formatDate(date[1] ?? date[0]);

      // Log that date filter was updated and fetch data
      console.log('Date filter updated, fetching data with new date range...');
      this.fetchSummaryData();
    },

    formatDate(date) {
      const d = new Date(date);
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const year = d.getFullYear();

      return [year, month, day].join('-');
    },


    formatAmount(val) {
      if (!val) return "0.00";
      return Number(val).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});
    },

    // Get identifier color
    getIdentifierColor(identifier) {
      const colorMap = {
        'DEPOSIT': 'bg-green-100 text-green-800',
        'STAKE': 'bg-cyan-100 text-cyan-800',
        'PAYOUTS': 'bg-yellow-100 text-yellow-800',
        'REFUNDS': 'bg-orange-100 text-orange-800',
        'WITHDRAW_CHARGES': 'bg-amber-50 text-amber-700',
        'BONUS_STAKE': 'bg-blue-100 text-blue-800',
        'FREEBET_STAKE': 'bg-indigo-100 text-indigo-800',
        'WITHDRAW': 'bg-purple-100 text-purple-800',
        'TRANSFER': 'bg-teal-100 text-teal-800',
        'BONUS': 'bg-pink-100 text-pink-800',
        'REVERSAL': 'bg-red-100 text-red-800',
        'VOID': 'bg-gray-100 text-gray-800',
        'LIABILITIES': 'bg-gray-100 text-gray-800'
      };

      return colorMap[identifier] || 'bg-gray-100 text-gray-800';
    },
  }
}
</script>

<style scoped>
.dashboard-demo { font-family: 'Inter', sans-serif; }

/* Responsive Date Picker Styles */
.responsive-datepicker :deep(.dp__input) {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  line-height: 1rem;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
  width: 100%;
}

.responsive-datepicker :deep(.dp__input:hover) {
  border-color: #a5b4fc;
}

.responsive-datepicker :deep(.dp__input:focus) {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 1px rgba(99, 102, 241, 0.2);
}

.responsive-datepicker :deep(.dp__main) {
  font-size: 0.75rem;
}

.responsive-datepicker :deep(.dp__preset_ranges) {
  font-size: 0.75rem;
}

.responsive-datepicker :deep(.dp__action_buttons) {
  font-size: 0.75rem;
}

/* Mobile responsive improvements */
@media (max-width: 768px) {
  .filter-card {
    margin: 0.5rem;
    padding: 0.75rem !important;
  }

  .responsive-datepicker :deep(.dp__input) {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }

  .responsive-datepicker :deep(.dp__main) {
    font-size: 0.875rem;
  }

  .responsive-datepicker :deep(.dp__menu) {
    max-width: 100vw;
    margin: 0 0.5rem;
  }

  .responsive-datepicker :deep(.dp__preset_ranges) {
    font-size: 0.75rem;
    max-height: 200px;
    overflow-y: auto;
  }

  .responsive-datepicker :deep(.dp__calendar_header) {
    padding: 0.5rem;
  }

  .responsive-datepicker :deep(.dp__calendar_item) {
    padding: 0.25rem;
    font-size: 0.75rem;
  }

  /* Ensure proper spacing for mobile cards */
  .grid {
    gap: 1rem;
  }
}

/* Tablet responsive improvements */
@media (min-width: 769px) and (max-width: 1024px) {
  .responsive-datepicker :deep(.dp__input) {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }

  .responsive-datepicker :deep(.dp__main) {
    font-size: 0.8125rem;
  }
}

/* Ensure date picker overlay doesn't get clipped */
.responsive-datepicker :deep(.dp__menu_wrap) {
  z-index: 9999;
}

.responsive-datepicker :deep(.dp__overlay) {
  z-index: 9998;
}
</style>
