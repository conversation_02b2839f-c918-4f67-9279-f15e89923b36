<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Summary" pageSubtitle="Averages" />

      <div class="filter-card m-4 p-4 mb-4 border rounded-md hover:border-indigo-300 transition-colors">
        <h4 class="text-sm font-medium text-gray-700 mb-1">Date</h4>
          <div>
            <label class="block text-xs text-gray-600 mb-1">Select Date Range</label>
            <VueDatePicker
                    v-model="date"
                    range
                    multi-calendars
                    :enable-time-picker="false"
                    :format="'yyyy-MM-dd'"
                    :preset-ranges="presetRanges"
                    placeholder="Select date range"
                    class="w-full text-xs"
                    @change="selectDate"
                    @update:model-value="selectDate"
                  />
          </div>
      </div>

        <auto-table
          :headers="tableHeaders"
          :data="summaryAverages"
          :loading="isLoading"
          :total-items="total"
          :items-per-page="limit"
          :current-page-prop="offset"
          :server-side-pagination="true"
          :pagination="total > limit"
          :show-items-count="true"
          :items-per-page-options="[10, 25, 50, 100]"
          @page-change="handlePageChange"
          @items-per-page-change="handleLimitChange"
        >
          <!-- Source badge -->
          <template #source="{ item }">
            <div class="badge-container">
              <span class="badge" :class="getSourceClass(item.source)">
                <span class="source-text">{{ formatSourceText(item.source) }}</span>
              </span>
            </div>
          </template>

          <!-- Identifier badge -->
          <template #identifier_name="{ item }">
            <div class="badge-container">
              <span class="badge identifier-badge" :class="getIdentifierClass(item.identifier_name)">
                {{ item.identifier_name }}
              </span>
            </div>
          </template>

          <!-- Currency values with amount badges -->
          <template #average_stake="{ item }">
            <div class="badge-container">
              <span class="badge amount-badge" :class="getAmountClass(item.average_stake)">
                {{ item.currency || 'KES' }} {{ formatAmount(item.average_stake) }}
              </span>
            </div>
          </template>

          <template #average_minimum_stake="{ item }">
            <div class="badge-container">
              <span class="badge amount-badge" :class="getAmountClass(item.average_minimum_stake)">
                {{ item.currency || 'KES' }} {{ formatAmount(item.average_minimum_stake) }}
              </span>
            </div>
          </template>

          <template #average_maximum_stake="{ item }">
            <div class="badge-container">
              <span class="badge amount-badge" :class="getAmountClass(item.average_maximum_stake)">
                {{ item.currency || 'KES' }} {{ formatAmount(item.average_maximum_stake) }}
              </span>
            </div>
          </template>

          <template #avg_payout_per_player="{ item }">
            <div class="badge-container">
              <span class="badge amount-badge" :class="getAmountClass(item.avg_payout_per_player)">
                {{ item.currency || 'KES' }} {{ formatAmount(item.avg_payout_per_player) }}
              </span>
            </div>
          </template>

          <!-- Bet count badge -->
          <template #avg_bet_count_per_player="{ item }">
            <div class="badge-container">
              <span class="badge bet-count-badge">
                {{ item.avg_bet_count_per_player }}
              </span>
            </div>
          </template>

          <!-- Date Column -->
          <template #updated_at="{ item }">
              <span>{{ moment(item.updated_at).format('llll') }}</span>
         </template>

        </auto-table>
  </div>
</template>

<script>
import moment from "moment";
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';
import { AutoTable } from '@/components/common';
import { mapActions } from "vuex";
import VueDatePicker from "@vuepic/vue-datepicker";
import { endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths } from "date-fns";

export default {
  name: "SummaryAverages",
  components: {
    CustomLoading,
    PageHeader,
    VueDatePicker,
    AutoTable,
  },
  data() {
    return {
      moment: moment,
      summaryAverages: [],
      isLoading: false,
      params: {
        start: '',
        end: '',
        timestamp: Date.now(),
        page: '1',
        limit: '100'
      },
      total: 0,
      limit: 100,
      offset: 1,
      tableHeaders: [
        { label: 'Source', key: 'source', align: 'center' },
        { label: 'Identifier', key: 'identifier_name', align: 'center' },
        { label: 'Avg Stake', key: 'average_stake', align: 'center' },
        { label: 'Min Stake', key: 'average_minimum_stake', align: 'center' },
        { label: 'Max Stake', key: 'average_maximum_stake', align: 'center' },
        { label: 'Avg Bets/Player', key: 'avg_bet_count_per_player', align: 'center' },
        { label: 'Avg Payout/Player', key: 'avg_payout_per_player', align: 'center' },
        { label: 'Date', key: 'updated_at', align: 'center' },
      ],
      date: null,
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'Yesterday', range: [new Date(Date.now() - 86400000), new Date(Date.now() - 86400000)]},
        {label: 'Last 7 days', range: [new Date(Date.now() - 604800000), new Date()]},
        {label: 'Last 14 days', range: [new Date(Date.now() - 1209600000), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'Last 6 months', range: [new Date(Date.now() - 15768000000), new Date()]},
        // {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        // {
        //   label: 'This year (slot)',
        //   range: [startOfYear(new Date()), endOfYear(new Date())],
        //   slot: 'yearly',
        // },
      ],
    }
  },
  created() {
    this.fetchSummaryAverages();
  },
  methods: {
    ...mapActions(["getDashSummaryAverageReports"]),

    // Handle items per page change
    handleLimitChange(newLimit) {
      this.limit = newLimit;
      this.offset = 1;
      this.fetchSummaryAverages();
    },

    // Fetch summary averages data
    async fetchSummaryAverages() {
      this.isLoading = true;
      this.params.timestamp = Date.now();
      const response = await this.getDashSummaryAverageReports(this.params);
      if (response && response.status === 200) {
        console.log("Summary Averages: " + JSON.stringify(response));
        this.summaryAverages = response.message || [];
        this.total = this.summaryAverages.length;
      } else {
        this.summaryAverages = [];
        this.total = 0;
      }
      this.isLoading = false;
    },

    // Select date range
    selectDate(date) {
      // If date is null (cleared), reset date filters and fetch data
      if (!date) {
        console.log('Date filter cleared, resetting and fetching data...');
        this.date = null;
        this.params.start = '';
        this.params.end = '';
        this.fetchSummaryAverages();
        return;
      }

      this.date = date;

      // If date range is incomplete, return without doing anything
      if (!date[0]) return;

      // Update date filter values
      this.params.start = this.formatDate(date[0]);
      this.params.end = this.formatDate(date[1] ?? date[0]);

      // Log that date filter was updated and fetch data
      console.log('Date filter updated, fetching data with new date range...');
      this.fetchSummaryAverages();
    },

    // Handle page change
    handlePageChange(page) {
      this.offset = page;
      this.params.page = page.toString();
      this.fetchSummaryAverages();
    },

    formatDate(date) {
      const d = new Date(date);
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const year = d.getFullYear();

      return [year, month, day].join('-');
    },

    formatAmount(val) {
      if (!val) return "0.00";
      return Number(val).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});
    },

    // Get class for source badge
    getSourceClass(source) {
      const sourceMap = {
        'SPORTS_BOOK_CASH_BET': 'source-sports',
        'SPORTS_BOOK_BET_WINS': 'source-sports-win',
        'SPORTS_BOOK_BET_REFUND': 'source-sports-refund',
        'AVIATRIX_CASH_BET': 'source-aviatrix',
        'AVIATRIX_PAYOUT': 'source-aviatrix-win',
        'AVIATRIX_PROMO_WIN': 'source-aviatrix-promo',
        'DEPOSIT_MPESA': 'source-deposit'
      };

      return sourceMap[source] || 'source-default';
    },

    // Get class for identifier badge
    getIdentifierClass(identifier) {
      const identifierMap = {
        'STAKE': 'identifier-stake',
        'PAYOUTS': 'identifier-payout',
        'DEPOSIT': 'identifier-deposit',
        'REFUNDS': 'identifier-refund'
      };

      return identifierMap[identifier] || 'identifier-default';
    },

    // Get class for amount badge based on value
    getAmountClass(amount) {
      const value = parseFloat(amount);

      if (value === 0) return 'amount-zero';
      if (value < 100) return 'amount-low';
      if (value < 1000) return 'amount-medium-low';
      if (value < 5000) return 'amount-medium';
      if (value < 10000) return 'amount-medium-high';
      if (value < 20000) return 'amount-high';
      return 'amount-very-high';
    },

    // Format source text by splitting on underscore and wrapping
    formatSourceText(source) {
      if (!source) return '';
      const parts = source.split('_');
      if (parts.length <= 2) return source;

      // Group the first part separately, then join the rest
      return parts[0] + '\n' + parts.slice(1).join(' ');
    }
  }
}
</script>

<style scoped>
/* Source text styling for line breaks */
.source-text {
  display: inline-block;
  white-space: pre-line;
  line-height: 1.2;
}

/* Bet count badge */
.bet-count-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  background-color: #e8f5e9; /* Light green background */
  color: #1b5e20; /* Dark green text */
  font-weight: normal;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  width: 80px;
  letter-spacing: 0.3px;
  font-family: monospace;
}

.bet-count-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}
</style>
