<template>
  <div class="p-6">
    <PageHeader
      title="Scheduled SMS"
      subtitle="View and manage scheduled SMS messages"
      :show-back-button="true"
      @back="goBack"
    />

    <!-- Quick Navigation -->
    <div class="mx-6 mb-4 flex justify-end">
      <router-link
        to="/app/bulk-sms"
        class="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors flex items-center space-x-2"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        <span>Schedule New SMS</span>
      </router-link>
    </div>

    <CustomLoading v-if="isLoading" />

    <!-- Summary Cards -->
    <div v-if="!isLoading && data.length > 0" class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 rounded-lg">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-blue-600">Total Scheduled</p>
            <p class="text-2xl font-bold text-blue-900">{{ total.toLocaleString() }}</p>
          </div>
        </div>
      </div>

      <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 rounded-lg">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-green-600">Pending</p>
            <p class="text-2xl font-bold text-green-900">{{ pendingCount.toLocaleString() }}</p>
          </div>
        </div>
      </div>

      <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <div class="flex items-center">
          <div class="p-2 bg-orange-100 rounded-lg">
            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-orange-600">Ready to Send</p>
            <p class="text-2xl font-bold text-orange-900">{{ readyCount.toLocaleString() }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Data Table -->
    <auto-table
      :data="data"
      :loading="isLoading"
      :total-items="total"
      :items-per-page="limit"
      :current-page-prop="offset"
      :server-side-pagination="true"
      :pagination="total > limit"
      :show-items-count="true"
      :exclude-columns=excludeColumns
      :table-margin-bottom="12"
      @page-change="gotToPage"
    >
      <!-- Index Column -->
      <template #index="{ index }">
        <div class="text-center">{{ index + 1 + ((offset - 1) * limit) }}</div>
      </template>

      <!-- Campaign Name Column -->
      <template #campaign_name="{ item }">
        <span class="font-medium">{{ item.campaign_name || 'N/A' }}</span>
      </template>

      <!-- Message Column -->
      <template #message="{ item }">
        <div class="max-w-xs">
          <span class="text-sm">{{ truncateMessage(item.message) }}</span>
          <button 
            v-if="item.message && item.message.length > 50"
            @click="showFullMessage(item)"
            class="text-blue-600 hover:text-blue-800 text-xs ml-1"
          >
            View Full
          </button>
        </div>
      </template>

      <!-- Scheduled Time Column -->
      <template #scheduled_datetime="{ item }">
        <div class="text-sm">
          <div class="font-medium">{{ formatDate(item.scheduled_datetime) }}</div>
          <div class="text-gray-500">{{ formatTime(item.scheduled_datetime) }}</div>
        </div>
      </template>

      <!-- Status Column -->
      <template #status="{ item }">
        <span 
          class="px-2 py-1 text-xs font-medium rounded-full"
          :class="getStatusClass(item.status)"
        >
          {{ getStatusText(item.status) }}
        </span>
      </template>

      <!-- Recipients Count Column -->
      <template #recipient_count="{ item }">
        <span class="font-medium">{{ (item.recipient_count || 0).toLocaleString() }}</span>
      </template>

      <!-- Created Date Column -->
      <template #created_at="{ item }">
        <span class="text-xs text-gray-500">{{ moment(item.created_at).format('MMM DD, YYYY HH:mm') }}</span>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item }">
        <div class="flex space-x-2">
          <button
            v-if="canExecute(item)"
            @click="executeScheduledMessage(item)"
            class="px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700 transition-colors"
            title="Execute Now"
          >
            Execute
          </button>
          <button
            @click="viewDetails(item)"
            class="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
            title="View Details"
          >
            Details
          </button>
          <button
            v-if="canCancel(item)"
            @click="cancelScheduledMessage(item)"
            class="px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 transition-colors"
            title="Cancel"
          >
            Cancel
          </button>
        </div>
      </template>
    </auto-table>

    <!-- No Data Message -->
    <div v-if="!isLoading && data.length === 0" class="text-center py-8">
      <div class="text-gray-400 text-lg mb-2">📅</div>
      <p class="text-gray-500">No scheduled SMS messages found</p>
      <router-link 
        to="/app/bulk-sms" 
        class="text-blue-600 hover:text-blue-800 text-sm mt-2 inline-block"
      >
        Schedule a new message
      </router-link>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import { mapActions } from "vuex";
import { AutoTable, CustomLoading } from "@/components/common";
import PageHeader from "@/components/common/PageHeader.vue";

export default {
  components: {
    AutoTable,
    CustomLoading,
    PageHeader,
  },
  data() {
    return {
      isLoading: false,
      data: [],
      total: 0,
      limit: 100,
      offset: 1,
      
      tableHeaders: {
        index: '#',
        campaign_name: 'Campaign',
        message: 'Message',
        scheduled_datetime: 'Scheduled Time',
        status: 'Status',
        recipient_count: 'Recipients',
        created_at: 'Created',
        actions: 'Actions'
      },

      moreParams: {
        page: 1,
        limit: 100,
        timestamp: 'timestamp',
      },
      excludeColumns: ['user_id','status'],
    }
  },
  computed: {
    pendingCount() {
      return this.data.filter(item => item.status_text.toLowerCase() === 'pending' || item.status_text === 0).length;
    },
    readyCount() {
      return this.data.filter(item => item.ready_status.toLowerCase() === 'ready' || item.ready_status === 0).length;
    }
  },
  mounted() {
    this.loadScheduledSMS();
  },
  methods: {
    ...mapActions(["getScheduledSMS", "executeScheduledSMS", "toggleSideMenu"]),
    
    moment,

    goBack() {
      this.$router.go(-1);
    },

    async loadScheduledSMS() {
      this.isLoading = true;

      const params = new URLSearchParams();
      for (const key in this.moreParams) {
        if (this.moreParams.hasOwnProperty(key)) {
          params.append(key, this.moreParams[key]);
        }
      }

      try {
        const response = await this.getScheduledSMS(this.moreParams);
        
        if (response.status === 200) {
          console.log("fds",JSON.stringify(response.message.result)); 
          this.data = response.message.result || [];
          this.total = parseInt(response.message.record_count) || this.data.length;
        } else {
          this.data = [];
          this.total = 0;
          this.$swal.fire('Error!', response.message || 'Failed to load scheduled SMS', 'error');
        }
      } catch (error) {
        console.error('Error loading scheduled SMS:', error);
        this.data = [];
        this.total = 0;
        this.$swal.fire('Error!', 'Failed to load scheduled SMS', 'error');
      }

      this.isLoading = false;
    },

    gotToPage(page) {
      this.moreParams.page = page;
      this.offset = page;
      this.loadScheduledSMS();
    },

    formatDate(datetime) {
      if (!datetime) return 'N/A';
      return moment(datetime).format('MMM DD, YYYY');
    },

    formatTime(datetime) {
      if (!datetime) return 'N/A';
      return moment(datetime).format('HH:mm');
    },

    truncateMessage(message) {
      if (!message) return 'N/A';
      return message.length > 50 ? message.substring(0, 50) + '...' : message;
    },

    getStatusClass(status) {
      switch (status) {
        case 'pending':
        case 0:
          return 'bg-yellow-100 text-yellow-800';
        case 'sent':
        case 1:
          return 'bg-green-100 text-green-800';
        case 'failed':
        case 2:
          return 'bg-red-100 text-red-800';
        case 'cancelled':
        case 3:
          return 'bg-gray-100 text-gray-800';
        default:
          return 'bg-blue-100 text-blue-800';
      }
    },

    getStatusText(status) {
      switch (status) {
        case 'pending':
        case 0:
          return 'Pending';
        case 'sent':
        case 1:
          return 'Sent';
        case 'failed':
        case 2:
          return 'Failed';
        case 'cancelled':
        case 3:
          return 'Cancelled';
        default:
          return 'Unknown';
      }
    },

    canExecute(item) {
      return (item.status === 'pending' || item.status === 0) && 
             new Date(item.scheduled_datetime) <= new Date();
    },

    canCancel(item) {
      return item.status === 'pending' || item.status === 0;
    },

    async executeScheduledMessage(item) {
      const result = await this.$swal.fire({
        title: 'Execute Scheduled SMS?',
        text: `This will immediately send the scheduled message to ${item.recipient_count || 0} recipients.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#10b981',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, execute now!',
        showLoaderOnConfirm: true,
        preConfirm: async () => {
          try {
            return await this.executeScheduledSMS({ id: item.id });
          } catch (error) {
            this.$swal.showValidationMessage('Failed to execute scheduled SMS');
          }
        }
      });

      if (result.isConfirmed) {
        if (result.value.status === 200) {
          this.$swal.fire('Success!', 'Scheduled SMS executed successfully', 'success');
          this.loadScheduledSMS(); // Refresh the list
        } else {
          this.$swal.fire('Error!', result.value.message || 'Failed to execute scheduled SMS', 'error');
        }
      }
    },

    showFullMessage(item) {
      this.$swal.fire({
        title: 'Full Message',
        text: item.message,
        icon: 'info',
        confirmButtonText: 'Close'
      });
    },

    viewDetails(item) {
      const details = `
        <div class="text-left">
          <p><strong>Campaign:</strong> ${item.campaign_name || 'N/A'}</p>
          <p><strong>Scheduled Time:</strong> ${moment(item.scheduled_datetime).format('YYYY-MM-DD HH:mm:ss')}</p>
          <p><strong>Recipients:</strong> ${(item.recipient_count || 0).toLocaleString()}</p>
          <p><strong>Status:</strong> ${this.getStatusText(item.status)}</p>
          <p><strong>Created:</strong> ${moment(item.created_at).format('YYYY-MM-DD HH:mm:ss')}</p>
          <p><strong>Message:</strong></p>
          <div class="mt-2 p-2 bg-gray-100 rounded text-sm">${item.message || 'N/A'}</div>
        </div>
      `;

      this.$swal.fire({
        title: 'SMS Details',
        html: details,
        icon: 'info',
        confirmButtonText: 'Close',
        width: '600px'
      });
    },

    async cancelScheduledMessage(item) {
      // This would need a cancel API endpoint
      this.$swal.fire('Info', 'Cancel functionality would be implemented with a cancel API endpoint', 'info');
    }
  }
}
</script>
