<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Bulk SMS" pageSubtitle="Outbox" />

    <auto-table
        :headers="tableHeaders"
        :data="data"
        :loading="isLoading"
        :total-items="total"
        :items-per-page="limit"
        :current-page-prop="offset"
        :server-side-pagination="true"
        :pagination="total > limit"
        :show-items-count="true"
        :has-actions="true"
        :get-actions="getRowActions"
        :items-per-page-options="[10, 25, 50, 100]"
        @page-change="gotToPage"
        @items-per-page-change="handleLimitChange"
      >
        <!-- Index Column -->
        <template #index="{ index }">
          <div class="text-center">{{ index + 1 + ((offset - 1) * limit) }}</div>
        </template>

        <!-- Customer Column -->
        <template #msisdn="{ item }">
          <span class="font-medium">{{ item.msisdn }}</span>
        </template>

        <!-- Message Column -->
        <template #message="{ item }">
          <span>{{ item.message }}</span>
        </template>

        <!-- Date Column -->
        <template #created_at="{ item }">
          <span style="font-size: 11px; color: grey">{{ moment(item.created_at).format('llll') }}</span>
        </template>

        <!-- DLR State Column -->
        <template #dlr_state="{ item }">
          <div v-if="parseInt(item.dlr_state) === 1" 
               class="action-button inline-block px-3 py-1 rounded-full text-white text-xs font-medium shadow-md transform transition-transform hover:scale-105 hover:-translate-y-1 hover:shadow-lg w-20 text-center bg-gradient-to-r from-green-500 to-green-600">
            Sent
          </div>
          <div v-else-if="parseInt(item.dlr_state) === 0" 
               class="action-button inline-block px-3 py-1 rounded-full text-white text-xs font-medium shadow-md transform transition-transform hover:scale-105 hover:-translate-y-1 hover:shadow-lg w-20 text-center bg-gradient-to-r from-red-500 to-red-600">
            Pending
          </div>
          <div v-else-if="parseInt(item.dlr_state) === 3" 
               class="action-button inline-block px-3 py-1 rounded-full text-white text-xs font-medium shadow-md transform transition-transform hover:scale-105 hover:-translate-y-1 hover:shadow-lg w-20 text-center bg-gradient-to-r from-red-500 to-red-600">
            Failed
          </div>
        </template>

        <!-- Action Column -->
        <template #actions="{ item, index }">
          <div class="relative inline-block">
            <button
                v-if="parseInt(item.dlr_state) === 1"
                class="action-button inline-block px-3 py-1 rounded-full text-white text-xs font-medium shadow-md transform transition-transform hover:scale-105 hover:-translate-y-1 hover:shadow-lg bg-gradient-to-r from-blue-500 to-blue-600"
                @click="toggleDropdown(index)">
              <span class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="currentColor" class="w-4 h-4 mr-1">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M12 6.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 12.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 18.75a.75.75 0 110-1.5.75.75 0 010 1.5z"/>
                </svg>
                Actions
              </span>
            </button>

            <button v-else 
                    class="action-button inline-block px-3 py-1 rounded-full text-white text-xs font-medium shadow-md bg-gradient-to-r from-gray-400 to-gray-500 opacity-50 cursor-not-allowed">
              <span class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="currentColor" class="w-4 h-4 mr-1">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M12 6.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 12.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 18.75a.75.75 0 110-1.5.75.75 0 010 1.5z"/>
                </svg>
                Actions
              </span>
            </button>

            <div v-if="showDropdown[index]" 
                 class="absolute right-0 mt-2 bg-white border rounded-lg shadow-lg z-10 overflow-hidden transition-all duration-200 ease-in-out"
                 style="width: 200px; text-align: left;">
              <ul class="py-1">
                <li @click="repostMessage(item.id)" 
                    class="flex items-center px-4 py-2 cursor-pointer hover:bg-blue-50 transition-colors duration-150">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                       stroke="currentColor" class="w-4 h-4 mr-2 text-blue-600">
                    <path stroke-linecap="round" stroke-linejoin="round"
                          d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"/>
                  </svg>
                  Repost
                </li>
              </ul>
            </div>
          </div>
        </template>
    </auto-table>
      
    <!--Modals-->
    <!--Blacklist Modal-->
    <div v-if="deposit!=null" class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
         :class="{ 'opacity-100 pointer-events-auto': viewModelOpen, 'opacity-0 pointer-events-none': !viewModelOpen }">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
      <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <!--Title -->
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Details</p>
            <div class="modal-close cursor-pointer z-50" @click="viewModelOpen = false">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                   viewBox="0 0 18 18">
                <path
                    d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
              </svg>
            </div>
          </div>
          <!--Body -->
          <div class="mb-4">
            <label v-if="deposit.callback_status === '1'" class="block text-sm font-medium">Callback Status :
              Success</label>
            <label v-else-if="deposit.callback_status ==='3'" class="block text-sm font-medium">Callback Status :
              Failed</label>
            <label v-else class="block text-sm font-medium">Callback Status : Pending</label>
          </div>

          <div class="mb-4">
            <label class="block text-sm font-medium">Callback Net Status : {{ deposit.callback_net_status }}</label>
          </div>

          <div class="mb-4">
            <label class="block text-sm font-medium">Callback TAT (secs): {{ deposit.callback_tat }}</label>
          </div>

          <div class="mb-4">
            <label class="block text-sm font-medium">
              Callback Date: {{ moment(deposit.callback_date).format('lll') }}
            </label>
          </div>

          <div class="mb-4">
            <label class="block text-sm font-medium">Callback Response</label>
            <textarea
                v-model="deposit.callback_response"
                class="mt-1 p-2 border rounded-md w-full"
                disabled>
              </textarea>
          </div>

        </div>
      </div>
    </div>

  </div>
</template>

<script>
import moment from "moment";
// import numeral from "numeral"
import {mapActions} from "vuex";
import Download from "@/components/downloadCSV.vue";
import { AutoTable, ActionDropdown, ActionItem, CustomLoading } from "@/components/common";
import PageHeader from "@/components/common/PageHeader.vue";

export default {
  components: {
    Download,
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      //
      data: [],
      //
      tableHeaders: [
        { label: '#', key: 'index', align: 'center' },
        { label: 'Customer', key: 'msisdn', align: 'center' },
        { label: 'Message', key: 'message', align: 'left' },
        { label: 'Date', key: 'created_at', align: 'center' },
        { label: 'DLR State', key: 'dlr_state', align: 'center' },
        // { label: 'Action', key: 'actions', sortable: false, align: 'center' }
      ],
      selectedStatus: 'All', // Initially, no filter (show all)
      filteredData: this.data, // Initially same as data
      deposit: null,
      moreParams: {
        dlr_status: '',
        mobile_number: '',
        start: '',
        end: '',
        page: '',
        limit: 100,
        timestamp: 'timestamp',
        skip_cache: '',
        export: '',
      },
      download: {request: false, loading: false, headers: {}, items: [], fileTitle: ''},

    }
  },
  mounted() {
    this.setOutbox()
  },
  methods: {
    ...mapActions(["getOutbox", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    // Filtering
    filterByStatus() {
      console.log("this.selectedStatus", this.selectedStatus)
      if (this.selectedStatus === "All") {
        this.filteredData = this.data; // Show all if no filter
      } else {
        this.filteredData = this.data.filter(account => parseInt(account.status) === parseInt(this.selectedStatus));
      }
    },
    // Pagination and dropdowns
    gotToPage(page) {
      let vm = this
      vm.moreParams.page = page
      vm.offset = page
      vm.setOutbox()
    },
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    toggleDetails(data) {
      this.viewModelOpen = true;
      this.deposit = data;
    },

    repostMessage(id) {
      this.closeDropdown();
      
      this.$swal.fire({
        title: 'Repost Message',
        text: "Are you sure you want to repost this message?",
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, repost it!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async () => {
          try {
            // Replace with your actual API call
            // Example: return await this.repostSmsMessage({ id });
            return { status: 200, message: "Message reposted successfully" };
          } catch (error) {
            return { status: 500, message: error.message || "Failed to repost message" };
          }
        },
      }).then((result) => {
        if (result.value && result.value.status === 200) {
          this.$swal.fire('Reposted!', result.value.message, 'success');
          // Refresh the data
          this.setOutbox();
        } else if (result.value) {
          this.$swal.fire('Error!', result.value.message, 'error');
        }
      });
    },

    async setOutbox() {
      this.isLoading = true

      const params = new URLSearchParams();

      for (const key in this.moreParams) {
        if (this.moreParams.hasOwnProperty(key)) {
          params.append(key, this.moreParams[key]);
        }
      }

      const queryString = params.toString();

      let response = await this.getOutbox(queryString)

      // console.log("data OK: " + JSON.stringify(response.message.result))
      this.data = response.message.result ?? []
      this.total = parseInt(response.message.record_count) ?? 0

      this.showDropdown = []
      for (let i = 0; i < this.data.length; i++) {
        this.showDropdown.push(false)
      }

      this.filterByStatus()
      this.isLoading = false
    },

    //
  },
}
</script>
