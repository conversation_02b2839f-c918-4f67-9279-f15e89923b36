<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <!-- <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" /> -->
    <page-header pageName="Reports" pageSubtitle="" />

    <!-- Filters Section -->
    <div class="p-4 mb-4">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

        <!-- Report Type Filter -->
        <div class="filter-card p-3 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-2">Report Type</h4>
          <select
            v-model="selectedReportType"
            @change="applyFilters"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
          >
            <option value="registration">Registration</option>
            <option value="deposits">Deposits</option>
            <option value="withdrawals">Withdrawals</option>
            <option value="sportsbook">Sportsbook</option>
            <option value="freebets">Freebets</option>
          </select>
        </div>

        <!-- Date Range Filter -->
        <div class="filter-card p-3 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-2">Date Range</h4>
          <VueDatePicker
                    v-model="date"
                    range
                    multi-calendars
                    :enable-time-picker="false"
                    :format="'yyyy-MM-dd'"
                    :preset-ranges="presetRanges"
                    placeholder="Select date range"
                    class="w-full text-xs"
                    @update:model-value="selectDate"
                  />
        </div>

        <!-- Filter Actions -->
        <div class="filter-card p-3 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-2">Actions</h4>
          <div class="flex space-x-2">
            <button
              @click="applyFilters"
              class="flex-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
            >
              Apply Filters
            </button>
            <button
              @click="resetFilters"
              class="flex-1 px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors text-sm"
            >
              Reset
            </button>
          </div>
        </div>

      </div>
    </div>

    <!-- Reports Table using Enhanced AutoTable component with optional formatting -->
  <div class="px-4 py-1">
    <auto-table
      :items="reports"
      :has-actions="false"
      :loading="isLoading"
      :total-items="total"
      :items-per-page="limit"
      :current-page-prop="offset"
      :server-side-pagination="true"
      :pagination="total > limit"
      :show-items-count="true"
      :date-formats="dateFormats"
      :decimal-places="decimalPlaces"
      :no-auto-date-format="noAutoDateFormat"
      :column-styles="columnStyles"
      :header-styles="headerStyles"
      :get-actions="getRowActions"
      :empty-message="apiMessage || 'No data available'"
      :items-per-page-options="[10, 25, 50, 100]"
      @page-change="gotToPage"
      @items-per-page-change="updateItemsPerPage"
    >
      <!-- Custom slot example (optional) -->
      <template #bet_amount="{ item }">
        <span class="font-semibold text-green-600">
          ${{ parseFloat(item.bet_amount).toFixed(2) }}
        </span>
      </template>
    </auto-table>
  </div>

    
      <!--Modals-->
      <!--Report Modal-->
      <div v-if="report!=null" class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
           :class="{ 'opacity-100 pointer-events-auto': viewModelOpen, 'opacity-0 pointer-events-none': !viewModelOpen }">
        <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
        <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
          <div class="modal-content py-4 text-left px-6">
            <!--Title -->
            <div class="flex justify-between items-center pb-3">
              <p class="text-2xl font-bold">Details</p>
              <div class="modal-close cursor-pointer z-50" @click="viewModelOpen = false">
                <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                     viewBox="0 0 18 18">
                  <path
                      d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
                </svg>
              </div>
            </div>
            <!--Body -->
            <!--Body -->
            <div class="mb-4">
              <label v-if="stakeAmt" class="block text-sm font-medium">Stake Amount : {{ stakeAmt }}</label>
            </div>

            <div class="mb-4">
              <label v-if="desc" class="block text-sm font-medium">Description : {{ desc }}</label>
            </div>
            <div class="mb-4">
              <label v-if="stakeAmt" class="block text-sm font-medium">Stake Amount : {{ stakeAmt }}</label>
            </div>

            <div class="mb-4">
              <label v-if="dateOfStake" class="block text-sm font-medium">Date of Stake : {{ dateOfStake }}</label>
            </div>
            <div class="mb-4">
              <label v-if="stakeAmt" class="block text-sm font-medium">Stake Amount : {{ stakeAmt }}</label>
            </div>

            <div class="mb-4">
              <label v-if="exciseAmt" class="block text-sm font-medium">Excise Amount : {{ exciseAmt }}</label>
            </div>

            <div class="mb-4">
              <label v-if="expectedOutcomeTime" class="block text-sm font-medium">Expected Outcome Time :
                {{ expectedOutcomeTime }}</label>
            </div>

            <div class="mb-4">
              <label v-if="walletBalanceStake" class="block text-sm font-medium">Wallet Balance Stake:
                {{ walletBalanceStake }}</label>
            </div>


          </div>
        </div>
      </div>
  </div>
</template>

<script>
import moment from "moment";
import {mapActions} from "vuex";
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';
import AutoTable from '@/components/common/AutoTable.vue';
import VueDatePicker from "@vuepic/vue-datepicker";
import '@vuepic/vue-datepicker/dist/main.css';

export default {
  components: {
    CustomLoading,
    PageHeader,
    AutoTable,
    VueDatePicker
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      //
      selectedStatus: 'All', // Initially, no filter (show all)
      filteredReports: this.reports, // Initially same as reports
      //
      reports: [],
      report: null,
      apiMessage: '', // Store API message when no data
      //
      stakeAmt: "",
      desc: "",
      dateOfStake: "",
      exciseAmt: "",
      expectedOutcomeTime: "",
      walletBalanceStake: "",
      //
      moreParams: {
        dlr_status: '',
        mobile_number: '',
        start: '',
        end: '',
        page: '',
        limit: 10,
        timestamp: 'timestamp',
        skip_cache: '',
        report_type: 'registration', // New filter for report type
      },

      // New filter properties
      date: null, // Add missing date property for VueDatePicker
      dateRange: null,
      selectedReportType: 'registration',

      // Preset date ranges for VueDatePicker
      presetRanges: [
        { label: 'Today', range: [new Date(), new Date()] },
        { label: 'Yesterday', range: [new Date(Date.now() - 24 * 60 * 60 * 1000), new Date(Date.now() - 24 * 60 * 60 * 1000)] },
        { label: 'Last 7 Days', range: [new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), new Date()] },
        { label: 'Last 30 Days', range: [new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date()] },
        { label: 'This Month', range: [new Date(new Date().setDate(1)), new Date()] },
        {
          label: 'Last Month', range: [
            new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1),
            new Date(new Date().getFullYear(), new Date().getMonth(), 0)
          ]
        }
      ],

      // Optional formatting configurations for AutoTable
      dateFormats: {
        // Show only date for these columns (no time)
        date: 'date',
        // Show full datetime for these columns
        // date: 'datetime',
        // Show only time for these columns
        // date: 'time'
      },

      decimalPlaces: {
        // Show 2 decimal places for currency fields
        bet_amount: 2,
        stake_amount: 2,
        payout_amount: 2,
        total_stake:2,
        average_stake:2,
        total_amount_deposited: 2,
        average_deposits: 2,
        total_amount_withdrawn: 2,
        average_withdrawal_amount: 2,
        average_stake_amount: 2,
        total_stake_amount: 2,

        // total
      },

      // Disable automatic date formatting for these columns (show raw values)
      noAutoDateFormat: [
        'raw_timestamp',
        'unix_timestamp'
      ],

      // Custom CSS styles for specific columns
      columnStyles: {
        // Registration
        date: 'text-left font-bold text-purple-700',
        number_of_registrations: 'text-left font-bold text-black-700',
        verified: 'text-left font-bold text-green-600',
        unverified: 'text-left font-bold text-red-600',

        // deposits
        number_of_deposits: 'text-left font-bold text-black-600',
        total_amount_deposited: 'text-left font-bold text-black-600',
        average_deposit_amount: 'text-left font-bold text-black-600',
        total_depositors: 'text-left font-bold text-black-600',
        unique_depositors: 'text-left font-bold text-purple-600',
        number_of_transactions: 'text-left font-bold text-black-600',

        // withdrawals
        number_of_withdrawals: 'text-left font-bold text-green-600',
        total_amount_withdrawn: 'text-left font-bold text-green-600',
        average_withdrawal_amount: 'text-left font-bold text-green-600',
        unique_withdrawers: 'text-left font-bold text-green-600',
        number_of_withdrawal_transactions: 'text-left font-bold text-green-600',
        
        // sportsbook cashbets [Number Of Bets	Total Stake Amount	Average Stake Amount	Total Wins	Total Losses	Total Pending	Total Bettors	Unique Bettors]
        number_of_bets: 'text-left font-bold text-green-600',
        total_stake_amount: 'text-left font-bold text-green-600',
        average_stake_amount: 'text-left font-bold text-green-600',
        total_wins: 'text-left font-bold text-green-600',
        total_losses: 'text-left font-bold text-red-600',
        total_pending: 'text-left font-bold text-gray-500',
        total_bettors: 'text-left font-bold text-green-600',
        unique_bettors: 'text-left font-bold text-purple-600',

        number_of_freebets: 'text-left font-bold text-green-600',

      },

      // Custom CSS styles for column headers
      headerStyles: {
        // registration
        date: 'text-left font-bold text-purple-700 bg-purple-50 text-xs w-1/4',
        number_of_registrations: 'text-left font-bold text-green-700 bg-green-50 w-1/4',
        verified: 'text-left font-bold text-green-700 bg-green-50 w-1/4',
        unverified: 'text-left font-bold text-red-700 bg-red-50 w-1/4',

        // deposits
        number_of_deposits: 'text-left font-bold text-green-700 bg-green-50 w-1/4',
        total_amount_deposited: 'text-left font-bold text-green-700 bg-green-50 w-1/4',
        average_deposit_amount: 'text-left font-bold text-green-700 bg-green-50 w-1/4',
        total_depositors: 'text-left font-bold text-purple-700 bg-green-50 w-1/4',
        unique_depositors: 'text-left font-bold text-green-700 bg-green-50 w-1/4',
        number_of_transactions: 'text-left font-bold text-green-700 bg-green-50 w-1/4',

        // withdrawals
        number_of_withdrawals: 'text-left font-bold text-green-700 bg-green-50 w-1/4',
        total_amount_withdrawn: 'text-left font-bold text-green-700 bg-green-50 w-1/4',
        average_withdrawal_amount: 'text-left font-bold text-green-700 bg-green-50 w-1/4',
        unique_withdrawers: 'text-left font-bold text-green-700 bg-green-50 w-1/4',

        // sportsbook cashbets [Number Of Bets	Total Stake Amount	Average Stake Amount	Total Wins	Total Losses	Total Pending	Total Bettors	Unique Bettors]
        number_of_bets: 'text-left font-bold text-green-700 bg-green-50 w-1/4',
        total_stake_amount: 'text-left font-bold text-green-700 bg-green-50 w-1/4',
        average_stake_amount: 'text-left font-bold text-green-700 bg-green-50 w-1/4',
        total_wins: 'text-left font-bold text-green-700 bg-green-50 w-1/4',
        total_losses: 'text-left font-bold text-green-700 bg-green-50 w-1/4',
        total_pending: 'text-left font-bold text-green-700 bg-green-50 w-1/4',
        total_bettors: 'text-left font-bold text-green-700 bg-green-50 w-1/4',
        unique_bettors: 'text-left font-bold text-purple-700 bg-green-50 w-1/4',

        number_of_freebets: 'text-left font-bold text-black-700 bg-green-50 w-1/4',


      },

    }
  },
  mounted() {
    this.moreParams.report_type = 'registration'
    this.setReports()
  },
  methods: {
    ...mapActions(["getSpecialReports", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },
    // Filtering
    filterByStatus() {
      // console.log("this.selectedStatus", this.selectedStatus)
      if (this.selectedStatus === "All") {
        this.filteredReports = this.reports; // Show all if no filter
      } else {
        this.filteredReports = this.reports.filter(depositData => parseInt(depositData.callback_status) === parseInt(this.selectedStatus));
      }
    },
    // Pagination handler for DataTable
    gotToPage(page) {
      this.moreParams.page = page
      this.offset = page
      this.setReports()
    },
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    toggleDetails(data) {
      this.viewModelOpen = true;
      this.report = data;
      // Parse the JSON string in "extra_data" field
      const extraData = JSON.parse(data.callback_extra_data);
      console.log("extraData:", extraData)

// Extract specific properties
      this.resultCode = extraData.ResultCode;
      this.debitPartyCharges = extraData.DebitPartyCharges;
      this.transCompletedTime = extraData.TransCompletedTime;
      this.debitAccountBalance = extraData.DebitAccountBalance;
      this.receiverPartyPublicName = extraData.ReceiverPartyPublicName;
      this.initiatorAccountCurrentBalance = extraData.InitiatorAccountCurrentBalance;
      this.debitPartyAffectedAccountBalance = extraData.DebitPartyAffectedAccountBalance;
    },


    async setReports() {
      let app = this
      app.isLoading = true
      this.moreParams.timestamp = Date.now()
      const params = new URLSearchParams();
      for (const key in this.moreParams) {
        if (this.moreParams.hasOwnProperty(key)) {
          params.append(key, this.moreParams[key]);
        }
      }
      const queryString = params.toString();
      let response = await this.getSpecialReports(queryString)

      // console.log("SpecialReports OK: " + JSON.stringify(response.message.result))
      if (response.status === 200) {
        this.reports = response.message.result || []
        this.total = parseInt(response.message.record_count || 0)
        this.apiMessage = '' // Clear any previous message
        app.showTables = true;
      } else {
        // Handle error cases - display API message
        this.reports = []
        this.total = 0
        this.apiMessage = response.message || 'No data available'
      }

      app.isLoading = false

    },

    // Handle items per page change
    updateItemsPerPage(newLimit) {
      this.limit = newLimit
      this.moreParams.limit = newLimit
      this.offset = 1 // Reset to first page
      this.moreParams.page = 1
      this.setReports()
    },


    toggleReportDetails(data) {
      this.viewModelOpen = true;
      this.report = data;

      // Parse the JSON string in "extra_data" field
      const extraData = JSON.parse(data.extra_data);
      console.log("extraData:", extraData)

      this.stakeAmt = extraData.stakeAmt
      this.desc = extraData.desc
      this.dateOfStake = extraData.dateOfStake
      this.exciseAmt = extraData.exciseAmt
      this.expectedOutcomeTime = extraData.expectedOutcomeTime
      this.walletBalanceStake = extraData.walletBalanceStake
    },

    // New filter methods
    onDateRangeUpdate() {
      if (this.dateRange && Array.isArray(this.dateRange) && this.dateRange.length === 2) {
        // Format dates as YYYY-MM-DD
        this.moreParams.start = this.formatDate(this.dateRange[0]);
        this.moreParams.end = this.formatDate(this.dateRange[1]);
      } else {
        this.moreParams.start = '';
        this.moreParams.end = '';
      }
    },

    
    // Update date filter values and fetch data
    selectDate() {
      // If date is null (cleared), reset date filters and fetch data
      if (!this.date) {
        console.log('Date filter cleared, resetting and fetching data...');
        this.moreParams.start = '';
        this.moreParams.end = '';
        this.setReports();
        return;
      }

      // If date range is incomplete, return without doing anything
      if (!this.date[0] || !this.date[1]) return;

      // Update date filter values
      this.moreParams.start = this.formatDate(this.date[0]);
      this.moreParams.end = this.formatDate(this.date[1]);

      // Log that date filter was updated and fetch data
      console.log('Date filter updated, fetching data with new date range...');
      this.setReports();
    },

    formatDate(date) {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    applyFilters() {
      // Update report type filter
      this.moreParams.report_type = this.selectedReportType;

      // Reset to first page when applying filters
      this.offset = 1;
      this.moreParams.page = 1;

      // Fetch reports with new filters
      this.setReports();
    },

    resetFilters() {
      // Reset all filter values
      this.dateRange = null;
      this.selectedReportType = '';

      // Reset moreParams
      this.moreParams.start = '';
      this.moreParams.end = '';
      this.moreParams.report_type = '';
      this.moreParams.page = 1;

      // Reset pagination
      this.offset = 1;

      // Fetch reports without filters
      this.setReports();
    },

    // Define actions for each row using getActions (3-dot dropdown menu)
    getRowActions(item) {
      return [
        {
          label: 'View Details',
          action: () => this.toggleReportDetails(item),
          icon: 'fas fa-eye'
        },
        {
          label: 'Edit Report',
          action: () => this.editReport(item),
          icon: 'fas fa-edit'
        },
        {
          label: 'Download PDF',
          action: () => this.downloadReport(item),
          icon: 'fas fa-download'
        },
        {
          label: 'Share Report',
          action: () => this.shareReport(item),
          icon: 'fas fa-share'
        },
        {
          label: 'Delete Report',
          action: () => this.deleteReport(item),
          icon: 'fas fa-trash'
        }
      ];
    },

    // Action methods for dropdown menu
    editReport(item) {
      console.log('Editing report:', item);
      this.$swal.fire('Info', `Editing report ${item.bet_id || item.id}`, 'info');
    },

    downloadReport(item) {
      console.log('Downloading report:', item);
      this.$swal.fire('Success', `Downloading report ${item.bet_id || item.id}`, 'success');
      // Add your download logic here
    },

    shareReport(item) {
      console.log('Sharing report:', item);
      this.$swal.fire('Info', `Sharing report ${item.bet_id || item.id}`, 'info');
      // Add your share logic here
    },

    deleteReport(item) {
      console.log('Deleting report:', item);
      this.$swal.fire({
        title: 'Are you sure?',
        text: `Delete report ${item.bet_id || item.id}?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'Cancel'
      }).then((result) => {
        if (result.isConfirmed) {
          // Perform delete operation
          this.$swal.fire('Deleted!', 'Report has been deleted.', 'success');
        }
      });
    },

    //
  },
}
</script>

<style scoped>
/* Report table styles */
.action-button {
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: #d1d1d1 !important;
  transform: translateY(-1px);
}

/* Modal styles */
.modal {
  transition: all 0.3s ease;
}

.modal-container {
  max-height: 90vh;
}
</style>
