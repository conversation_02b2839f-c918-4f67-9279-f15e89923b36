<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
    <page-header pageName="Taxes" pageSubtitle="" />

    <!-- Taxes Table using AutoTable component -->
   
      <div class="p-3">
        <auto-table
          :headers="tableHeaders"
          :data="taxes"
          :has-actions="true"
          :get-actions="getRowActions"
          :loading="isLoading"
          :total-items="total"
          :items-per-page="limit"
          :current-page-prop="offset"
          :server-side-pagination="true"
          :pagination="total > limit"
          :show-items-count="true"
          :items-per-page-options="[10, 25, 50, 100]"
          @page-change="gotToPage"
          @items-per-page-change="handleLimitChange"
        >
          <!-- Index Column -->
          <template #index="{ index }">
            <div>{{ index + 1 + ((offset - 1) * limit) }}</div>
          </template>

          <!-- Bet ID Column -->
          <template #bet_id="{ item }">
            <div><strong>{{ item.bet_id }}</strong></div>
          </template>

          <!-- Bet Type Column -->
          <template #bet_type="{ item }">
            <div><strong>{{ item.bet_type }}</strong></div>
          </template>

          <!-- Bet Amount & Odds Column -->
          <template #bet_amount_odds="{ item }">
            <div>
              <span class="font-medium">{{ item.bet_amount }}</span>
              <br><span style="font-size: 11px; color: grey">{{ item.bet_odds }}</span>
            </div>
          </template>

          <!-- Mobile Number Column -->
          <template #mobile_number="{ item }">
            <div>+{{ item.mobile_number }}</div>
          </template>

          <!-- Post Description Column -->
          <template #post_desc="{ item }">
            <div>{{ item.post_desc }}</div>
          </template>

          <!-- Stake Post Date Column -->
          <template #stake_post_date="{ item }">
            <div style="font-size: 11px; color: grey">{{ moment(item.stake_post_date).format('llll') }}</div>
          </template>

          <!-- Actions Column -->
          <template #actions="{ item }">
            <div @click="toggleTaxDetails(item)" class="action-button"
                style="background-color: #e3e3e3; padding: 8px; border-radius: 4px; cursor: pointer; display: inline-block;">
              <strong>View</strong>
            </div>
          </template>
        </auto-table>
      </div>

    
      <!--Modals-->
      <!--Tax Modal-->
      <div v-if="tax!=null" class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
           :class="{ 'opacity-100 pointer-events-auto': viewModelOpen, 'opacity-0 pointer-events-none': !viewModelOpen }">
        <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>
        <div class="modal-container bg-white w-11/12 md:max-w-4xl mx-auto rounded shadow-lg z-50 overflow-y-auto">
          <div class="modal-content py-4 text-left px-6">
            <!--Title -->
            <div class="flex justify-between items-center pb-3">
              <p class="text-2xl font-bold">Details</p>
              <div class="modal-close cursor-pointer z-50" @click="viewModelOpen = false">
                <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                     viewBox="0 0 18 18">
                  <path
                      d="M1.22 1.22a1 1 0 0 1 1.41 0L9 7.59l6.37-6.37a1 1 0 1 1 1.41 1.41L10.41 9l6.36 6.37a1 1 0 0 1-1.41 1.41L9 10.41l-6.37 6.36a1 1 0 0 1-1.41-1.41L7.59 9 .22 2.63a1 1 0 0 1 0-1.41z"/>
                </svg>
              </div>
            </div>
            <!--Body -->
            <!--Body -->
            <div class="mb-4">
              <label v-if="stakeAmt" class="block text-sm font-medium">Stake Amount : {{ stakeAmt }}</label>
            </div>

            <div class="mb-4">
              <label v-if="desc" class="block text-sm font-medium">Description : {{ desc }}</label>
            </div>
            <div class="mb-4">
              <label v-if="stakeAmt" class="block text-sm font-medium">Stake Amount : {{ stakeAmt }}</label>
            </div>

            <div class="mb-4">
              <label v-if="dateOfStake" class="block text-sm font-medium">Date of Stake : {{ dateOfStake }}</label>
            </div>
            <div class="mb-4">
              <label v-if="stakeAmt" class="block text-sm font-medium">Stake Amount : {{ stakeAmt }}</label>
            </div>

            <div class="mb-4">
              <label v-if="exciseAmt" class="block text-sm font-medium">Excise Amount : {{ exciseAmt }}</label>
            </div>

            <div class="mb-4">
              <label v-if="expectedOutcomeTime" class="block text-sm font-medium">Expected Outcome Time :
                {{ expectedOutcomeTime }}</label>
            </div>

            <div class="mb-4">
              <label v-if="walletBalanceStake" class="block text-sm font-medium">Wallet Balance Stake:
                {{ walletBalanceStake }}</label>
            </div>


          </div>
        </div>
      </div>
  </div>
</template>

<script>
import moment from "moment";
import {mapActions} from "vuex";
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';
import { AutoTable } from '@/components/common';

export default {
  components: {
    CustomLoading,
    PageHeader,
    AutoTable
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      //
      selectedStatus: 'All', // Initially, no filter (show all)
      filteredTaxes: this.taxes, // Initially same as taxes
      //
      taxes: [],
      tax: null,
      //
      stakeAmt: "",
      desc: "",
      dateOfStake: "",
      exciseAmt: "",
      expectedOutcomeTime: "",
      walletBalanceStake: "",
      //
      tableHeaders: [
        { key: 'index', label: '#' },
        { key: 'bet_id', label: 'Bet ID' },
        { key: 'bet_type', label: 'Bet Type' },
        { key: 'bet_amount_odds', label: 'Bet Amount & Odds' },
        { key: 'mobile_number', label: 'Mobile Number' },
        { key: 'post_desc', label: 'Post Desc' },
        { key: 'stake_post_date', label: 'Stake Post Date' }
      ],
      //
      moreParams: {
        dlr_status: '',
        mobile_number: '',
        start: '',
        end: '',
        page: '',
        limit: 10,
        timestamp: 'timestamp',
        skip_cache: '',
      },

    }
  },
  mounted() {
    this.setTaxes()
  },
  methods: {
    ...mapActions(["getTaxes", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    // Get row actions for AutoTable
    getRowActions(item) {
      return [
        {
          label: 'View Details',
          action: () => this.viewTaxDetails(item),
          icon: 'fas fa-eye'
        },
        {
          label: 'Download Receipt',
          action: () => this.downloadReceipt(item),
          icon: 'fas fa-download'
        }
      ];
    },

    // Handle items per page change
    handleLimitChange(newLimit) {
      this.limit = newLimit;
      this.moreParams.limit = newLimit.toString();
      this.offset = 1;
      this.moreParams.page = '1';
      this.setTaxes();
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },
    // Filtering
    filterByStatus() {
      // console.log("this.selectedStatus", this.selectedStatus)
      if (this.selectedStatus === "All") {
        this.filteredTaxes = this.taxes; // Show all if no filter
      } else {
        this.filteredTaxes = this.taxes.filter(depositData => parseInt(depositData.callback_status) === parseInt(this.selectedStatus));
      }
    },
    // Pagination handler for DataTable
    gotToPage(page) {
      this.moreParams.page = page
      this.offset = page
      this.setTaxes()
    },
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    toggleDetails(data) {
      this.viewModelOpen = true;
      this.tax = data;
      // Parse the JSON string in "extra_data" field
      const extraData = JSON.parse(data.callback_extra_data);
      console.log("extraData:", extraData)

// Extract specific properties
      this.resultCode = extraData.ResultCode;
      this.debitPartyCharges = extraData.DebitPartyCharges;
      this.transCompletedTime = extraData.TransCompletedTime;
      this.debitAccountBalance = extraData.DebitAccountBalance;
      this.receiverPartyPublicName = extraData.ReceiverPartyPublicName;
      this.initiatorAccountCurrentBalance = extraData.InitiatorAccountCurrentBalance;
      this.debitPartyAffectedAccountBalance = extraData.DebitPartyAffectedAccountBalance;
    },


    async setTaxes() {
      let app = this
      app.isLoading = true
      this.moreParams.timestamp = Date.now()
      const params = new URLSearchParams();
      for (const key in this.moreParams) {
        if (this.moreParams.hasOwnProperty(key)) {
          params.append(key, this.moreParams[key]);
        }
      }
      const queryString = params.toString();
      let response = await this.getTaxes(queryString)

      // console.log("Taxez OK: " + JSON.stringify(response.message.result))
      if (response.status === 200) {
        this.taxes = response.message.result
        this.total = parseInt(response.message.record_count)
        app.showTables = true;
      }

      app.isLoading = false

    },


    toggleTaxDetails(data) {
      this.viewModelOpen = true;
      this.tax = data;

      // Parse the JSON string in "extra_data" field
      const extraData = JSON.parse(data.extra_data);
      console.log("extraData:", extraData)

      this.stakeAmt = extraData.stakeAmt
      this.desc = extraData.desc
      this.dateOfStake = extraData.dateOfStake
      this.exciseAmt = extraData.exciseAmt
      this.expectedOutcomeTime = extraData.expectedOutcomeTime
      this.walletBalanceStake = extraData.walletBalanceStake
    },


    //
  },
}
</script>

<style scoped>
/* Tax table styles */
.action-button {
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: #d1d1d1 !important;
  transform: translateY(-1px);
}

/* Modal styles */
.modal {
  transition: all 0.3s ease;
}

.modal-container {
  max-height: 90vh;
}
</style>
