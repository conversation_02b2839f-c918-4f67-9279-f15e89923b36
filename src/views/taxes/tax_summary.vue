<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
    <page-header pageName="Tax" pageSubtitle="Summary" />

    <div class="block p-3 ">
      <!-- Export Section -->
      <div class="flex justify-between items-center px-0 mb-4">
        <h2 class="text-lg font-semibold text-gray-700">Tax Summary</h2>
        <div class="flex space-x-3">
          <excel-export v-if="taxSummary.length > 0" :headers="exportHeaders" :items="taxSummary"
            :file-title="'Tax Summary - ' + moment(new Date()).format('YYYY-MM-DD')" button-text="Export to Excel"
            sheet-name="Tax Summary" />
          <button v-if="!download.request && !download.loading"
            class="inline-flex items-center px-4 py-2 rounded-md bg-blue-500 text-white hover:bg-blue-600 transition-colors"
            @click.prevent="downLoadTaxSummary()">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export CSV
          </button>
          <button v-else-if="!download.request && download.loading"
            class="inline-flex items-center px-4 py-2 rounded-md bg-blue-500 text-white">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
              viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
              </path>
            </svg>
            Processing...
          </button>
          <download v-else :file-title="download.fileTitle" :headers="download.headers" :items="download.items" />
        </div>
      </div>

      <auto-table :data="taxSummary" :loading="isLoading" :total-items="total" :items-per-page="limit"
        :current-page-prop="offset" :server-side-pagination="true" :pagination="total > limit" :show-items-count="true"
        :exclude-columns=excludeColumns :table-margin-bottom="0" @page-change="gotToPage" />

    </div>

  </div>
</template>

<script>
import moment from "moment";
import { mapActions } from "vuex";
import Download from "@/components/downloadCSV.vue";
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';
import ExcelExport from '@/components/ExcelExport.vue';
import AutoTable from "@/components/common/AutoTable.vue";

export default {
  components: {
    Download,
    AutoTable,
    CustomLoading,
    PageHeader,
    ExcelExport
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      //
      taxSummary: [],
      //
      selectedStatus: 'All', // Initially, no filter (show all)
      filteredTaxSummary: this.taxSummary, // Initially same as taxSummary
      //
  
      moreParams: {
        page: '',
        timestamp: 'timestamp',
        skip_cache: '',
        summary_date: '',
        limit: "10",
        export: "",
      },
      excludeColumns: [
        "trx_count",
        "updated_at",
      ],


      download: { request: false, loading: false, headers: {}, items: [], fileTitle: '' },

      // Excel export headers with better formatting
      exportHeaders: {
        summary_id: 'Summary ID',
        total_bets: 'Total Bets',
        total_stke: 'Total Stake',
        total_payout: 'Total Payout',
        total_deposits: "Total Deposits",
        total_withdrawals: "Total Withdrawals",
        ggr: 'Total GGR',
        excise_tax: 'Excise Tax',
        witholding_tax: 'Withholding Tax',
        betting_tax: "Betting Tax",
        tax_factor: "Tax Factor",
        summary_date: 'Summary Date',
        created_date: "Record Date"
      },
    }
  },
  mounted() {
    this.setTaxSummary()
  },
  methods: {
    ...mapActions(["getTaxSummary", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
      // this.isSideMenuOpen = !this.isSideMenuOpen;
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },
   
    // Pagination and dropdowns
    gotToPage(page) {
      let vm = this
      vm.moreParams.page = page
      vm.offset = page
      vm.setTaxSummary()
    },
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    toggleDetails(data) {
      this.viewModelOpen = true;
      this.gameTransaction = data;
      // Parse the JSON string in "extra_data" field
      const extraData = JSON.parse(data.extra_data);
      // console.log("extraData:", extraData)

      // Access individual properties
      this.gameTransactionId = extraData.game_transaction_id;
      this.gameRoundId = extraData.game_round_id;
      this.msisdn = extraData.msisdn;
      this.trx_id = extraData.trx_id;
      this.selection = extraData.selection;
      this.total_odd = extraData.total_odd;
      this.org_balance = extraData.org_balance;
      this.withdrawal_dlr_id = extraData.withdrawal_dlr_id;
    },

    async setTaxSummary() {
      this.isLoading = true

      const params = new URLSearchParams();

      for (const key in this.moreParams) {
        if (this.moreParams.hasOwnProperty(key)) {
          params.append(key, this.moreParams[key]);
        }
      }

      const queryString = params.toString();

      // console.log("Params getTaxSummary: " + queryString);

      let response = await this.getTaxSummary(queryString)

      // console.log("taxSummary OK: " + JSON.stringify(response.message.result))
      if (response.status === 200) {

        this.taxSummary = response.message.result
        this.total = parseInt(response.message.record_count)

        this.showDropdown = []
        for (let i = 0; i < this.taxSummary.length; i++) {
          this.showDropdown.push(false)
        }
      }

      this.isLoading = false
    },

    //
    async downLoadTaxSummary() {
      this.isLoading = true

      this.moreParams.export = "1"
      const params = new URLSearchParams();

      for (const key in this.moreParams) {
        if (this.moreParams.hasOwnProperty(key)) {
          params.append(key, this.moreParams[key]);
        }
      }

      const queryString = params.toString();

      // console.log("Params: " + queryString);

      let response = await this.getTaxSummary(queryString)

      this.download.items = response.message.result
      this.download.headers = {
        summary_id: 'SUMMARY ID',
        total_bets: 'TOTAL BETS',
        total_stke: 'TOTAL STAKE',
        total_payout: 'TOTAL PAYOUT',
        total_ggr: 'TOTAL GGR',
        excise_tax: 'EXCISE TAX',
        witholding_tax: 'WITHOLDING TAX',
        summary_date: 'SUMMARY DATE',
      }
      this.download.fileTitle = 'Tax Summary as at - ' + moment(new Date()).format('lll')
      this.download.loading = false
      this.download.request = true

      this.isLoading = false
    },


    //
  },
}
</script>

<style scoped>
/* Table styles */
table {
  border-collapse: separate;
  border-spacing: 0;
}

thead {
  background-color: #f9fafb;
}

th {
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

tbody tr:hover {
  background-color: #f9fafb;
}

/* Pagination styles */
.pagination-button {
  transition: all 0.2s ease;
}

.pagination-button:hover {
  background-color: #f3f4f6;
}

/* Modal styles */
.modal {
  transition: all 0.3s ease;
}

.modal-container {
  max-height: 90vh;
}
</style>
