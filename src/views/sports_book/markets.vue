<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Sports" pageSubtitle="Markets" />

    <div class="px-4 pb-4 grid grid-cols-1 gap-2 w-full sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-3 ">
      <!-- Search Input for Team Name -->
      <div class="flex items-end">
        <div class="w-full">
          <label class="block text-xs font-extrabold mb-1 text-gray-700">Search Market</label>
          <input
              v-model="marketsParams.market_name"
              type="text"
              class="w-full px-3 py-2 border rounded-md focus:ring focus:ring-blue-300"
              placeholder="Enter search term..."
              @keyup.enter="setMarkets"
          />
        </div>
        <button
            class="ml-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            @click="setMarkets">
          Search
        </button>
      </div>
    </div>

      <auto-table
        :headers="tableHeaders"
        :data="markets"
        :has-actions="true"
        :total-items="total"
        :items-per-page="limit"
        :current-page-prop="offset"
        :server-side-pagination="true"
        :pagination="total > limit"
        :show-items-count="true"
        :get-actions="getRowActions"
        @page-change="gotToPage"
      >
        <!-- Index Column -->
        <template #index="{ index }">
          <div class="text-center">{{ index + 1 + ((offset - 1) * limit) }}</div>
        </template>

        <!-- Market ID Column -->
        <template #market_id="{ item }">
          <div>{{ item.market_id }}</div>
        </template>

        <!-- Name Column -->
        <template #name="{ item }">
          <div>{{ item.name }}</div>
        </template>

        <!-- Alias Column -->
        <template #alias="{ item }">
          <div>{{ item.alias }}</div>
        </template>

        <!-- Priority Column -->
        <template #priority="{ item }">
          <div>{{ item.priority }}</div>
        </template>

        <!-- Date Column -->
        <template #created="{ item }">
          <div>
            <span style="font-size: 11px; color: grey">{{ moment(item.created).format('llll') }}</span>
          </div>
        </template>

        <template #actions="{ item, index }">
          <action-dropdown>
            <action-item @click="openUpdateModal(item, index)">
              <i class="fas fa-edit mr-2 text-indigo-600"></i> Update Market
            </action-item>
            <action-item @click="openDisableModal(item, index)">
              <i class="fas fa-ban mr-2 text-red-600"></i> Disable
            </action-item>
          </action-dropdown>
        </template>

      </auto-table>

    <!-- Update Market Modal -->
    <div v-if="isModalOpen" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Update Market <span class="font-light">({{ market.name }})</span></h3>
                <div class="mt-2 space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Alias <span class="font-light">({{ market.alias }})</span></label>
                    <input v-model="updateMarketParams.alias" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Enter alias">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Priority <span class="font-light">({{ market.priority }})</span></label>
                    <input v-model="updateMarketParams.priority" type="number" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Enter priority">
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button @click="updateMark" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
              Update
            </button>
            <button @click="closeModal" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Disable Market Modal -->
    <div v-if="isDisableModalOpen" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Disable Market</h3>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">Are you sure you want to disable this market? This action cannot be undone.</p>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
              Disable
            </button>
            <button @click="closeModal" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import {mapActions} from "vuex";
import VueDatePicker from "@vuepic/vue-datepicker";
import {AutoTable, ActionDropdown, ActionItem, CustomLoading} from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
    VueDatePicker,
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      showDropdown: [],
      isModalOpen: false,
      isDisableModalOpen: false,
      isRenameModalOpen: false,
      
      //
      market: '',
      markets: [],
      marketsParams: {
        market_name: '',
        sport_id: '',
        category_id: '',
        country: '',
        priority: '',
        timestamp: 'timestamp',
        limit: '10',
        page: ''
      },
      updateMarketParams: {
        // name: '',
        alias: '',
        priority: '',
        timestamp: 'timestamp',
      },
      tableHeaders: [
        { key: 'index', label: '#', align: 'center' },
        { key: 'market_id', label: 'Market_ID' },
        { key: 'name', label: 'Name' },
        { key: 'alias', label: 'Alias' },
        { key: 'priority', label: 'Priority' },
        { key: 'created', label: 'Date' },
      ]
    }
  },
  mounted() {
    this.setMarkets()
  },

  methods: {
    ...mapActions(["getMarkets", "updateMarket", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    getRowActions(item) {
      return [
        {
          label: 'Update Market',
          action: () => this.openUpdateModal(item),
          icon: 'edit',
          class: 'text-indigo-600'
        },
        {
          label: 'Disable',
          action: () => this.openDisableModal(item),
          icon: 'ban',
          class: 'text-red-600'
        }
      ];
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    // Format Date
    formatDate(date) {
      var d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    // Pagination and dropdowns
    gotToPage(page) {
      let vm = this
      vm.marketsParams.page = page
      vm.offset = page
      vm.setMarkets()
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    openUpdateModal(item, index) {
      this.market = item;
      this.isModalOpen = true;
      this.showDropdown[index] = false;
    },
    openDisableModal(item, index) {
      this.market = item;
      this.isDisableModalOpen = true;
      this.showDropdown[index] = false;
    },

    closeModal() {
      this.isModalOpen = false;
      this.isDisableModalOpen = false;
      this.isRenameModalOpen = false;
    },

    //
    async setMarkets() {
      let app = this
      app.isLoading = true
      app.marketsParams.limit = app.limit.toString()

      const params = new URLSearchParams();
      for (const key in app.marketsParams) {
        if (app.marketsParams.hasOwnProperty(key)) {
          params.append(key, app.marketsParams[key]);
        }
      }
      const queryString = params.toString();
      // console.log("Params: " + queryString);
      let response = await this.getMarkets(queryString)
      if (response.status === 200) {
        app.markets = response.message.result
        app.showDropdown = []
        for (let i = 0; i < app.markets.length; i++) {
          app.showDropdown.push(false)
        }

        if (response.message.record_count !== 0) {
          app.total = parseInt(response.message.record_count)
        }

      } else {
        app.total = 0;
        app.markets = []
      }
      app.isLoading = false
    },

    async updateMark() {
      let app = this
      app.updateMarketParams.market_id = this.market.market_id

      // validate
      if (app.updateMarketParams.alias === '' && app.updateMarketParams.priority === '') {
        app.$swal.fire('Error!', 'No field to update', 'error')
        return
      }

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this update this market!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (_) => {
          app.isLoading = true
          return await this.updateMarket(app.updateMarketParams)
        },
      })
          .then(async (response) => {
            app.isLoading = false

            console.log(JSON.stringify("up-Market: ", response))
            if (response.value.status === 200) {
              app.$swal.fire({
                title: 'Updated!',
                text: response.value.message,
                icon: 'success'
              }).then(async (_) => {
                app.updateMarketParams.priority = ''
                app.updateMarketParams.alias = ''
                app.market = {}
                await app.setMarkets()
              })

            } else {
              app.$swal.fire('Error!', response.message, 'error')
            }

          })


      app.closeModal()

    },


    //
  },
}
</script>
