<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>

    <div class="flex items-center p-6 pb-1 border-b gap-4 mb-5">
      <!-- Left Section: <PERSON>u <PERSON>ggle and <PERSON> Button -->
      <div class="flex items-center justify-center w-10 h-10 bg-gray-200 rounded-full cursor-pointer"
           @click="toggleSideM">
        <i v-if="!this.$store.state.isSideMenuOpen"
           class="fa fa-bars text-black text-lg"></i>
        <i v-else class="fa fa-close text-black text-lg"></i>
      </div>
      <button
          class="px-4 py-2 text-sm font-medium text-white bg-neutral-300 rounded-full hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 w-16 h-10"
          @click="goBack">
        Back
      </button>
      <!-- Middle Section: Name -->
      <div class="flex text-center font-extrabold ml-20" style="font-size: 30px">Odds Live</div>
    </div>

    <div v-if="viewModelOpen" class="flex px-4 pb-2">
      <div class="grid grid-cols-1 gap-2 w-full sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 ">

        <div class="block ">
          <label class="block text-xs text-gray-700">Sport</label>
          <v-select
              class="pt-1.5"
              v-model="sport"
              :options="sports"
              single
              placeholder="Select Sport"
          />
        </div>

      </div>
    </div>

    <div class="block py-4 bg-white rounded-lg shadow-lg mx-3 border overflow-x-auto">
      <table class="w-full mb-12 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2 px-2"> ID</th>
          <th class="py-2 px-2">Match Id</th>
          <th class="py-2">Odd Status</th>
          <th class="py-2">Outcome Name</th>
          <th class="py-2 pr-3 ">Odds</th>
          <th class="py-2 pr-3 ">Prev Odds</th>
          <th class="py-2">Direction</th>
          <th class="py-2">Producer Name</th>
          <th class="py-2 pr-3">Market Name</th>
          <th class="py-2 pr-2"> Status Name</th>
          <th class="py-2 pr-2">Market Priority</th>
          <th class="py-2 pr-2">Alias Priority</th>
<!--          <th class="py-2 pr-2">Date</th>-->
          <th class="py-2 pr-2 text-center">Actions</th>
        </tr>
        </thead>
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(item,index) in odds_live_list" :key="item.id">

          <td class="py-2 pr-2 px-2">
            {{ item.id }}
          </td>

          <td class="py-2  pr-2">
            <span>{{ (item.match_id) }}</span>
          </td>

          <td class="py-2  pr-3">{{ item.odd_status }}</td>

          <td class="py-2  pr-3"> {{ item.outcome_name }}</td>

          <td class="py-2 w-30 pr-3"> {{ item.odds }}</td>

          <td class="py-2 w-30 pr-3"> {{ item.prevous_odds }}</td>

          <td class="py-2 w-24 pr-3">
            <span>{{ item.direction }}</span>
          </td>

          <td class="py-2 w-24 pr-3">
            {{ item.producer_name }}
          </td>

          <td class="py-2 w-24 pr-3">
            {{ item.market_name }}
          </td>

          <td class="py-2 w-24 pr-3">
            {{ item.status_name }}
          </td>

          <td class="py-2 w-24 pr-3">
            {{ item.market_priority }}
          </td>

          <td class="py-2 w-24 pr-3">
            {{ item.alias_priority }}
          </td>

<!--          <td class="py-2 w-24 pr-3">
            <span style="font-size: 11px; color: grey">{{ moment(item.created).format('llll') }}</span>
          </td>-->

          <td class="py-2 text-center w-24">
            <div class="relative inline-block">
              <button
                  v-if="(parseInt(item.match_status) === 0) || (parseInt(item.match_status) === null)"
                  class="px-3 py-1 flex items-center space-x-1">

                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#808080" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <button v-else class="px-3 py-1 flex items-center space-x-1" @click="toggleDropdown(index)">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#000000" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <div v-if="showDropdown[index]" class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10"
                   style="width: 230px; text-align: left;">
                <ul class="py-2">

                  <li @click="">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-indigo-200" onclick="viewBetSlip">Show Bet
                      Slip</a>
                  </li>
                  <li @click="">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">Repost to BC</a>
                  </li>
                </ul>
              </div>

            </div>
          </td>

        </tr>
        </tbody>

      </table>

      <!-- Pagination -->
      <div class="flex-grow text-center sm:text-right" v-show="total > limit">
        <div class="inline-block bg-white border rounded-md divide-x text-xs sm:text-sm">
          <button class="p-2 sm:p-3 px-3 sm:px-5" v-if="offset > 1" @click="gotToPage(offset - 1)">&larr;</button>
          <button class="p-2 sm:p-3 px-3 sm:px-5" :class="{'bg-gray-100': offset === 1}" @click="gotToPage(1)">1
          </button>
          <button class="p-2 sm:p-3 px-3 sm:px-5" :class="{'bg-gray-100': offset === 2}"
                  v-show="Math.ceil(total / limit) > 1"
                  @click="gotToPage(2)">2
          </button>
          <button class="p-2 sm:p-3 px-3 sm:px-5" v-show="Math.ceil(total / limit) > 3">...</button>
          <button class="p-2 sm:p-3 px-3 sm:px-5" :class="{'bg-gray-100': offset === offset}"
                  v-show="Math.ceil(total / limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
          </button>
          <button class="p-2 sm:p-3 px-3 sm:px-5" :class="{'bg-gray-100': offset === Math.ceil(total / limit)}"
                  v-show="Math.ceil(total / limit) > 4" @click="gotToPage(Math.ceil(total / limit))">
            {{ Math.ceil(total / limit) }}
          </button>
          <button class="p-2 sm:p-3 px-3 sm:px-5" v-show="(offset * limit) < total" @click="gotToPage(offset + 1)">
            &rarr;
          </button>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
// import numeral from "numeral"
import {mapActions} from "vuex";
import {endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths} from "date-fns";
import VueDatePicker from "@vuepic/vue-datepicker";

export default {
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      //
      odds_live: {},
      odds_live_list: [],
      moreParams: {
        match_id: '',
        status: '',
        sort: '',
        start: '',
        end: '',
        page: '',
        limit: "10",
        timestamp: 'timestamp',
        skip_cache: '',
      },

      //
    }
  },
  components: {
    VueDatePicker,
    Loading
  },
  mounted() {
    this.moreParams.match_id = this.$route.params.match_id ?? ""
    this.setOddsLive()
    // this.setFixtures()
  },

  methods: {
    ...mapActions(["getOddsLive", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    async onCountryChange(value) {
      if (!value) {
        this.moreParams.country = ''
        await this.setOddsLive()
      } else {
        this.moreParams.country = value.label
        await this.setOddsLive()
      }
    },

    // Format Date
    formatDate(date) {
      var d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    // Pagination and dropdowns
    gotToPage(page) {
      let vm = this
      vm.moreParams.page = page
      vm.offset = page
      vm.setOddsLive()
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    toggleDetails(data) {
      this.viewModelOpen = true;
      this.market = data;
    },

    async setOddsLive() {
      let app = this
      app.isLoading = true

      const params = new URLSearchParams();

      for (const key in app.moreParams) {
        if (app.moreParams.hasOwnProperty(key)) {
          params.append(key, app.moreParams[key]);
        }
      }

      const queryString = params.toString();

      console.log("Params: " + queryString);

      let response = await this.getOddsLive(queryString)

      // console.log("setOddsLive OK: " + JSON.stringify(response))
      if (response.status === 200) {
        app.odds_live_list = response.message.result;

        if (response.message.record_count !== 0) {
          // app.total = parseInt(app.sports_book[0].trx_count)
          app.total = response.message.record_count
        }

        app.showDropdown = []
        for (let i = 0; i < app.odds_live_list.length; i++) {
          app.showDropdown.push(false)
        }
      } else {
        app.odds_live_list = [];
        app.total = 0;
      }
      // app.filterByStatus()
      app.isLoading = false
    },

    async setCountries() {
      let app = this
      app.isLoading = true

      const params = new URLSearchParams();

      for (const key in app.countryParams) {
        if (app.countryParams.hasOwnProperty(key)) {
          params.append(key, app.countryParams[key]);
        }
      }

      const queryString = params.toString();

      console.log("Params: " + queryString);

      let response = await this.getCountries(queryString)

      console.log("Countries OK: " + JSON.stringify(response))
      if (response.status === 200) {

        response.message.result.forEach(function (item) {
          let list = {label: item.country, value: parseInt(item.id)}
          app.countries.push(list)
        })

        if (response.message.record_count !== 0) {
          app.total = parseInt(response.message.record_count)
        }

      } else {

      }
      // app.filterByStatus()
      app.isLoading = false
    },

    async setFixtures() {
      let app = this
      app.isLoading = true

      const params = new URLSearchParams();

      for (const key in app.sportsParams) {
        if (app.sportsParams.hasOwnProperty(key)) {
          params.append(key, app.sportsParams[key]);
        }
      }

      const queryString = params.toString();

      console.log("Params: " + queryString);

      let response = await this.getSports(queryString)

      console.log("Sports OK: " + JSON.stringify(response))
      if (response.status === 200) {

        response.message.result.forEach(function (item) {
          let list = {label: item.sport_name, value: parseInt(item.sport_id)}
          app.sports.push(list)
        })

        if (response.message.record_count !== 0) {
          app.total = parseInt(response.message.record_count)
        }

      } else {

      }
      // app.filterByStatus()
      app.isLoading = false
    },


    //
  },
}
</script>
