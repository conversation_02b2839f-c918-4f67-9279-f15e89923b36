<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Sports" pageSubtitle="Tournaments" />


    <!-- Filter Cards -->
    <div class="px-4 pb-4 mb-2">
      <div class="bg-white rounded-lg shadow-sm border p-4">
        <h3 class="text-sm font-medium mb-3 text-gray-700">Filters</h3>

        <div class="grid grid-cols-1 gap-4 w-full sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4">
          <!-- Sport Filter -->
          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Sport</label>
            <v-select
              class="text-sm"
              v-model="sport"
              :options="sports"
              single
              clearable
              placeholder="Select Sport"
              @update:modelValue="onSportChange"
            />
          </div>

          <!-- Country Filter -->
          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Country</label>
            <v-select
              class="text-sm"
              v-model="country"
              :options="countries"
              single
              placeholder="Select a Country"
              clearable
              @update:modelValue="onCountryChange"
            />
          </div>

          <!-- Tournament Name Filter -->
          <div class="block">
            <label class="block text-xs font-bold text-gray-700 mb-1">Tournament Name</label>
            <input
              type="text"
              v-model="tournamentsParams.tournament_name"
              placeholder="Search by name"
              class="w-full px-3 py-1.5 text-sm border rounded-md shadow-sm"
              @keyup.enter="setTournaments()"
            />
          </div>

          <!-- Filter Buttons -->
          <div class="flex items-end space-x-2">
            <button
              @click="setTournaments()"
              class="px-4 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
            >
              Apply Filters
            </button>
            <button
              @click="resetFilters()"
              class="px-4 py-1.5 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm"
            >
              Reset
            </button>
          </div>
        </div>
      </div>
    </div>


      <auto-table
        :headers="tableHeaders"
        :data="tournaments"
        :has-actions="true"
        :loading="isLoading"
        :sortable="true"
        :total-items="total"
        :current-page-prop="offset"
        :items-per-page="limit"
        :server-side-pagination="true"
        :pagination="total > limit"
        :show-items-count="true"
        :table-class="'mb-6'"
        @page-change="gotToPage"
        @sort-change="handleSortChange"
      >
        <!-- Tournament ID Column -->
        <template #tournament_id="{ item }">
          <span class="font-medium">{{ item.tournament_id }}</span>
        </template>

        <!-- Tournament Name Column -->
        <template #tournament_name="{ item }">
          <span class="font-medium">{{ item.tournament_name }}</span>
          <br>
          <!-- season -->
           <span class="text-xs text-gray-500">({{ item.season_name }})</span>
        </template>

        <!-- Sport Name Column -->
        <template #sport_name="{ item }">
          <span>{{ item.sport_name }}</span>
          <br>
          <span>{{ item.country }}</span>
        </template>

        <!-- Priority Column -->
        <template #priority="{ item }">
          <div class="flex space-x-2">
            <span class="inline-flex items-center justify-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
               {{ item.priority }}
            </span>
            <br>
            <span class="inline-flex items-center justify-center p-2 rounded-full text-xs font-medium"
                  :class="parseInt(item.tournament_priority) === 1 ? 'bg-green-200 text-green-800' : 'bg-orange-200 text-gray-800'">
               {{ item.tournament_priority }}
            </span>
          </div>
        </template>

        <!-- Date Column -->
        <template #created="{ item }">
          <span style="font-size: 11px; color: grey">{{ moment(item.created).format('llll') }}</span>
        </template>

        <!-- Actions Column -->
        <template #actions="{ item, index }">
          <action-dropdown>
            <action-item @click="openPriorityModal(item, index)">
              <i class="fas fa-edit mr-2 text-indigo-600"></i> Update Tournament
            </action-item>
            <action-item @click="openDisableModal(item, index)">
              <i class="fas fa-ban mr-2 text-red-600"></i> Disable
            </action-item>
          </action-dropdown>
        </template>
      </auto-table>

      <!-- Modal -->
      <div v-if="isPriorityModalOpen"
           class="fixed inset-0 z-10 bg-black bg-opacity-50 flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
          <h2 class="text-lg font-semibold mb-4">Update Tournament </h2>

          <!-- Priority Input -->
          <div class="mb-6">
            <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
              Priority <span class="font-light">(Current: {{this.tournament.priority }})</span>
            </label>
            <input
                type="number"
                id="priority"
                v-model="updateTournamentParams.priority"
                class="mt-1 block w-full border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                placeholder="Enter priority"
            />
          </div>

          <!-- Tournament Priority Input -->
          <div class="mb-6">
            <label for="tournament_priority" class="block text-sm font-medium text-gray-700 mb-2">
              Tournament Priority <span class="font-light">(Current: {{this.tournament.tournament_priority }})</span>
            </label>
            <input
                type="number"
                id="tournament_priority"
                v-model="updateTournamentParams.tournament_priority"
                class="mt-1 block w-full border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                placeholder="Enter tournament priority"
            />
          </div>
          <!-- Input Fields -->
          <div class="mb-4">
            <label for="name" class="block text-sm font-medium text-gray-700">
              Tournament Name <span class="font-light">({{ this.tournament.tournament_name }})</span>
            </label>
            <input
                type="text"
                id="name"
                v-model="updateTournamentParams.tournament_name"
                class="mt-1 block w-full border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                placeholder="Enter new name"
            />
          </div>

          <div class="mb-4">
            <label for="category_name" class="block text-sm font-medium text-gray-700">
              Season Name <span class="font-light">({{ this.tournament.season_name }})</span>
            </label>
            <input
                type="text"
                id="category_name"
                v-model="updateTournamentParams.season_name"
                class="mt-1 block w-full border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
                placeholder="Enter new name"
            />
          </div>

          <!-- Modal Buttons -->
          <div class="flex justify-end space-x-4">
            <button @click="closeModal()" class="px-4 py-2 bg-gray-300 rounded-md hover:bg-gray-400">
              Cancel
            </button>
            <button @click="updateTourn()" class="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600">
              Save
            </button>
          </div>
        </div>
      </div>

  </div>
</template>

<script>
import moment from "moment";
import {mapActions} from "vuex";
import VueDatePicker from "@vuepic/vue-datepicker";
import {AutoTable, ActionDropdown, ActionItem, CustomLoading} from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
    VueDatePicker,
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],
      isPriorityModalOpen: false,
      isDisableModalOpen: false,

      // Table headers for DataTable
      tableHeaders: [
        { key: 'tournament_id', label: 'Tournament ID', sortable: true },
        { key: 'tournament_name', label: 'Name', sortable: true },
        // { key: 'season_name', label: 'Season', sortable: true },
        // { key: 'sport_id', label: 'Sports ID', sortable: true },
        { key: 'sport_name', label: 'Sports Name & Country', sortable: true },
        // { key: 'country', label: 'Country', sortable: true },
        { key: 'priority', label: 'Priorities', sortable: true },
        { key: 'created', label: 'Date', sortable: true }
      ],

      // Sort parameters
      sortKey: 'tournament_id',
      sortOrder: 'asc',

      // Filter values
      country: '',
      countries: [],
      countryParams: {
        timestamp: 'timestamp',
        limit: '1000',
        sort: '',
        start: '',
        end: '',
        page: '',
      },

      // Sports filter
      sport: '',
      sports: [],
      sportsParams: {
        sport_id: '',
        sport_name: '',
        priority: '',
        timestamp: 'timestamp',
        limit: '1000000',
        sort: '',
        start: '',
        end: '',
        page: '',
      },

      // Tournament data
      tournament: '',
      tournaments: [],
      tournamentsParams: {
        sport_id: '',
        category_id: '',
        country: '',
        priority: '',
        tournament_name: '', // Added for name search
        timestamp: 'timestamp',
        limit: '10000',
        page: '',
        sort_by: 'tournament_id', // Added for sorting
        sort_order: 'asc' // Added for sorting
      },
      updateTournamentParams: {
        tournament_name: '',
        season_name: '',
        priority: '',
        tournament_priority: '',
        timestamp: 'timestamp',
      },
    }
  },
  mounted() {
    this.setTournaments()
    this.setCountries()
    this.setFixtures()
  },

  methods: {
    ...mapActions(["getCountries", "getSports", "getTournaments", "updateTournament", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    // Handle sort change from DataTable
    handleSortChange({ key, order }) {
      this.sortKey = key;
      this.sortOrder = order;
      this.tournamentsParams.sort_by = key;
      this.tournamentsParams.sort_order = order;
      this.setTournaments();
    },

    // Reset all filters
    resetFilters() {
      this.sport = '';
      this.country = '';
      this.tournamentsParams = {
        sport_id: '',
        category_id: '',
        country: '',
        priority: '',
        tournament_name: '',
        timestamp: 'timestamp',
        limit: '10000',
        page: '1',
        sort_by: 'tournament_id',
        sort_order: 'asc'
      };
      this.offset = 1;
      this.setTournaments();
    },

    async onCountryChange(value) {
      if (!value) {
        this.tournamentsParams.country = ''
        await this.setTournaments()
      } else {
        this.tournamentsParams.country = value.label
        await this.setTournaments()
      }
    },

    async onSportChange(value) {
      if (!value) {
        this.tournamentsParams.sport_id = ''
        await this.setTournaments()
      } else {
        this.tournamentsParams.sport_id = value.value
        await this.setTournaments()
      }
    },

    formatDate(date) {
      var d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    // Pagination and dropdowns
    gotToPage(page) {
      let vm = this
      vm.tournamentsParams.page = page
      vm.offset = page
      vm.setTournaments()
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    openPriorityModal(item, index) {
      this.tournament = item;
      // Set initial values - both priority fields as number inputs
      this.updateTournamentParams.priority = item.priority || "";
      this.updateTournamentParams.tournament_priority = item.tournament_priority || "";
      this.updateTournamentParams.tournament_name = item.tournament_name || "";
      this.updateTournamentParams.season_name = item.season_name || "";
      this.isPriorityModalOpen = true;
      this.showDropdown[index] = false;
    },
    openDisableModal(item, index) {
      this.tournament = item;
      this.isDisableModalOpen = true;
      this.showDropdown[index] = false;
    },

    closeModal() {
      this.isPriorityModalOpen = false;
      this.isDisableModalOpen = false;
    },


    async setCountries() {
      let app = this
      app.isLoading = true

      const params = new URLSearchParams();
      for (const key in app.countryParams) {
        if (app.countryParams.hasOwnProperty(key)) {
          params.append(key, app.countryParams[key]);
        }
      }
      const queryString = params.toString();
      // console.log("Params: " + queryString);
      let response = await this.getCountries(queryString)
      // console.log("Countries OK: " + JSON.stringify(response))
      if (response.status === 200) {
        response.message.result.forEach(function (item) {
          let list = {label: item.country, value: parseInt(item.id)}
          app.countries.push(list)
        })

        if (response.message.record_count !== 0) {
          app.total = parseInt(response.message.record_count)
        }
      } else {
        app.countries = [];
      }

      app.isLoading = false
    },

    async setFixtures() {
      let app = this
      app.isLoading = true

      const params = new URLSearchParams();
      for (const key in app.sportsParams) {
        if (app.sportsParams.hasOwnProperty(key)) {
          params.append(key, app.sportsParams[key]);
        }
      }
      const queryString = params.toString();
      // console.log("Params: " + queryString);
      let response = await this.getSports(queryString)
      // console.log("Sports OK: " + JSON.stringify(response))
      if (response.status === 200) {
        response.message.result.forEach(function (item) {
          let list = {label: item.sport_name, value: parseInt(item.sport_id)}
          app.sports.push(list)
        })

        if (response.message.record_count !== 0) {
          app.total = parseInt(response.message.record_count)
        }

      } else {

      }

      app.isLoading = false
    },

    async setTournaments() {
      let app = this
      app.isLoading = true

      const params = new URLSearchParams();
      for (const key in app.tournamentsParams) {
        if (app.tournamentsParams.hasOwnProperty(key)) {
          params.append(key, app.tournamentsParams[key]);
        }
      }
      const queryString = params.toString();
      // console.log("Params: " + queryString);
      let response = await this.getTournaments(queryString)
      // console.log("setTournaments OK: " + JSON.stringify(response.message.result.length))
      if (response.status === 200) {
        app.tournaments = response.message.result
        app.showDropdown = []
        for (let i = 0; i < app.tournaments.length; i++) {
          app.showDropdown.push(false)
        }
        // console.log("app.tournaments OK: " + JSON.stringify(app.tournaments.length))

        if (response.message.record_count !== 0) {
          app.total = parseInt(response.message.record_count)
        }

      } else {

        app.tournaments = []
        // console.log("app.tournaments !OK: " + JSON.stringify(app.tournaments.length))
      }
      // app.filterByStatus()
      app.isLoading = false
    },

    async updateTourn() {
      let app = this
      app.isLoading = true
      // app.updateTournamentParams.tournament_id = this.tournament.id
      app.updateTournamentParams.tournament_id = this.tournament.tournament_id

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this update this tournament!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (_) => {
          return await this.updateTournament(app.updateTournamentParams)
        },
      })
          .then(async (response) => {
            app.isLoading = false

            if (response.value.status === 200) {
              app.$swal.fire({
                title: 'Updated!',
                text: response.value.message,
                icon: 'success'
              }).then(async (_) => {
                app.updateTournamentParams.priority = ''
                app.updateTournamentParams.tournament_priority = ''
                app.updateTournamentParams.tournament_name = ''
                app.updateTournamentParams.season_name = ''
                app.tournament = {}
                await app.setTournaments()
              })

            } else {
              app.$swal.fire('Error!', response.message, 'error')
            }

          })


      app.closeModal()

    },


    //
  },
}
</script>
