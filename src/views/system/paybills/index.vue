<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
    <page-header pageName="System" pageSubtitle="PayBills" />

    <div class="w-full flex mb-4 px-4 text-xs gap-2 justify-end items-end">
      <router-link class="inline-block px-4 py-2 rounded-md bg-primary font-bold"
                   :to="{ name: 'paybills-add' }">
        Add PayBill
      </router-link>
    </div>

    <!-- PayBill Config -->
    <div v-show="paybills.length>0" class=" py-4 rounded-lg shadow-lg mx-3 border overflow-x-auto">
      <table class="w-full mb-12 table">
        <thead class="border-b-2 border-gray-400 text-xs text-left">
        <tr class="table-row">
          <th class="py-2 pl-5">Paybill Type</th>
          <th class="py-2">Initiator Name</th>
          <th class="py-2">Paybill Number</th>
<!--          <th class="py-2">Date</th>-->
          <th class="py-2 text-center">Status</th>
          <th class="py-2 text-center">Action</th>
        </tr>
        </thead>
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(item,index) in paybills" :key="item.id">

          <td class="py-2 pl-5 border-b">{{ item.paybill_type === '2' ? "B2C" : "C2B" }}</td>

          <td class="py-2 border-b">{{ item.initiator_name }}</td>

          <td class="py-2 border-b"><span>{{ item.paybill_number }}</span></td>

<!--          <td class="py-2 border-b whitespace-nowrap"><span>{{ moment(item.created_at).format('llll') }}</span></td>-->

          <td class="py-2 text-center border-b">
            <div v-if="parseInt(item.status) === 1" class="action-button"
                 style="background-color: green; padding: 8px; border-radius: 4px; color: white; cursor: pointer;">
              Active
            </div>
            <div v-else-if="parseInt(item.status) === 3" class="action-button"
                 style="background-color: red; padding: 8px; border-radius: 4px; color: white; cursor: pointer;">
              In-Active
            </div>

          </td>

          <td class="py-2 text-center border-b">
            <div class="relative inline-block">
              <button
                  v-if="parseInt(item.status) !== 0"
                  class="px-3 py-1 flex items-center space-x-1"
                  @click="toggleDropdown(index)">

                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#000000" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <button v-else class="px-3 py-1 flex items-center space-x-1">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#808080" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>

              <div v-if="showDropdown[index]" class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10"
                   style="width: 230px; text-align: left;">
                <ul class="py-2">

                  <li v-if="parseInt(item.paybill_type)===2 && parseInt(item.service_route)===2"
                      @click="switchGates(item,1)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Switch to Daraja</a>
                  </li>

                  <li v-if="parseInt(item.paybill_type)===2 && parseInt(item.service_route)===2"
                      @click="switchGates(item,1)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Switch to Daraja</a>
                  </li>

                  <li v-if="parseInt(item.paybill_type)===2 && parseInt(item.status)===1"
                      @click="switchGates(item,2)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Switch to Broker</a>
                  </li>

                  <li v-if="parseInt(item.paybill_type)===1 && parseInt(item.paybill_status) === 1"
                      @click="disableEnable(item,0)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Disable Deposits</a>
                  </li>

                  <li v-if="parseInt(item.paybill_type)===1 && parseInt(item.status) !== 3"
                      @click="disableEnable(item,3)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Suspend Deposits</a>
                  </li>

                  <li v-if="item.paybill_type==='1'&& parseInt(item.status) !== 1"
                      @click="disableEnable(item,1)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Enable Deposits</a>
                  </li>

                  <li v-if="item.paybill_type==='2'&& parseInt(item.status) !== 1"
                      @click="disableEnable(item,1)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Enable Payout</a>
                  </li>

                  <li v-if="item.paybill_type==='2'&& parseInt(item.status) === 1"
                      @click="disableEnable(item,0)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Disable Payout</a>
                  </li>

                  <li v-if="item.paybill_type==='2'&& parseInt(item.status) !== 3"
                      @click="disableEnable(item,3)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Suspend Payout</a>
                  </li>

                  <li @click="editPaybill(item)">
                    <a class="block px-3 py-2 cursor-pointer hover:bg-blue-200">Edit PayBill</a>
                  </li>

                </ul>
              </div>

            </div>
          </td>

        </tr>
        </tbody>
      </table>

    </div>
    <!--  End PayBill  -->

<!--  Add  -->

  </div>
</template>

<script>
import {mapActions} from "vuex";
import moment from "moment-timezone";
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    CustomLoading,
    PageHeader,
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],
      //
      requestPayload: {
        status: "",
        start: "",
        end: "",
        page: 1,
        timestamp: Date.now(),
        skip_cache: false,
        paybill_type: "",
        paybill_number: "",
        paybill_status: "",
      },
      paybills: [],


      statuses: [
        {text: 'Active', value: 1},
        {text: 'Pending Approval', value: 2},
        {text: 'Suspended', value: 3},
      ],
    }

  },
  async mounted() {
    await this.setPaybills()
  },

  methods: {
    ...mapActions(["getPaybills", "fillPayBill", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    //
    async setPaybills() {
      let app = this
      app.isLoading = true

      let response = await this.getPaybills(app.requestPayload)

      // console.log("getPaybills response: ", JSON.stringify(response))

      if (response.status === 200) {
        app.paybills = response.message.result
        app.total = response.message.total_count

        app.showDropdown = []
        for (let i = 0; i < app.paybills.length; i++) {
          app.showDropdown.push(false)
        }
      }

      app.isLoading = false

    },


    async editPaybill(item) {
      this.closeDropdown()
      await this.fillPayBill(item);
      await this.$router.push({name: 'paybills-edit'})
    },

    async switchGates(item, actionType) {
      let app = this
      app.closeDropdown()
      let id = item.id

      app.moreParams2.action_type = actionType
      app.moreParams2.timestamp = Date.now()
      let payload = app.moreParams2
      payload.id = id
      // console.log('payload!', payload)

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this change Paybill Gateway",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, change!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.switchPaybillRoute(payload)
        },
      })
          .then(async (result) => {
            if (result.value.status === 200) {
              app.link = result.value.message
              app.$swal.fire('Added!', result.value.message, 'success')
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    async disableEnable(item, status) {
      let app = this
      let id = item.id

      app.moreParams3.status = status
      app.moreParams3.timestamp = Date.now()
      let payload = app.moreParams3
      payload.id = id

      const result = await this.disableEnablePaybill(payload)

      if (result.status === 200) {
        app.link = result.value.message
        await app.setPaybills()
        app.$swal.fire('Added!', result.message, 'success')
      } else {
        app.$swal.fire('Error!', result.message, 'error')
      }

    },

    //
    toggleDetails(item) {
      console.log("paybills item: ", item)
      // this.$router.push({name: 'paybills-view', params: {id: item.id}})
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },


  },
}
</script>
