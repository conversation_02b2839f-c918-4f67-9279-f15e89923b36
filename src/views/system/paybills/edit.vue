<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
    <page-header pageName="System" pageSubtitle="Edit PayBill" />

    <div class="block px-6  ">
      <!--Full Name, Phone, Email -->

      <div class="grid grid-cols-3 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block ">Paybill Number</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number"
                 v-model="form.paybill_number" placeholder="736736" disabled>
        </div>

        <div class="block">
          <label class="text-xs mb-1 block ">Paybill Type</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.paybill_type">
            <option value="" disabled selected>Select Paybill Type</option>
            <option v-for="type in paybill_types" :value="type.value">
              {{ type.text }}
            </option>
          </select>
        </div>

        <div class="block mb-4">
          <label v-if="parseInt(form.paybill_type)===2" class="text-xs mb-1 block ">Action Type</label>
          <select v-if="parseInt(form.paybill_type)===2"
                  class="w-full block px-4 py-2 border bg-white rounded-md outline-none"
                  v-model="form.action_type">
            <option value="" disabled selected>Select Action Type</option>
            <option v-for="type in action_types" :value="type.value">
              {{ type.text }}
            </option>
          </select>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="block mb-4">
          <label class="text-xs mb-1 block font-medium">Initiator Name</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="form.initiator_name" placeholder="Name">
        </div>

        <div class="block mb-4">
          <label class="text-xs mb-1 block font-medium">Initiator Password</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="form.initiator_pwd" placeholder="******">
        </div>
      </div>

      <div class="grid grid-cols-3 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block ">Secret Key</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="form.secret_key" placeholder="Secret key">
        </div>

        <div class="block mb-4">
          <label class="text-xs mb-1 block font-medium">Consumer Key</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="form.consumer_key" placeholder="Consumer key">
        </div>

        <div class="block mb-4">
          <label class="text-xs mb-1 block font-medium">Service App Key</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="text"
                 v-model="form.service_app_key" placeholder="Service app key">
        </div>
      </div>


      <!--Buttons-->
      <div class="gap-4 block text-sm text-right mt-10">
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" :to="{name: 'paybills'}">
          Cancel
        </router-link>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20"
                @click.prevent="editPaybill"
                id="addSystemUser">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat>
          Submit
        </button>
      </div>

    </div>
  </div>
</template>

<script>

import {mapActions} from "vuex";
import VueDatePicker from "@vuepic/vue-datepicker";
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    VueDatePicker,
    CustomLoading,
    PageHeader,
  },
  data() {
    return {
      isLoading: false,
      loading: true,
      fullPage: true,
      //
      roles: [],
      showPassword: false,
      form: {
        timestamp: '',
        paybill_number: '',
        paybill_type: '',
        action_type: '',
        initiator_name: '',
        initiator_pwd: '',
        secret_key: '',
        consumer_key: '',
        service_app_key: '',
      },
      paybill_types: [
        {text: 'C2B', value: 1},
        {text: 'B2C', value: 2},
      ],

      action_types: [
        {text: 'Daraja', value: 1},
        {text: 'Broker', value: 2},
      ],
    }
  },
  mounted() {
    this.form = this.$store.state.paybill
    console.log("EDIT PAYBILL: ", JSON.stringify(this.form))
  },

  methods: {
    ...mapActions(["editPayBill", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },


    async editPaybill() {
      let app = this

      this.form.timestamp = Date.now();
      const payload = this.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this Modifies this PayBill!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.editPayBill(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Added!', result.value.message, 'success')
              await this.$router.push({name: 'user'})
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },
    togglePasswordVisibility() {
      this.showPassword = !this.showPassword;
    },
  }
}
</script>
