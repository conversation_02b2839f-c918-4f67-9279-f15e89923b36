<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
    <page-header pageName="System" pageSubtitle="Users" />

    <div class="w-full flex mb-4 px-4 text-xs gap-2 justify-end items-end">
      <router-link class="inline-block px-4 py-2 rounded-md bg-primary font-bold text-white"
                   :to="{ name: 'system-users-add' }">
        Add System User
      </router-link>
    </div>

    <div class="px-3 pb-12">
      <auto-table
        v-if="data.length > 0"
        :headers="tableHeaders"
        :data="data"
        :has-actions="true"
        :total-items="total"
        :items-per-page="limit"
        :current-page-prop="offset"
        :server-side-pagination="true"
        :pagination="total > limit"
        :show-items-count="true"
        @page-change="gotToPage"
      >
        <!-- Index Column -->
        <template #index="{ index }">
          <div>{{ (index + 1) + ((offset - 1) * limit) }}</div>
        </template>

        <!-- Display Name Column -->
        <template #display_name="{ item }">
          <div class="font-medium">{{ item.display_name }}</div>
        </template>

        <!-- Username Column -->
        <template #user_name="{ item }">
          <div>
            {{ item.user_name }}
            <br>
            {{ item.msisdn }}
          </div>
        </template>

        <!-- Role Column -->
        <template #role_name="{ item }">
          <div>{{ item.role_name }}</div>
        </template>

        <!-- Login Attempts Column -->
        <template #login_attempts="{ item }">
          <div>
            <span class="text-green-600">Successful: {{ item.cumulative_success_login }}</span>
            <br>
            <span class="text-red-600">Failed: {{ item.cumlative_failed_attempts }}</span>
          </div>
        </template>

        <!-- Last Login Column -->
        <template #last_logged_on="{ item }">
          <div class="whitespace-nowrap">{{ moment(item.last_logged_on).format('lll') }}</div>
        </template>

        <!-- Status Column -->
        <template #status="{ item }">
          <div class="text-center">
            <span 
              class="inline-block px-3 py-1 rounded-md text-white text-xs"
              :class="{
                'bg-green-600': parseInt(item.status) === 1,
                'bg-red-600': parseInt(item.status) === 3,
                'bg-purple-600': parseInt(item.status) !== 1 && parseInt(item.status) !== 3
              }"
            >
              {{ parseInt(item.status) === 1 ? 'Active' : parseInt(item.status) === 3 ? 'Deactivated' : 'Inactive' }}
            </span>
          </div>
        </template>

        <!-- Actions Column -->
        <template #actions="{ item }">
          <action-dropdown>
            <action-item 
              v-if="parseInt(item.status) === 1"
              @click="editRow(item)"
            >
              <template #icon>
                <svg class="mr-2 h-4 w-4 text-orange-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </template>
              Edit User 
            </action-item>
            
            <action-item @click="resend(item)">
              <template #icon>
                <svg class="mr-2 h-4 w-4 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              </template>
              Resend OTP
            </action-item>
          </action-dropdown>
        </template>
      </auto-table>
    </div>
  </div>
</template>

<script>
import {AutoTable, ActionDropdown, ActionItem, CustomLoading} from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';
import moment from "moment";
import {mapActions} from "vuex";
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
import Swal from 'sweetalert2';

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
    VueDatePicker
  },
  data() {
    return {
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: this.$store.state.client_name,
      clientName: "",
      organisations: [],
      data: [],
      roles: [],

      //
      fullPage: true,
      total: 0,
      offset: 1,
      limit: 100,
      showDropdown: [],

      isOpen: false,
      isLoading: false,
      //

      moreParams: {
        status: "",
        user_name: "",
        start: "",
        end: "",
        limit: "100",
        page: "1",
        timestamp: "timestamp",
        skip_cache: "",
      },
      time3: "",
      moment: moment,
      id: null,
      status: null,
      
      // Table headers for DataTable
      tableHeaders: [
        { label: '#', key: 'index', sortable: false },
        { label: 'Display name', key: 'display_name', sortable: true },
        { label: 'Username', key: 'user_name', sortable: true },
        { label: 'Role', key: 'role_name', sortable: true },
        { label: 'Login Attempts', key: 'login_attempts', sortable: false },
        { label: 'Last Login', key: 'last_logged_on', sortable: true },
        { label: 'Status', key: 'status', sortable: true, align: 'center' },
        // { text: 'Actions', value: 'actions', sortable: false, align: 'center' }
      ]
    }
  },

  async mounted() {
    //
    await this.setUsers()
  },

  methods: {
    ...mapActions(["getSystemUsers", "getSystemRoles", "sendUserOTP", "fillSystemUser", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    //
    gotToPage(page) {
      let vm = this
      vm.moreParams.page = page
      vm.offset = page
      vm.setUsers()
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    async resend(item) {
      let app = this
      const payload = {
        timestamp: Date.now(),
        user_id: item.user_id,
      }
      console.log("resendOTPhg payload: ",JSON.stringify(payload))

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this resends otp to the user!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, resend!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.sendUserOTP(payload)
        },
      })
      .then(async (result) => {
        console.log("result: " + JSON.stringify(result))
        if (result.value.status === 200) {
          app.$swal.fire('Sent!', result.value.message, 'success')
        } else {
          app.$swal.fire('Error!', result.value.message, 'error')
        }
      })
    },

    async editRow(row) {
      await this.fillSystemUser(row)
      console.log("fillUser -to edit : ", JSON.stringify(row))
      await this.$router.push({name: 'system-users-edit'})
    },

    async setUsers() {
      let app = this
      this.isLoading = true
      app.data = []
      app.moreParams.timestamp = Date.now()
      let response = await this.getSystemUsers(app.moreParams)
      // console.log("USERS: ", JSON.stringify(response))
      if (response.status === 200) {
        app.data = response.message.result
        app.total = parseInt(response.message.record_count)
      }
      this.isLoading = false
    },
  }
}
</script>

<style scoped>
/* Add any custom styles here */
</style>
