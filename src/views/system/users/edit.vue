<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
    <page-header pageName="System" pageSubtitle="Edit User" />

    <div class="block px-6 py-4 mx-3">
      <!-- Two-column layout for the first two cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <!-- User Information Card -->
        <div class="filter-card p-4 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="font-medium text-gray-700 mb-3 text-sm">User Information</h4>
          <div class="space-y-4">
            <!-- Full Name -->
            <div class="block">
              <label class="block text-xs font-medium text-gray-700 mb-1">User Names</label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-sm" 
                     v-model="form.display_name">
            </div>
            
            <!-- Phone Number -->
            <div class="block">
              <label class="block text-xs font-medium text-gray-700 mb-1">Phone Number</label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-sm" 
                     v-model="form.msisdn" disabled>
            </div>
          </div>
        </div>
        
        <!-- Contact & Role Card -->
        <div class="filter-card p-4 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="font-medium text-gray-700 mb-3 text-sm">Contact & Role</h4>
          <div class="space-y-4">
            <!-- Email -->
            <div class="block">
              <label class="block text-xs font-medium text-gray-700 mb-1">Email</label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-sm" 
                     v-model="form.user_name">
            </div>
            
            <!-- Role Section -->
            <div class="block">
              <label class="block text-xs font-medium text-gray-700 mb-1">Assign Role</label>
              <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-sm" v-model="form.role_id">
                <option value="" disabled selected>Choose one Role</option>
                <option v-for="role in roles" :value="role.value">
                  {{ role.text }}
                </option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <!-- Permissions Section -->
      <div class="filter-card p-4 border rounded-md hover:border-indigo-300 transition-colors mb-4">
        <h4 class="font-medium text-gray-700 mb-3 text-sm">Permissions</h4>
        
        <!-- Search filter for permissions -->
        <div class="mb-3">
          <input 
            class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-sm" 
            type="text" 
            v-model="permissionSearch" 
            placeholder="Search permissions..."
          >
        </div>
        
        <!-- Reset button when permissions are modified from role defaults -->
        <div v-if="showResetButton" class="mb-3 text-right">
          <button 
            class="px-3 py-1 bg-gray-200 text-gray-700 rounded-md text-xs hover:bg-gray-300 transition-colors"
            @click="resetToOriginalPermissions"
          >
            Reset to Original Permissions
          </button>
        </div>
        
        <div class="permissions-grid">
          <div 
            v-for="(permission, index) in filteredPermissions" 
            :key="index" 
            class="permission-checkbox"
          >
            <input 
              type="checkbox" 
              :id="'permission-' + permission.value" 
              :value="permission.value"
              v-model="selectedPermissions"
              class="mr-2"
            >
            <label :for="'permission-' + permission.value" class="text-xs cursor-pointer">
              {{ permission.label }}
            </label>
          </div>
        </div>
      </div>

      <!-- Buttons -->
      <div class="gap-4 block text-sm text-right mt-6">
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2 hover:bg-neutral-200 transition-colors" 
                     :to="{name: 'system-users'}">
          Cancel
        </router-link>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20 text-white hover:opacity-90 transition-colors" 
                @click="editUser" id="addMember">
          <vue-loaders-ball-beat color="white" scale="1" v-show="loading"></vue-loaders-ball-beat>
          <span v-show="!loading">Save</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';
import {mapActions} from "vuex";

export default {
  components: {
    CustomLoading,
    PageHeader,
  },
  data() {
    return {
      isLoading: false,
      fullPage: false,
      loading: false,
      permissionSearch: "", // New property for permission search
      form: {
        user_id: null,
        msisdn: null,
        user_name: null,
        email_address: null,
        permissions_acl: [],
        user_permissions: [],
        role_id: '',
        timestamp: Date.now(),
      },
      roles: [],
      permissions_list: [],
      selectedPermissions: [], // Array to store selected permission values
      originalPermissions: [], // Store original user permissions
      rolePermissions: {}, // Store permissions for each role
      showResetButton: false, // Show reset button when permissions are modified from role defaults
    }
  },
  computed: {
    // Filter permissions based on search input
    filteredPermissions() {
      if (!this.permissionSearch) {
        return this.permissions_list;
      }
      
      const searchTerm = this.permissionSearch.toLowerCase();
      return this.permissions_list.filter(permission => 
        permission.label.toLowerCase().includes(searchTerm)
      );
    }
  },
  async mounted() {
    await this.setRoles();
    await this.setPermissions();
    await this.loadRolePermissions();
    await this.setForm();
  },
  watch: {
    // Watch for role changes to update permissions
    'form.role_id': function(newRoleId) {
      if (newRoleId && this.rolePermissions[newRoleId]) {
        // Store current permissions to check if they've been modified
        const currentPermissions = [...this.selectedPermissions];
        
        // Update permissions based on the selected role
        this.selectedPermissions = this.rolePermissions[newRoleId].map(perm => parseInt(perm.id));
        
        // Show reset button if this is not the initial form setup
        if (this.originalPermissions.length > 0) {
          this.showResetButton = true;
        }
      }
    }
  },
  methods: {
    ...mapActions(["getSystemPermissions", "getSystemRoles", "updateSystemUser", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },


    async editUser() {
      let app = this;
      
      // Create payload with required fields that should always be sent
      const payload = {
        user_id: app.form.user_id,
        role_id: app.form.role_id,
        timestamp: Date.now()
      };
      
      // Only include display_name if it was changed
      if (app.form.display_name !== app.$store.state.user.display_name) {
        payload.display_name = app.form.display_name;
      }
      
      // Only include user_name/email if it was changed
      if (app.form.user_name !== app.$store.state.user.user_name) {
        payload.user_name = app.form.user_name;
      }
      
      // Only include permissions if they were changed
      const originalPermIds = app.originalPermissions.sort().join(':');
      const currentPermIds = [...app.selectedPermissions].sort().join(':');
      
      if (originalPermIds !== currentPermIds) {
        payload.permissions_acl = app.selectedPermissions.join(':');
      }

      console.log("payload: ", JSON.stringify(payload));

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this updates this user!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (_) => {
          app.isLoading = true;
          return await this.updateSystemUser(payload);
        },
      })
      .then(async (result) => {
        if (result.value.status === 200) {
          app.$swal.fire('Updated!', result.value.message, 'success');
          await this.$router.push({name: 'system-users'});
        } else {
          app.$swal.fire('Error!', result.value.message, 'error');
        }

        app.isLoading = false;
      });
    },


    // Fetch System roles
    async setRoles() {
      let app = this
      app.isLoading = true
      let response = await this.getSystemRoles({limit: 100})
      if (response.status === 200) {
        response.message.result.forEach(function (item) {
          let role = {text: item.role_name, value: item.role_id}
          app.roles.push(role)
        })
      }
      app.isLoading = false
    },

    //
    async setPermissions() {
      let app = this;
      app.isLoading = true
      let payload = {page: 1, per_page: 200};
      let response = await this.getSystemPermissions(payload);
      // console.log("KAKA response :",JSON.stringify(response.message.data))
      if (response.status === 200) {
        for (let i = 0; i < response.message.result.length; i++) {
          let item = response.message.result[i];
          app.permissions_list.push({label: item.permission, value: parseInt(item.permission_id)});
        }
      }
      // console.log("Permissions Len :",JSON.stringify(app.permissions_list))
      app.isLoading = false
    },

    // Load permissions for each role
    async loadRolePermissions() {
      let app = this;
      app.isLoading = true;
      
      let response = await this.getSystemRoles({limit: 100, with_permissions: true});
      if (response.status === 200) {
        response.message.result.forEach(function(role) {
          if (role.permissions) {
            app.rolePermissions[role.role_id] = role.permissions;
          }
        });
      }
      
      app.isLoading = false;
    },
    
    // Reset permissions to original user permissions
    resetToOriginalPermissions() {
      this.selectedPermissions = [...this.originalPermissions];
      this.showResetButton = false;
    },
    
    async setForm() {
      let app = this;
      let user = this.$store.state.user;

      app.form.display_name = user.display_name;
      app.form.user_id = user.user_id;
      app.form.msisdn = user.msisdn;
      app.form.user_name = user.user_name;
      app.form.email_address = user.email_address;
      app.form.status = user.status;
      app.form.role_id = user.role_id;

      // Initialize selectedPermissions with user's existing permissions
      const userPermIds = user.permissions.map(item => parseInt(item.id));
      app.selectedPermissions = [...userPermIds];
      app.originalPermissions = [...userPermIds]; // Store original permissions
    },

  }
}
</script>

<style scoped>
.permissions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin-top: 8px;
  max-height: 300px; /* Add max height to limit the size */
  overflow-y: auto; /* Add scrolling for overflow content */
  padding-right: 8px; /* Add padding for scrollbar */
}

.permission-checkbox {
  display: flex;
  align-items: center;
  padding: 6px;
  border-radius: 4px;
}

.permission-checkbox:hover {
  background-color: #f1f5f9;
}

.filter-card {
  background-color: white;
  transition: all 0.2s ease;
}

.filter-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
</style>
