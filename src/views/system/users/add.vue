
<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
    <page-header pageName="System" pageSubtitle="Add User" />

    <div class="block px-6 py-4 mx-3">
      <!-- Two-column layout for the first two cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <!-- User Information Card -->
        <div class="filter-card p-4 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="font-medium text-gray-700 mb-3 text-sm">User Information</h4>
          <div class="space-y-4">
            <!-- Full Name -->
            <div class="block">
              <label class="block text-xs font-medium text-gray-700 mb-1">User Names</label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-sm" 
                     type="text" v-model="form.display_name" placeholder="<PERSON>">
            </div>
            
            <!-- Phone Number -->
            <div class="block">
              <label class="block text-xs font-medium text-gray-700 mb-1">Phone Number</label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-sm" 
                     type="text" v-model="form.msisdn" placeholder="254712345678" required>
            </div>
          </div>
        </div>
        
        <!-- Contact & Role Card -->
        <div class="filter-card p-4 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="font-medium text-gray-700 mb-3 text-sm">Contact & Role</h4>
          <div class="space-y-4">
            <!-- Email -->
            <div class="block">
              <label class="block text-xs font-medium text-gray-700 mb-1">Email</label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-sm" 
                     type="text" v-model="form.user_name" placeholder="<EMAIL>">
            </div>
            
            <!-- Role Section -->
            <div class="block">
              <label class="block text-xs font-medium text-gray-700 mb-1">Assign Role</label>
              <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-sm" v-model="form.role_id">
                <option value="" disabled selected>Choose one Role</option>
                <option v-for="role in roles" :value="role.value">
                  {{ role.text }}
                </option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <!-- Permissions Section -->
      <div class="filter-card p-4 border rounded-md hover:border-indigo-300 transition-colors mb-4">
        <h4 class="font-medium text-gray-700 mb-3 text-sm">Permissions</h4>
        
        <!-- Search filter for permissions -->
        <div class="mb-3">
          <input 
            class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-sm" 
            type="text" 
            v-model="permissionSearch" 
            placeholder="Search permissions..."
          >
        </div>
        
        <div class="permissions-grid">
          <div 
            v-for="permission in filteredPermissions" 
            :key="permission.value" 
            class="permission-checkbox"
          >
            <input 
              type="checkbox" 
              :id="`permission-${permission.value}`" 
              :value="permission.value" 
              v-model="selectedPermissions"
              class="mr-2"
            >
            <label :for="`permission-${permission.value}`" class="text-xs cursor-pointer">
              {{ permission.label }}
            </label>
          </div>
        </div>
      </div>

      <!-- Buttons -->
      <div class="gap-4 block text-sm text-right mt-6">
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2 hover:bg-neutral-200 transition-colors" 
                     :to="{name: 'system-users'}">
          Cancel
        </router-link>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2 pl-20 pr-20 text-white hover:opacity-90 transition-colors" 
                @click="createUser" id="addSystemUser">
          <vue-loaders-ball-beat color="white" scale="1" v-show="loading"></vue-loaders-ball-beat>
          Submit
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import {mapActions} from "vuex";
import moment from "moment-timezone";
import country from 'country-list-js';
import VueDatePicker from "@vuepic/vue-datepicker";
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';
import vSelect from 'vue-select';
import 'vue-select/dist/vue-select.css';

export default {
  components: {
    CustomLoading,
    PageHeader,
    vSelect,
  },
  data() {
    return {
      isLoading: false,
      fullPage: false,
      loading: false,
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",
      permissionSearch: "", // New property for permission search
      //
      form: {
        timestamp: Date.now(),
        user_name: '',
        display_name: '',
        role_id: '',
        permissions_acl: [],
      },
      //
      roles: [],
      permissions: [],
      selectedPermissions: [], // Array to store selected permission values
    }
  },
  computed: {
    // Filter permissions based on search input
    filteredPermissions() {
      if (!this.permissionSearch) {
        return this.permissions;
      }
      
      const searchTerm = this.permissionSearch.toLowerCase();
      return this.permissions.filter(permission => 
        permission.label.toLowerCase().includes(searchTerm)
      );
    }
  },
  async mounted() {
     this.setRoles()
     this.setPermissions()
  },
  methods: {
    ...mapActions(["getSystemRoles", "getSystemPermissions", "addSystemUser", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    // Fetch System roles
    async setRoles() {
      let app = this
      app.isLoading = true
      let response = await this.getSystemRoles({limit: 100})
      if (response.status === 200) {
        response.message.result.forEach(function (item) {
          let role = {text: item.role_name, value: item.role_id}
          app.roles.push(role)
        })
      }
      app.isLoading = false
    },

    // Fetch System permissions
    async setPermissions() {
      let app = this;
      app.isLoading = true
      let payload = {page: 1, per_page: 200};
      let response = await this.getSystemPermissions(payload);
      if (response.status === 200) {
        for (let i = 0; i < response.message.result.length; i++) {
          let item = response.message.result[i];
          app.permissions.push({label: item.permission, value: parseInt(item.permission_id)});
        }
      }
      app.isLoading = false
    },

    async createUser() {
      let app = this
      
      // Join the selected permission IDs with ":"
      app.form.permissions_acl = app.selectedPermissions.join(":");
      delete app.form.user_permissions
      
      const payload = this.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this adds a new user!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, Add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (_) => {
          app.isLoading = true
          return await this.addSystemUser(payload)
        },
      })
      .then(async (result) => {
        if (result.value.status === 200) {
          app.$swal.fire('Added!', result.value.message, 'success')
          await this.$router.push({name: 'system-users'})
        } else {
          app.$swal.fire('Error!', result.value.message, 'error')
        }
        app.isLoading = false
      })
    },

  }
}
</script>

<style scoped>
/* Add filter card styles */
.filter-card {
  background-color: white;
  transition: all 0.2s ease;
}

.filter-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-2px);
}

/* Keep existing permissions grid styles */
.permissions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin-top: 8px;
  max-height: 300px;
  overflow-y: auto;
  padding-right: 8px;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  padding: 12px;
}

.permission-checkbox {
  display: flex;
  align-items: center;
  padding: 6px;
  border-radius: 4px;
}

.permission-checkbox:hover {
  background-color: #f1f5f9;
}

/* Custom checkbox styling */
.permission-checkbox input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border: 1px solid #cbd5e1;
  border-radius: 3px;
  outline: none;
  cursor: pointer;
  position: relative;
  background-color: white;
}

.permission-checkbox input[type="checkbox"]:checked {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.permission-checkbox input[type="checkbox"]:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 5px;
  height: 9px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.permission-checkbox label {
  cursor: pointer;
}

/* Add a scrollbar styling for the permissions grid */
.permissions-grid::-webkit-scrollbar {
  width: 6px;
}

.permissions-grid::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.permissions-grid::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.permissions-grid::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Button hover effects */
button, a {
  transition: all 0.2s ease;
}

button:hover, a:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
</style>
