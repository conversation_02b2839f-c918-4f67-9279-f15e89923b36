<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
    <page-header pageName="System" pageSubtitle="Permissions" />

    <div class="p-4">
        <auto-table
          :items="items"
          :exclude-columns="excludedColumns"
          :has-actions="true"
          :loading="isLoading"
          :total-items="total"
          :items-per-page="limit"
          :current-page-prop="offset"
          :server-side-pagination="true"
          :pagination="total > limit"
          :show-items-count="true"
          :column-styles="columnStyles"
          :header-styles="headerStyles"
          :get-actions="getRowActions"
          :items-per-page-options="[10, 25, 50, 100, 200]"
          @page-change="gotToPage"
          @items-per-page-change="updateItemsPerPage"
        >
      <!-- ID Column -->
      <template #permission_id="{ item }">
        <strong>{{ item.permission_id }}</strong>
      </template>

      <!-- Permission Name Column -->
      <template #permission="{ item }">
        <div>{{ item.permission }}</div>
      </template>

      <!-- Description Column -->
      <template #permission_desc="{ item }">
        <div>{{ item.permission_desc }}</div>
      </template>

      <!-- Status Column -->
      <template #status="{ item }">
        <button class="inline-block px-3 py-1 rounded-md text-white" :class="getStatusBg(item.status)">
          {{ parseInt(item.status) === 1 ? 'Active' : parseInt(item.status) === 3 ? 'Deactivated' : 'Inactive' }}
        </button>
      </template>

      <!-- Date Column -->
      <template #created_at="{ item }">
        <div>{{ moment(item.created_at).format('lll') }}</div>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item }">
        <ActionDropdown>
          <ActionItem @click="editPermission(item.permission_id)">Edit</ActionItem>
          <ActionItem @click="deletePermission(item.permission_id)">Delete</ActionItem>
        </ActionDropdown>
      </template>
    </auto-table> 
    </div>
  </div>
</template>

<script>
import moment from "moment";
import { mapActions } from "vuex";
import AutoTable from '@/components/common/AutoTable.vue';
import ActionDropdown from '@/components/common/ActionDropdown.vue';
import ActionItem from '@/components/common/ActionItem.vue';
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
  },
  data() {
    return {
      isLoading: false,
      items: [],
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      moment: moment,

      // Columns to exclude from the table display
      // Add any internal fields or sensitive data you don't want to show
      excludedColumns: [
        'created_by',           // Hide last updated timestamp
        'trx_count',          // Hide internal system ID
      ],
      tableHeaders: [
        { label: 'ID', key: 'permission_id', sortable: true, align: 'center' },
        { label: 'Permission Name', key: 'permission', sortable: true, align: 'center' },
        { label: 'Permission Description', key: 'permission_desc', sortable: false, align: 'center' },
        { label: 'Status', key: 'status', sortable: false, align: 'center', },
        { label: 'Created', key: 'created_at', sortable: true, align: 'center' },
      ],

      // Custom CSS styles for specific columns (body cells)
      columnStyles: {
        permission_id: 'text-center font-bold text-blue-600',
        permission: 'text-left font-semibold text-gray-800',
        permission_desc: 'text-left text-gray-600',
        status: 'text-center',
        created_at: 'text-center text-gray-600 text-sm'
      },

      // Custom CSS styles for column headers
      headerStyles: {
        permission_id: 'text-center font-bold text-blue-700 bg-blue-50',
        permission: 'text-left font-bold text-gray-700 bg-gray-50',
        permission_desc: 'text-left font-bold text-gray-700 bg-gray-50',
        status: 'text-center font-bold text-green-700 bg-green-50',
        created_at: 'text-center font-bold text-gray-700 bg-gray-50'
      }
    }
  },

  mounted() {
    this.getPermissions()
  },
  methods: {
    ...mapActions(["getSystemPermissions", "updateSystemPermissions", "toggleSideMenu"]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    gotToPage(page) {
      let vm = this
      vm.offset = page
      vm.getPermissions()
    },

    async editPermission(item) {
      // Store the permission data in Vuex or localStorage
      this.$store.commit('setPermissionData', item)
      // Navigate to edit page
      this.$router.push({ name: 'permissions-edit' })
    },

    async toggleStatus(item) {
      let vm = this
      vm.isLoading = true
      
      const newStatus = parseInt(item.status) === 1 ? 3 : 1
      const payload = {
        permission_id: item.permission_id,
        status: newStatus
      }
      
      const response = await this.updateSystemPermissions(payload)
      
      if (response.status === 200) {
        // Refresh the list
        await vm.getPermissions()
      } else {
        // Show error message
        this.$swal.fire('Error!', response.message || 'Failed to update status', 'error')
      }
      
      vm.isLoading = false
    },

    async getPermissions() {
      let vm = this
      vm.isLoading = true;
      let payload = {page: vm.offset, per_page: vm.limit}

      let response = await this.getSystemPermissions(payload)
      if (response.status === 200) {
        let data = response.message
        vm.items = data.result || []
        vm.total = parseInt(data.record_count || 0) // Convert to number
      } else {
        vm.items = [];
        vm.total = 0;
      }
      vm.isLoading = false;
    },
    
    getStatusBg(id) {
      id = parseInt(id)
      switch (id) {
        case 1:
          return 'bg-green-600'
        case 3:
          return 'bg-red-600'
        default:
          return 'bg-purple-600'
      }
    },

    // Define actions for each row using getActions (3-dot dropdown menu)
    getRowActions(item) {
      return [
        {
          label: 'Edit Permission',
          action: () => this.editPermission(item),
          icon: 'fas fa-edit'
        },
        {
          label: parseInt(item.status) === 1 ? 'Deactivate' : 'Activate',
          action: () => this.toggleStatus(item),
          icon: parseInt(item.status) === 1 ? 'fas fa-ban' : 'fas fa-check-circle'
        }
      ];
    },

    // Handle items per page change
    updateItemsPerPage(newLimit) {
      this.limit = newLimit
      this.offset = 1 // Reset to first page
      this.getPermissions()
    },
  },
}
</script>
