<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
    <page-header pageName="System" pageSubtitle="Edit Role" />

    <div class="block px-6 py-4 mx-3">
      <!-- Role Information Card -->
      <div class="filter-card p-4 border rounded-md hover:border-indigo-300 transition-colors mb-4">
        <h4 class="font-medium text-gray-700 mb-3 text-sm">Role Information</h4>
        <div class="grid grid-cols-2 gap-4">
          <div class="block mb-4">
            <label class="text-xs mb-1 block font-medium">Role Name</label>
            <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.role_name">
          </div>
          <div class="block mb-4">
            <label class="text-xs mb-1 block font-medium">Role Description</label>
            <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.description">
          </div>
        </div>
      </div>
      
      <!-- Permissions Card -->
      <div class="filter-card p-4 border rounded-md hover:border-indigo-300 transition-colors mb-4">
        <h4 class="font-medium text-gray-700 mb-3 text-sm">Permissions</h4>
        
        <!-- Search filter for permissions -->
        <div class="mb-3">
          <input 
            class="w-full block px-4 py-2 border bg-white rounded-md outline-none text-sm" 
            type="text" 
            v-model="permissionSearch" 
            placeholder="Search permissions..."
          >
        </div>
        
        <div class="permissions-grid grid grid-cols-4 gap-3">
          <div v-for="(permission, index) in filteredPermissions" :key="index" class="permission-checkbox">
            <input 
              type="checkbox" 
              :id="'permission-' + permission.value" 
              :value="permission.value"
              v-model="selectedPermissions"
              class="mr-2"
            >
            <label :for="'permission-' + permission.value" class="text-sm">
              ({{ permission.value }}) {{ permission.label }}
            </label>
          </div>
        </div>
      </div>
      
      <div class="flex justify-end items-center space-x-3 mt-6">
        <router-link 
          class="px-12 py-1.5 text-sm bg-neutral-100 hover:bg-neutral-200 border rounded-md transition-colors" 
          :to="{name: 'roles'}" >
          Cancel
        </router-link>
        <button 
          class="px-36 py-2 text-sm bg-indigo-600 hover:bg-primary-dark text-white font-medium rounded-md shadow-sm transition-colors" 
          @click="editRole()"
          id="addMember" >
          <vue-loaders-ball-beat color="white" scale="0.5" v-show="loading"></vue-loaders-ball-beat>
          <span v-show="!loading">Save</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import {mapActions} from "vuex";
import {CustomLoading} from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    CustomLoading,
    PageHeader,
  },
  data() {
    return {
      isLoading: false,
      fullPage: false,
      loading: false,
      permissionSearch: "", // New property for permission search
      form: {
        role_id: null,
        role_name: null,
        description: null,
        permission_acl: [],
        permissions_list: [],
        timestamp: Date.now(),
        status: 1,
      },
      items: [],
      statuses:[
        {label: "Active", value: 1},
        {label: "DeActive", value: 3},
        {label: "Suspend", value: 5},
      ],
      selectedPermissions: [], // Array to store selected permission values
    }
  },
  computed: {
    // Filter permissions based on search input
    filteredPermissions() {
      if (!this.permissionSearch) {
        return this.items;
      }
      
      const searchTerm = this.permissionSearch.toLowerCase();
      return this.items.filter(permission => 
        permission.label.toLowerCase().includes(searchTerm)
      );
    }
  },
  async mounted() {
    this.isLoading = true
    await this.setPermissions()
    await this.setForm()
    this.isLoading = false
  },
  methods: {
    ...mapActions(["getSystemPermissions", "updateSystemRole", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    async editRole() {
      let app = this
      app.loading = true
      
      // Use selectedPermissions instead of permissions_list
      app.form.permission_acl = app.selectedPermissions.join(":");
      app.form.timestamp = Date.now()

      if (app.form.role_name === "") {
        app.$swal.fire({
          icon: 'error',
          title: 'Error',
          text: `Kindly add Role Name.`
        });
        return
      }

      delete app.form.permissions_list

      const payload = app.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this adds updates this role!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          $('#addMember').html(' Please Wait ...');
          return await this.updateSystemRole(payload)

        },
      })
          .then(async (result) => {
            $('#addMember').html('Save');
            app.loading = false
            if (result.value.status === 200) {
              app.$swal.fire({
                title: 'Updated!',
                text: result.value.message,
                icon: 'success'
              }).then(async (result) => {
                await app.$router.push({name: 'roles'})
              })

            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },


    async setPermissions() {
      let app = this;
      app.isLoading = true
      let payload = {page: 1, per_page: 200};
      let response = await this.getSystemPermissions(payload);
      
      if (response.status === 200) {
        for (let i = 0; i < response.message.result.length; i++) {
          let item = response.message.result[i];
          app.items.push({label: item.permission, value: parseInt(item.permission_id)});
        }
      }
      app.isLoading = false
    },

    async setForm() {
      let app = this;
      let role = this.$store.state.roleData;
      app.form.role_id = role.role_id
      app.form.role_name = role.role_name
      app.form.description = role.description ?? ""
      
      // Preselect permissions based on role data
      if (role.permissions && role.permissions.length > 0) {
        // Convert permission IDs to integers for proper comparison
        app.selectedPermissions = role.permissions.map(item => parseInt(item.id));
      } else if (role.permission_acl) {
        // Alternative: handle permission_acl if it exists
        const permissionIds = role.permission_acl.split(':').map(id => parseInt(id));
        app.selectedPermissions = permissionIds.filter(id => id); // Filter out any falsy values
      }
      
      console.log("Selected permissions:", app.selectedPermissions);
    },

  }
}
</script>

<style scoped>
.permissions-grid {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
}

.permission-checkbox {
  display: flex;
  align-items: center;
  padding: 4px 0;
}

.filter-card {
  background-color: white;
  transition: all 0.2s ease;
}

.filter-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

@media (max-width: 1024px) {
  .permissions-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .permissions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .permissions-grid {
    grid-template-columns: repeat(1, 1fr);
  }
}
</style>
