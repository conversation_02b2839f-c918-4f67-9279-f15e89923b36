<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
    <page-header pageName="System" pageSubtitle="Roles" />

    <div class="w-full flex mb-4 px-4 text-xs gap-2 justify-end items-end">
      <div class="flex-grow"></div>
      <router-link class="inline-block px-4 py-2 rounded-md bg-primary font-bold" :to="{ name: 'roles-add' }">
        Add Role
      </router-link>
    </div>

    <div class="p-3">
      <auto-table
        :headers="tableHeaders"
        :data="items"
        :has-actions="true"
        :total-items="total"
        :items-per-page="limit"
        :current-page-prop="offset"
        :server-side-pagination="true"
        :pagination="total > limit"
        :show-items-count="true"
        @page-change="gotToPage"
      >
        <!-- Index Column -->
        <template #index="{ index }">
          <div class="text-center">{{ index + 1 + ((offset - 1) * limit) }}</div>
        </template>

        <!-- Role Name Column -->
        <template #role_name="{ item }">
          <div>{{ item.role_name }}</div>
        </template>

        <!-- Status Column -->
        <template #status="{ item }">
          <button class="inline-block px-4 py-2 rounded-md text-white" :class="getStatusBg(item.status)">
            {{ parseInt(item.status) === 1 ? 'Active' : parseInt(item.status) === 3 ? 'Deactivated' : 'Inactive' }}
          </button>
        </template>

        <!-- Date Column -->
        <template #created_at="{ item }">
          <div>{{ moment(item.created_at).format('lll') }}</div>
        </template>

        <!-- Actions Column -->
        <template #actions="{ item }">
          <action-dropdown>
            <action-item @click="editRole(item)">
              <i class="fas fa-edit mr-2 text-orange-600"></i> Edit Role
            </action-item>
          </action-dropdown>
        </template>
      </auto-table>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import { mapActions } from "vuex";
import ActionDropdown from '@/components/common/ActionDropdown.vue';
import ActionItem from '@/components/common/ActionItem.vue';
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';
import AutoTable from '@/components/common/AutoTable.vue';

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
    moment,
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      fullPage: true,
      isOpen: false,
      items: [],
      total: 0,
      limit: 10,
      offset: 1,
      permissions: [],
      tableHeaders: [
        { label: '#', key: 'index', sortable: false, align: 'center' },
        { label: 'Role Name', key: 'role_name', sortable: true, align: 'left' },
        { label: 'Status', key: 'status', sortable: false, align: 'center' },
        { label: 'Date Created', key: 'created_at', sortable: true, align: 'center' },
      ]
    }
  },

  mounted() {
    this.getItems()
  },
  methods: {
    ...mapActions(["getSystemRoles", "fillData", "fillRole", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    gotToPage(page) {
      let vm = this
      vm.offset = page
      vm.getItems()
    },

    async editRole(userData) {
      let app = this
      await this.fillRole(userData)
      app.$router.push({name: 'roles-edit'})
    },

    async getItems() {
      let vm = this
      vm.isLoading = true;
      let payload = {page: vm.offset, per_page: vm.limit}

      let response = await this.getSystemRoles(payload)

      if (response.status === 200) {
        vm.items = response.message.result
        vm.total = response.message.record_count
      }
      vm.isLoading = false;
    },

    getStatusBg(id) {
      id = parseInt(id)
      switch (id) {
        case 1:
          return 'bg-green-600'
        case 2:
          return 'bg-orange-600'
        case 3:
          return 'bg-red-600'
        default:
          return 'bg-purple-600'
      }
    },
  },
}
</script>
