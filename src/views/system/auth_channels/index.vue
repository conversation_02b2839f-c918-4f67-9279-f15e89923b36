<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>

    <div class="flex items-center p-6 pb-1 border-b gap-4 mb-5">
      <!-- Left Section: <PERSON>u <PERSON>ggle and <PERSON> Button -->
      <div class="flex items-center justify-center w-10 h-10 bg-gray-200 rounded-full cursor-pointer"
           @click="toggleSideM">
        <i v-if="!this.$store.state.isSideMenuOpen"
           class="fa fa-bars text-black text-lg"></i>
        <i v-else class="fa fa-close text-black text-lg"></i>
      </div>
      <button
          class="px-4 py-2 text-sm font-medium text-white bg-neutral-300 rounded-full hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 w-16 h-10"
          @click="goBack">
        Back
      </button>
      <!-- Middle Section: Name -->
      <div class="flex text-center font-extrabold ml-20" style="font-size: 30px">Auth Channels</div>
    </div>

    <div class="w-full flex mb-4 px-4 text-xs gap-2 justify-end items-end">
      <div class="flex-grow">
      </div>
      <router-link class="inline-block px-4 py-2 rounded-md bg-primary font-bold" :to="{ name: 'auth-channels-add' }">
        Add Channel
      </router-link>
    </div>
    <div v-show="authChannels.length>0" class="py-4 rounded-lg shadow-lg mx-3 border overflow-x-auto">
      <table class="w-full mb-12 table">
        <thead class="border-b-2 border-gray-400 text-xs text-left">
        <tr class="table-row">
          <th class="py-2 pl-5">#</th>
          <th class="py-2 ">Channel Name</th>
          <th class="py-2 text-center">Status</th>
          <th class="py-2">Date Created</th>
          <th class="py-2 text-center">Action</th>
          <th></th>
        </tr>
        </thead>
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(item,index) in authChannels" :key="item.channel_id">
          <td class="py-2 pl-5 w-1 pr-8 border-b">{{ index + 1 }}</td>
          <td class="py-2 font-medium border-b">{{ item.channel_name }}</td>
          <td class="text-center  py-2 border-b">
            <button class="inline-block px-4 py-2 rounded-md text-white" :class="getStatusBg(item.status)">
              {{ parseInt(item.status) === 1 ? 'Active' : parseInt(item.status) === 3 ? 'Deactivated' : 'Inactive' }}
            </button>
          </td>
          <td class="py-2 border-b">{{ moment(item.created_at).format('lll') }}</td>
          <td class="py-2 text-center relative w-24 border-b">
            <button class="px-4 py-2 border rounded-md bg-orange-400 text-white font-bold" @click="edit(item)">Edit</button>
          </td>
        </tr>
        </tbody>
      </table>

    </div>

  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import {mapActions} from "vuex";

export default {
  data() {
    return {
      isOpen: false,
      isLoading: false,
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      moment: moment,
      authChannels: [],
      //
      requestPayload: {
        status: '',
        channel_name: '',
        timestamp: '',
      },
    }
  },
  components: {
    Loading,
    moment,
  },

  mounted() {

    this.setAuthChannels()
  },
  methods: {
    ...mapActions(["getAuthChannels", "fillAuthChannel", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    //
    gotToPage(page) {
      let vm = this
      vm.offset = page
      vm.setAuthChannels()
    },

    //
    async edit(item) {
      let app = this
      await this.fillAuthChannel(item)
      app.$router.push({name: 'auth-channels-edit'})
    },

    //
    async setAuthChannels() {
      let vm = this
      vm.isLoading = true;
      vm.authChannels = []
      vm.requestPayload.timestamp = Date.now()

      let response = await this.getAuthChannels(vm.requestPayload)
      // console.log("authChannels", JSON.stringify(response.message))
      if (response.status === 200) {
        vm.authChannels = response.message.result
        vm.total = response.message.record_count
      }

      vm.isLoading = false;
    },

    //
    getStatusBg(id) {
      id = parseInt(id)
      switch (id) {
        case 1:
          return 'bg-green-600'
        case 2:
          return 'bg-orange-600'
        case 3:
          return 'bg-red-600'
        default:
          return 'bg-purple-600'
      }
    },

  },
}
</script>
