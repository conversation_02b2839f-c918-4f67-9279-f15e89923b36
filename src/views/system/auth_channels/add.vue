<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>

    <div class="flex items-center p-6 pb-1 border-b gap-4 mb-5">
      <!-- Left Section: <PERSON>u <PERSON>ggle and <PERSON> Button -->
      <div class="flex items-center justify-center w-10 h-10 bg-gray-200 rounded-full cursor-pointer"
           @click="toggleSideM">
        <i v-if="!this.$store.state.isSideMenuOpen"
           class="fa fa-bars text-black text-lg"></i>
        <i v-else class="fa fa-close text-black text-lg"></i>
      </div>
      <button
          class="px-4 py-2 text-sm font-medium text-white bg-neutral-300 rounded-full hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 w-16 h-10"
          @click="goBack">
        Back
      </button>
      <!-- Middle Section: Name -->
      <div class="flex text-center font-extrabold ml-20" style="font-size: 30px">Add Channels</div>
    </div>

    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 border ">
      <div class="block mb-4">
        <label class="text-xs mb-1 block font-medium">Role Name</label>
        <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.name">
      </div>
      <div class="block mb-4">
        <label class="text-xs mb-1 block font-medium">Role Description</label>
        <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.description">
      </div>
      <div class="block mb-4">
        <label class="text-xs mb-1 block font-medium">Permissions</label>
        <v-select
            class=""
            v-model="form.acl_list"
            :options="items"
            multiple
            placeholder="Select Permission"
        />
      </div>
      <div class="gap-4 block text-sm text-right">
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" :to="{name: 'auth-channels'}">
          Cancel
        </router-link>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2" @click="add()" id="addMember">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat>
          Save
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";

export default {
  data() {
    return {
      isLoading: false,
      fullPage: true,

      //
      form: {
        name: null,
        description: null,
        acl_list: [],
      },
      items: [],
      loading: false
    }
  },
  components: {
    Loading
  },
  async mounted() {
    await this.setPermissions()
  },
  methods: {
    ...mapActions(["addSystemRole", "getSystemPermissions", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    //
    async setPermissions() {
      let app = this
      let payload = {page: 1, per_page: 100}

      let response = await this.getSystemPermissions(payload)
      if (response.status === 200) {
        response.message.data.forEach(function (item) {
          let list = {label: item.name, value: parseInt(item.id)}
          app.items.push(list)
        })
      }
    },

    //
    checkFormValidity(payload) {
      for (const key in payload) {
        if (payload[key] === null || payload[key] === '') {
          return false;
        }
      }
      return true;
    },

    //
    async add() {
      let app = this
      app.loading = true
      $('#addMember').html(' Please Wait ...');

      let acl = []
      this.form.acl_list.forEach(function (item) {
        acl.push(item.value)
      })
      console.log("acl_list", acl)

      this.form.acl_list = acl

      if (!this.checkFormValidity(app.form)) {
        // Handle the case where there are invalid elements
        console.log("There are null or empty elements in the form.");
        app.$swal.fire({
          icon: 'error',
          title: 'Error',
          text: `Fill in all data.`
        });
        return
      }

      const payload = app.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this adds a new role!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.addSystemRole(payload)

        },
      })
          .then(async (result) => {
            $('#addMember').html('Add Role');
            app.loading = false
            if (result.value.status === 200) {
              app.$swal.fire({
                title: 'Added!',
                text: result.value.message,
                icon: 'success'
              }).then(async (result) => {
                await app.$router.push({name: 'roles'})
              })

            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

  }
}
</script>
