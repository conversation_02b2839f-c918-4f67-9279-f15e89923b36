<template>
  <div class="relative mx-auto max-w-4xl">
    <!-- Background with gradient overlay -->
    <div class="fixed inset-0 bg-cover bg-center z-0" 
         style="background-image: url('@/assets/login-bg.jpg');">
      <div class="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-emerald-900/80 backdrop-blur-sm"></div>
    </div>

    <div class="relative z-10 w-full sm:w-[420px] mx-auto mt-10 sm:mt-20 rounded-xl h-auto p-6 sm:p-8 shadow-2xl"
         style="background-color: rgba(16, 22, 36, 0.85); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1)">
      <div class="text-sm text-center text-white my-2 italic rounded-lg shadow-lg bg-orange-400">
        <alert v-show="type && message" :type="type" :message="message"></alert>
      </div>

      <div class="text-xl px-4 sm:px-8 pt-4 sm:pt-6 flex justify-center items-center gap-2">
        <img src="@/assets/mossbets-logo.png" alt="logo" class="sm:w-auto sm:h-auto"/>
        <small><sup class="text-amber-600">BO</sup></small>
      </div>

      <div class="text-xl text-center text-white mt-6 sm:my-6 font-medium">Welcome Back</div>

      <form @submit.prevent="submit" class="space-y-4">
        <div class="block" v-show="mc !== 1 && !showVerification">
          <label class="text-sm text-gray-300 font-medium mb-1 block">Username</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
              </svg>
            </div>
            <input class="pl-10 px-4 py-3 border border-gray-700 bg-gray-800/50 text-white rounded-lg w-full block outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                   v-model="form.username" placeholder="Enter your username">
          </div>
        </div>

        <div class="block" v-show="mc !== 1 && !showVerification">
          <label class="text-sm text-gray-300 font-medium mb-1 block">Password</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
              </svg>
            </div>
            <input type="password" class="pl-10 px-4 py-3 border border-gray-700 bg-gray-800/50 text-white rounded-lg w-full block outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                   v-model="form.password" placeholder="Enter your password">
          </div>
        </div>
        


        <div class="block" v-show="showPin && timerCount > 0 && mc !== 1">
          <label class="text-sm text-gray-300 font-medium mb-1 block">Enter OTP</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
              </svg>
            </div>
            <input type="password" class="pl-10 px-4 py-3 border border-gray-700 bg-gray-800/50 text-white rounded-lg w-full block outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                   v-model="form.verification_code" placeholder="Enter OTP code">
          </div>
          <label class="text-sm text-green-400 mt-1 block">OTP has been sent to registered email / phone</label>
        </div>

        <div class="block" v-show="mc === 1">
          <label class="text-sm text-gray-300 font-medium mb-1 block">Select Client/Brand</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
              </svg>
            </div>
            <select class="pl-10 px-4 py-3 border border-gray-700 bg-gray-800/50 text-white rounded-lg w-full block outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all appearance-none" 
                    v-model="client_id">
              <option selected>Select Client</option>
              <option v-for="item in clients" :value="item.id">{{ item.name }}</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
        </div>

        <div class="flex gap-2 my-2" v-show="showPin && timerCount > 0 && mc !== 1">
          <label class="w-1/2 text-left text-sm text-gray-300">00:{{ timerCount }}</label>
          <label class="w-1/2 text-right text-sm text-green-400 hover:text-green-300 cursor-pointer">
            <a href="#" @click="submit()">Resend OTP</a>
          </label>
        </div>



        <button type="button"
                class="block text-center px-4 py-3 w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg font-bold shadow-lg hover:shadow-green-500/30 transition-all duration-200 transform hover:-translate-y-1"
                v-show="showPin && timerCount > 0" @click="submitCode()">
          <vue-loaders-ball-beat color="white" scale="0.7" v-show="loading"></vue-loaders-ball-beat>
          <span id="verifyBTN" v-show="!loading" class="flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
            Verify & Login
          </span>
        </button>

        <button type="submit"
                class="block text-center px-4 py-3 w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg font-bold shadow-lg hover:shadow-green-500/30 transition-all duration-200 transform hover:-translate-y-1"
                v-show="(!showPin || timerCount <= 0) && mc !== 1">
          <vue-loaders-ball-beat color="white" scale="0.7" v-show="loading"></vue-loaders-ball-beat>
          <span id="signin" v-show="!loading" class="flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
            Sign In
          </span>
        </button>

        <div class="text-center pt-2">
          <router-link :to="{name: 'forgot-password'}" class="text-sm text-gray-300 hover:text-green-400 transition-colors">
            Forgot password?
          </router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import {mapActions} from "vuex";
import Alert from "@/components/alert.vue";
import $ from "jquery";
import Loading from "vue-loading-overlay";

export default {
  components: {
    Loading,
    Alert
  },
  data: function () {
    return {
      fullPage: true,
      showPopup: false,

      loading: false,
      type: null,
      message: null,
      phone: "",
      password: "",
      code: "",
      showPin: false,
      pin: "",
      timerCount: 120,
      form: {
        // username: this.$store.state.user_name ?? "<EMAIL>",
        // password: "KABIU@MOSS",
        username: this.$store.state.user_name,
        password: "",
        dial_code: "254"
      },
      client_id: null,
      clients: [],
      mc: null,
      windowSize: null,
    };
  },
  mounted() {
    this.windowSize = window.innerWidth

    if (this.$store.state.message) {
      this.message = this.$store.state.message
      this.type = "success"
    }
  },
  watch: {
    timerCount: {
      handler(value) {

        if (value > 0) {
          setTimeout(() => {
            this.timerCount--;
          }, 1000);
        }

      },
      immediate: true // This ensures the watcher is triggered upon creation
    },
    async client_id() {
      await this.fillClientId(this.client_id)
      await this.$router.push({name: 'dashboard'})
    }
  },
  methods: {
    ...mapActions(["LogIn", "LogInCode", "passwordForgot", "fillUserName",
      "fillMessage", "fillPermissions", "toggleSideMenuInit", "fillClientList"]),
    //
    async submit() {
      let app = this
      await app.fillMessage(null);
      app.loading = true
      if (!app.form.username || !app.form.password) {
        app.message = "All fields are required"
        app.type = "danger"
        app.loading = false
        return
      }

      app.form.password = btoa(app.form.password)
      const payload = app.form
      let response = await this.LogIn(payload);
      
      app.loading = false
      if (response.status === 200) {
        app.type = 'success'
        app.message = response.message.message || 'Login successful. Please verify with OTP.'

        this.$store.state.user_name = app.form.username
        await app.fillMessage(app.message);
        // Redirect to verify-login page
        await app.$router.push({name: 'verify-login', params: {username: app.form.username}})
      } else {
        this.message = response.message.message || response.message || 'Login failed'
        if (response.status === 401 || response.status === 400) {
          app.type = 'danger'
        } else if (response.status === 406) {
          this.page('reset')
          this.type = 'warning'
        } else if (response.status === 205 || response.status === 201) {
          await this.forgotPass()
        } else if (response.status === 410) {
          this.$store.state.user_name = app.form.username
          await app.$router.push("/verify")
        } else {
          this.type = 'danger'
        }
      }
      app.toggleSideMobileInit()
    },

    toggleSideMobileInit() {
      if ( this.windowSize <=768) {
        this.toggleSideMenuInit()
      }
    },

    extractPermissionIds(permissionsData) {
      let permissionIds = []
      for (let i = 0; i < permissionsData.length; i++) {
        permissionIds.push(permissionsData[i].id)
      }
      return permissionIds
    },

    async forgotPass() {
      let app = this
      app.loading = true
      delete app.form.password
      delete app.form.otp_code
      const payload = app.form
      $('#forgotBTN').html(' Please Wait ...');


      let response = await this.passwordForgot(payload);
      console.log("forgot response: ", JSON.stringify(response))
      if (response.status >= 200 && response.status < 300) {
        this.type = 'success'
        this.message = response.message
        await this.fillUserName(app.form.username);
        //
        await app.$router.push({name: "verify"})
        return
      } else {
        this.type = 'danger'
        this.message = response.message
      }
      app.loading = false
    },



    async submitCode() {
      let app = this
      await app.fillMessage(null);
      app.loading = true
      if (!app.form.username || !app.form.password || !app.form.verification_code) {
        app.message = "All fields are required"
        app.type = "danger"
        app.loading = false
        return
      }
      const payload = this.form

      let response = await this.LogInCode(payload);
      if (response.status === 200) {
        if (response.message.data.mc === 1 && response.message.data.clients) {
          app.mc = 1
          app.clients = response.message.data.clients
          await app.fillClientList(app.clients);
        } else {
          await app.$router.push({name: 'dashboard'})
        }
      } else {
        this.message = response.message.message || response.message || 'Verification failed'
        this.type = 'danger'
      }
      app.loading = false
    },

    page(page) {
      this.active = page
    },

    //
  }
}
</script>
