<template>
  <div class="relative mx-auto max-w-4xl">
    <!-- Background with gradient overlay -->
    <div class="fixed inset-0 bg-cover bg-center z-0" 
         style="background-image: url('@/assets/login-bg.jpg');">
      <div class="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-emerald-900/80 backdrop-blur-sm"></div>
    </div>

    <div class="relative z-10 w-full sm:w-[420px] mx-auto mt-10 sm:mt-20 rounded-xl h-auto p-6 sm:p-8 shadow-2xl"
         style="background-color: rgba(16, 22, 36, 0.85); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1)">
      <div class="text-sm text-center text-white my-2 italic rounded-lg shadow-lg bg-orange-400">
        <alert v-show="type && message" :type="type" :message="message"></alert>
      </div>

      <div class="text-xl px-4 sm:px-8 pt-4 sm:pt-6 flex justify-center items-center gap-2">
        <img src="@/assets/mossbets-logo.png" alt="logo" class="sm:w-auto sm:h-auto"/>
        <small><sup class="text-amber-600">BO</sup></small>
      </div>

      <div class="text-xl text-center text-white mt-6 sm:my-6 font-medium">Verify Login</div>
      <div class="text-sm text-center text-gray-300 mb-6">Enter the OTP code sent to your registered email/phone</div>

      <form @submit.prevent="verifyLogin" class="space-y-4">
        <!-- Username Display (Read-only) -->
        <div class="block">
          <label class="text-sm text-gray-300 font-medium mb-1 block">Username</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
              </svg>
            </div>
            <input class="pl-10 px-4 py-3 border border-gray-700 bg-gray-800/30 text-gray-400 rounded-lg w-full block outline-none cursor-not-allowed"
                   :value="username" readonly>
          </div>
        </div>

        <!-- Verification Code Input -->
        <div class="block">
          <label class="text-sm text-gray-300 font-medium mb-1 block">Enter OTP Code</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
              </svg>
            </div>
            <input type="text" class="pl-10 px-4 py-3 border border-gray-700 bg-gray-800/50 text-white rounded-lg w-full block outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                   v-model="verificationCode" placeholder="Enter OTP code" maxlength="6" autofocus>
          </div>
          <label class="text-sm text-green-400 mt-1 block">Please enter the OTP code to complete login</label>
        </div>

        <!-- Client Selection (if multi-client) -->
        <div class="block" v-show="mc === 1">
          <label class="text-sm text-gray-300 font-medium mb-1 block">Select Client/Brand</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
              </svg>
            </div>
            <select class="pl-10 px-4 py-3 border border-gray-700 bg-gray-800/50 text-white rounded-lg w-full block outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all appearance-none" 
                    v-model="client_id">
              <option selected>Select Client</option>
              <option v-for="item in clients" :value="item.id" :key="item.id">{{ item.name }}</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Verify Login Button -->
        <button type="submit"
                class="block text-center px-4 py-3 w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg font-bold shadow-lg hover:shadow-green-500/30 transition-all duration-200 transform hover:-translate-y-1">
          <vue-loaders-ball-beat color="white" scale="0.7" v-show="loading"></vue-loaders-ball-beat>
          <span v-show="!loading" class="flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
            Verify Login
          </span>
        </button>

        <div class="text-center pt-2">
          <router-link :to="{name: 'login'}" class="text-sm text-gray-300 hover:text-green-400 transition-colors">
            ← Back to Login
          </router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import {mapActions} from "vuex";
import Alert from "@/components/alert.vue";

export default {
  components: {
    Alert
  },
  data: function () {
    return {
      loading: false,
      type: null,
      message: null,
      verificationCode: "",
      username: "",
      client_id: null,
      clients: [],
      mc: null,
    };
  },
  mounted() {
    // Get username from store or route params
    this.username = this.$store.state.user_name || this.$route.params.username;
    
    // If no username, redirect back to login
    if (!this.username) {
      this.$router.push({name: 'login'});
      return;
    }

    // Check if there's a success message from login
    if (this.$store.state.message) {
      this.message = this.$store.state.message;
      this.type = "success";
    }
  },
  watch: {
    async client_id() {
      if (this.client_id) {
        await this.fillClientId(this.client_id);
        await this.$router.push({name: 'dashboard'});
      }
    }
  },
  methods: {
    ...mapActions(["LogInCode", "fillMessage", "fillPermissions", "fillClientList", "fillClientId"]),

    async verifyLogin() {
      let app = this;
      await app.fillMessage(null);
      app.loading = true;

      if (!app.verificationCode) {
        app.message = "OTP code is required";
        app.type = "danger";
        app.loading = false;
        return;
      }

      const payload = {
        username: app.username,
        otp_code: app.verificationCode
      };

      let response = await this.LogInCode(payload);
      if (response.status === 200) {
        app.type = 'success';
        app.message = response.message.message || 'Login verified successfully';

        if (response.message.data && response.message.data.mc === 1 && response.message.data.clients) {
          app.mc = 1;
          app.clients = response.message.data.clients;
          await app.fillClientList(app.clients);
        } else {
          if(this.$store.state.role === '1' || this.$store.state.role === '2'){
            await this.fillPermissions(this.$store.state.permissions);
            await app.$router.push({name: 'dashboard'});
          } else {
            await app.$router.push({name: 'customer-search'});
          }
        }
      } else {
        this.message = response.message.message || response.message || 'Verification failed';
        this.type = 'danger';
      }
      app.loading = false;
    }
  }
}
</script>
