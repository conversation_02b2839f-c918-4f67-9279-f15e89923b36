<template>
  <div class="relative mx-auto max-w-4xl">
    <!-- Background with gradient overlay -->
    <div class="fixed inset-0 bg-cover bg-center z-0" 
         style="background-image: url('@/assets/login-bg.jpg');">
      <div class="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-emerald-900/80 backdrop-blur-sm"></div>
    </div>

    <div class="relative z-10 w-full sm:w-[420px] mx-auto mt-10 sm:mt-20 rounded-xl h-auto p-6 sm:p-8 shadow-2xl"
         style="background-color: rgba(16, 22, 36, 0.85); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1)">
      <div class="text-sm text-center text-white my-2 italic rounded-lg shadow-lg bg-orange-400">
        <alert v-show="type && message" :type="type" :message="message"></alert>
      </div>

      <div class="text-xl px-4 sm:px-8 pt-4 sm:pt-6 flex justify-center items-center gap-2">
        <img src="@/assets/mossbets-logo.png" alt="logo" class="sm:w-auto sm:h-auto"/>
        <small><sup class="text-amber-600">BO</sup></small>
      </div>

      <form @submit.prevent="forgotPass" class="mt-4 sm:mt-8 space-y-4">
        <div class="text-xl text-center text-white mt-4 sm:my-4 font-medium">Forgot Password?</div>
        
        <div class="block">
          <label class="text-sm text-gray-300 font-medium mb-1 block">Username</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
              </svg>
            </div>
            <input class="pl-10 px-4 py-3 border border-gray-700 bg-gray-800/50 text-white rounded-lg w-full block outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all" 
                   v-model="form_forgot.username" placeholder="Enter your username">
          </div>
        </div>

        <!-- Add timer and resend button when OTP is sent -->
        <div class="flex gap-2 my-2" v-show="otpSent && timerCount > 0">
          <label class="w-1/2 text-left text-sm text-gray-300">00:{{ timerCount }}</label>
          <label class="w-1/2 text-right text-sm text-gray-300 hover:text-green-400 cursor-pointer transition-colors">
            <a href="#" @click="forgotPass">Resend OTP</a>
          </label>
        </div>

        <button type="submit" 
                class="block text-center px-4 py-3 w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg font-bold shadow-lg hover:shadow-green-500/30 transition-all duration-200 transform hover:-translate-y-1">
          <vue-loaders-ball-beat color="white" scale="0.7" v-show="loading"></vue-loaders-ball-beat>
          <span id="forgotBTN" v-show="!loading" class="flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
            Next
          </span>
        </button>

        <div class="text-center pt-2">
          <router-link :to="{name: 'login'}" class="text-sm text-gray-300 hover:text-green-400 transition-colors">
            Back to Login
          </router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import Swal from 'sweetalert2/dist/sweetalert2.js'
import store from "../../store";
import $ from 'jquery'
import {mapActions} from "vuex";
import Alert from "@/components/alert.vue";
export default {
  components: {Alert},
  data: function () {
    return {
      loading: false,
      type: null,
      message: null,
      phone: "",
      password: "",
      otpSent: false,
      timerCount: 120,
      form_forgot: {
        username: this.$store.state.user_name,
        dial_code: "254"
      },
    };
  },
  watch: {
    timerCount: {
      handler(value) {
        if (value > 0) {
          setTimeout(() => {
            this.timerCount--;
          }, 1000);
        }
      },
      immediate: false
    }
  },
  methods: {
    ...mapActions(["passwordReset", "passwordForgot", "fillUserName"]),

    page(page) {
      this.active = page
    },

    async forgotPass() {
      let app = this
      app.loading = true
      const payload = this.form_forgot
      $('#forgotBTN').html(' Please Wait ...');

      let response = await this.passwordForgot(payload);

      if (response.status === 200) {
        this.type = 'success'
        this.message = response.message
        await this.$store.dispatch('fillUserName',app.form_forgot.username);
        
        // Set OTP sent flag and start timer
        app.otpSent = true
        app.timerCount = 120
        
        app.$router.push({name: 'verify'});
        return
      }
      else {
        this.type = 'danger'
        this.message = response.message
      }
      app.loading = false
      $('#forgotBTN').html('Next');
    },
  }
}
</script>
