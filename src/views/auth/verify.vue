<template>
  <div class="relative mx-auto max-w-4xl">
    <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>

    <!-- Background with gradient overlay -->
    <div class="fixed inset-0 bg-cover bg-center z-0" 
         style="background-image: url('@/assets/login-bg.jpg');">
      <div class="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-emerald-900/80 backdrop-blur-sm"></div>
    </div>

    <div class="relative z-10 w-full sm:w-[420px] mx-auto mt-10 sm:mt-20 rounded-xl h-auto p-6 sm:p-8 shadow-2xl"
         style="background-color: rgba(16, 22, 36, 0.85); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.1)">
      <div class="text-sm text-center text-white my-2 italic rounded-lg shadow-lg bg-orange-400">
        <alert v-show="type && message" :type="type" :message="message"></alert>
      </div>

      <div class="text-xl px-4 sm:px-8 pt-4 sm:pt-6 flex justify-center items-center gap-2">
        <img src="@/assets/mossbets-logo.png" alt="logo" class="sm:w-auto sm:h-auto"/>
        <small><sup class="text-amber-600">BO</sup></small>
      </div>

      <form @submit.prevent="resetPass()" class="mt-6 sm:mt-10 space-y-4">
        <div class="text-xl text-center text-white mt-4 sm:my-4 font-medium">Set New Password</div>

        <div class="block">
          <label class="text-sm text-gray-300 font-medium mb-1 block">Verification Code</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 2a1 1 0 00-1 1v1a1 1 0 002 0V3a1 1 0 00-1-1zM4 4h3a3 3 0 006 0h3a2 2 0 012 2v9a2 2 0 01-2 2H4a2 2 0 01-2-2V6a2 2 0 012-2zm2.5 7a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm2.45 4a2.5 2.5 0 10-4.9 0h4.9zM12 9a1 1 0 100 2h3a1 1 0 100-2h-3zm-1 4a1 1 0 011-1h2a1 1 0 110 2h-2a1 1 0 01-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <input type="text" class="pl-10 px-4 py-3 border border-gray-700 bg-gray-800/50 text-white rounded-lg w-full block outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all" 
                   placeholder="D5GC24" v-model="form_reset.otp_code">
          </div>
        </div>

        <!-- Add timer and resend OTP button -->
        <div class="flex gap-2 my-2">
          <label class="w-1/2 text-left text-sm text-gray-300">00:{{ timerCount }}</label>
          <label class="w-1/2 text-right text-sm text-gray-300 hover:text-green-400 cursor-pointer transition-colors">
            <a href="#" @click="resendOTP">Resend OTP</a>
          </label>
        </div>

        <div class="my-4 border-t border-gray-700"></div>

        <div class="block">
          <label class="text-sm text-gray-300 font-medium mb-1 block">New Password</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
              </svg>
            </div>
            <input type="password" class="pl-10 px-4 py-3 border border-gray-700 bg-gray-800/50 text-white rounded-lg w-full block outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all" 
                   placeholder="Enter new password" v-model="form_reset.new_password">
          </div>
        </div>

        <div class="block">
          <label class="text-sm text-gray-300 font-medium mb-1 block">Confirm Password</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
              </svg>
            </div>
            <input type="password" class="pl-10 px-4 py-3 border border-gray-700 bg-gray-800/50 text-white rounded-lg w-full block outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all" 
                   placeholder="Confirm new password" v-model="confirmPassword">
          </div>
        </div>

        <button type="submit" 
                class="block text-center px-4 py-3 w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg font-bold shadow-lg hover:shadow-green-500/30 transition-all duration-200 transform hover:-translate-y-1">
          <vue-loaders-ball-beat color="white" scale="0.7" v-show="loading"></vue-loaders-ball-beat>
          <span id="savepasswordBTN" v-show="!loading" class="flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
            Save New Password
          </span>
        </button>

        <div class="text-center pt-2">
          <router-link :to="{name: 'login'}" class="text-sm text-gray-300 hover:text-green-400 transition-colors">
            Back to Login
          </router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import {mapActions} from "vuex";
import Alert from "@/components/alert.vue";
import Loading from "vue-loading-overlay";
export default {
  components: {Loading, Alert},
  data: function () {
    return {
      isLoading: false,
      fullPage: true,
      loading: false,
      type: null,
      message: null,
      confirmPassword: "",
      password: "",
      verificationCode: "",
      timerCount: 120,
      form_reset: {
        username: this.$store.state.user_name,
        new_password: "",
        otp_code: "",
      },
    };
  },
  watch: {
    timerCount: {
      handler(value) {
        if (value > 0) {
          setTimeout(() => {
            this.timerCount--;
          }, 1000);
        }
      },
      immediate: true
    }
  },
  methods: {
    ...mapActions(["passwordReset", "passwordForgot", "fillSystemUser", "fillMessage"]),

    async resetPass() {
      let app = this
      app.loading = true
      $('#savepasswordBTN').html(' Please Wait ...');

      // Validate passwords match
      if (app.form_reset.new_password !== app.confirmPassword) {
        app.type = 'danger'
        app.message = 'Passwords do not match'
        app.loading = false
        $('#savepasswordBTN').html('Save New Password');
        return
      }

      let payload = {
        username: app.form_reset.username,
        new_password: btoa(app.form_reset.new_password),
        otp_code: btoa(app.form_reset.otp_code)
      }

      let response = await this.passwordReset(payload);
      if (response.status === 200) {
        this.type = 'success'
        this.message = response.message
        await app.fillSystemUser(app.form_reset.username);
        await app.fillMessage(app.message);
        app.$router.push({name: 'login'});
        return
      }
      else {
        this.type = 'danger'
        this.message = response.message
      }
      app.loading = false
      $('#savepasswordBTN').html('Save New Password');
    },

    async resendOTP() {
      let app = this
      app.loading = true
      
      const payload = {
        username: app.form_reset.username,
        dial_code: "254"
      }
      
      let response = await this.passwordForgot(payload);
      
      if (response.status === 200) {
        app.type = 'success'
        app.message = 'OTP code has been resent'
        // Reset timer
        app.timerCount = 120
      } else {
        app.type = 'danger'
        app.message = response.message
      }
      
      app.loading = false
    }
  }
}
</script>
