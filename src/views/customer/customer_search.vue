<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Customer" pageSubtitle="Search" />

    <!-- Search bar -->
    <div class="grid sm:grid-cols-1 md:grid-cols-2 gap-4">
      <div class="block px-4 flex">
        <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="phone"
          placeholder="Enter Phone Number to Search" @keyup.enter="searchPhone">
        <button
          class="ml-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-400"
          @click="searchPhone">
          Search
        </button>
      </div>
    </div>

    <div class="space-y-4 px-4 mt-4">
      <div v-for="(item, index) in customers" :key="item.id"
        class="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
        <!-- Header with account info -->
        <div
          class="grid grid-cols-2 bg-gradient-to-r from-indigo-50 to-blue-50 px-4 py-3 flex justify-between items-center">
          <div class="flex items-center space-x-2">
            <div class="bg-indigo-100 p-2 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <div>
              <h2 class="text-lg font-bold text-gray-800">{{ item.acc_number }}</h2>
              <h3 class="text-sm text-gray-600">{{ item.name ?? "No Name" }}</h3>
            </div>
          </div>

          <div class="block grid grid-cols-2">
            <div class="block text-right">
              <span class="text-xs text-gray-500">Created</span>
              <p class="text-sm font-medium text-gray-700">{{ moment(item.created_at).format('lll') }}</p>
            </div>

            <!-- Actions dropdown -->
            <div class="block mt-4 flex justify-end relative">
              <button class="bg-indigo-100 hover:bg-indigo-200 text-indigo-700 p-2 rounded-full transition-colors"
                @click="toggleCustomerDropdown()">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                  stroke="currentColor" class="w-5 h-5">
                  <path stroke-linecap="round" stroke-linejoin="round"
                    d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>

              <!-- Dropdown menu -->
              <div v-if="showCustomerDropdown"
                class="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 w-56 overflow-hidden">
                <ul class="py-1 divide-y divide-gray-100">

                  <li @click="isSMSModalOpen = true" class="hover:bg-green-50 transition-colors">
                    <a class="flex items-center px-4 py-2 text-sm text-green-700 cursor-pointer">
                      <svg class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"
                          fill="#2196F3" />
                        <path d="M7 9H17V11H7V9ZM7 12H13V14H7V12Z" fill="white" />
                      </svg>
                      Send SMS
                    </a>
                  </li>

                  <!-- Enable/Disable SMS -->
                  <li v-if="item.can_market !== '1'" @click="enableDisableSMS(item, 1)"
                    class="hover:bg-yellow-50 transition-colors">
                    <a class="flex items-center px-4 py-2 text-sm text-green-700 cursor-pointer">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                      </svg>
                      Enable SMS
                    </a>
                  </li>

                  <li v-else @click="enableDisableSMS(item, 0)" class="hover:bg-yellow-50 transition-colors">
                    <a class="flex items-center px-4 py-2 text-sm text-yellow-700 cursor-pointer">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
                      </svg>
                      Disable SMS
                    </a>
                  </li>

                  <!-- Award Bonus/Free Bet -->
                  <li v-if="checkHasPermissions('45')" @click="awardPromotion(item)"
                    class="hover:bg-green-50 transition-colors">
                    <a class="flex items-center px-4 py-2 text-sm text-cyan-700 cursor-pointer">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Award Promotion
                    </a>
                  </li>

                  <!-- Turn On Off Withdrawals -->
                  <li v-if="item.balance_status === '1'" @click="updateWithdrawals(item, 3)"
                    class="hover:bg-red-50 transition-colors">
                    <a class="flex items-center px-4 py-2 text-sm text-red-700 cursor-pointer">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                      Disable Withdrawals
                    </a>
                  </li>
                  <li v-else @click="updateWithdrawals(item, 1)" class="hover:bg-green-50 transition-colors">
                    <a class="flex items-center px-4 py-2 text-sm text-green-700 cursor-pointer">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                      Enable Withdrawals
                    </a>
                  </li>

                  <!-- Suspend Account -->
                  <li v-if="item.blacklist_status !== '1'" @click="updateAccountStatus(item, 1)"
                    class="hover:bg-green-50 transition-colors">
                    <a class="flex items-center px-4 py-2 text-sm text-green-700 cursor-pointer">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
                      </svg>
                      Activate Account
                    </a>
                  </li>
                  <li v-else @click="updateAccountStatus(item, 3)" class="hover:bg-red-50 transition-colors">
                    <a class="flex items-center px-4 py-2 text-sm text-red-700 cursor-pointer">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
                      </svg>
                      Suspend Account
                    </a>
                  </li>

                </ul>
              </div>
            </div>
          </div>
        </div>



        <!-- Customer details -->
        <div class="p-4">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Account Info -->
            <div class="bg-gray-50 rounded-lg p-3 space-y-2">
              <h3 class="font-medium text-indigo-700 border-b pb-1 mb-2">Account Info</h3>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Mobile:</span>
                <span class="font-medium">{{ item.msisdn }} <span class="text-xs text-gray-500">({{ item.network
                    }})</span></span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">IP Address:</span>
                <span class="font-medium">{{ item.ip_address }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Country:</span>
                <span class="font-medium">{{ item.country }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Balance:</span>
                <span class="font-medium">{{ item.balance }} <span class="text-green-600 text-xs">(Bonus: {{ item.bonus
                    }})</span></span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Marketing:</span>
                <span class="font-medium">{{ item.can_market ? 'Enabled' : 'Disabled' }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Login Status:</span>
                <span class="font-medium">{{ item.login_status }}</span>
              </div>
            </div>

            <!-- Financial Summary -->
            <div class="bg-gray-50 rounded-lg p-3 space-y-2">
              <h3 class="font-medium text-indigo-700 border-b pb-1 mb-2">Financial Summary</h3>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Deposits:</span>
                <span class="font-medium">{{ item.total_deposits }} <span class="text-xs text-gray-500">({{
                  item.deposit_count }} times)</span></span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Withdrawals:</span>
                <span class="font-medium">{{ item.total_withdrawals }} <span class="text-xs text-gray-500">({{
                  item.withdrawal_count }} times)</span></span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">P/L:</span>
                <div class="flex justify-between text-sm"> 
                      <span class="font-medium" :class="{
                        'text-green-600': getProfitLoss(item) > 0,
                        'text-red-600': getProfitLoss(item) < 0
                      }">
                        {{ formatCurrency(getProfitLoss(item)) }}
                      </span>
                </div>
              </div>
              

              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Bonuses:</span>
                <span class="font-medium">{{ item.total_bonus }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Referrals:</span>
                <span class="font-medium">{{ item.total_referrals }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Free Bets:</span>
                <span class="font-medium">{{ item.total_freebets }}</span>
              </div>
            </div>

            <!-- Betting Summary -->
            <div class="bg-gray-50 rounded-lg p-3 space-y-2">
              <h3 class="font-medium text-indigo-700 border-b pb-1 mb-2">Betting Summary</h3>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Total Stake:</span>
                <span class="font-medium">{{ item.total_bet_stake }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Total Winnings:</span>
                <span class="font-medium">{{ item.total_winnings }}</span>
              </div>


            </div>
          </div>


        </div>
      </div>
    </div>

    <!--  customer -->
    <div v-if="customers.length > 0" class="block mt-6 py-2 rounded-lg bg-white shadow-lg mx-3 mb-6 border ">
      <!-- Tab buttons -->
      <div class="flex flex-wrap sm:flex-nowrap overflow-x-auto scrollbar-hide gap-1 sm:gap-0 p-2 sm:p-0">
        <button @click="toggleTab('sports')" :class="{ 'bg-blue-500 text-white': activeTab === 'sports' }"
          class="flex-shrink-0 sm:flex-1 px-2 sm:px-0 mx-1 sm:mx-2 border border-b-2 border-blue-500 rounded-full text-xs sm:text-sm whitespace-nowrap"
          style="height: 32px; align-content: center; min-width: 80px;">
          <span class="hidden sm:inline">Sports Bets</span>
          <span class="sm:hidden">Sports</span>
        </button>

        <button @click="toggleTab('virtual')" :class="{ 'bg-green-500 text-white': activeTab === 'virtual' }"
          class="flex-shrink-0 sm:flex-1 px-2 sm:px-0 mx-1 sm:mx-2 border border-b-2 border-green-500 rounded-full text-xs sm:text-sm whitespace-nowrap"
          style="height: 32px; align-content: center; min-width: 80px;">
          <span class="hidden sm:inline">Virtual Bets</span>
          <span class="sm:hidden">Virtual</span>
        </button>

        <button @click="toggleTab('softgaming')" :class="{ 'bg-purple-500 text-white': activeTab === 'softgaming' }"
          class="flex-shrink-0 sm:flex-1 px-2 sm:px-0 mx-1 sm:mx-2 border border-b-2 border-purple-500 rounded-full text-xs sm:text-sm whitespace-nowrap"
          style="height: 32px; align-content: center; min-width: 80px;">
          <span class="hidden sm:inline">Soft Gaming</span>
          <span class="sm:hidden">SoftGame</span>
        </button>

        <!--        <button @click="toggleTab('casino')"
                :class="{ 'bg-purple-500 text-white': activeTab === 'casino' }"
                class="flex-1 p-0 mx-2 border border-b-2 border-purple-500 rounded-full"
                style="height: 40px; align-content: center">
          Casino Bets
        </button>-->

        <button @click="toggleTab('deposits')" :class="{ 'bg-green-900 text-white': activeTab === 'deposits' }"
          class="flex-shrink-0 sm:flex-1 px-2 sm:px-0 mx-1 sm:mx-2 border border-b-2 border-green-900 rounded-full text-xs sm:text-sm whitespace-nowrap"
          style="height: 32px; align-content: center; min-width: 80px;">
          <span class="hidden sm:inline">Deposits</span>
          <span class="sm:hidden">Deposits</span>
        </button>

        <button @click="toggleTab('withdrawals')" :class="{ 'bg-red-500 text-white': activeTab === 'withdrawals' }"
          class="flex-shrink-0 sm:flex-1 px-2 sm:px-0 mx-1 sm:mx-2 border border-b-2 border-red-500 rounded-full text-xs sm:text-sm whitespace-nowrap"
          style="height: 32px; align-content: center; min-width: 90px;">
          <span class="hidden sm:inline">Withdrawals</span>
          <span class="sm:hidden">Withdraw</span>
        </button>

        <button @click="toggleTab('transactions')" :class="{ 'bg-orange-500 text-white': activeTab === 'transactions' }"
          class="flex-shrink-0 sm:flex-1 px-2 sm:px-0 mx-1 sm:mx-2 border border-b-2 border-orange-500 rounded-full text-xs sm:text-sm whitespace-nowrap"
          style="height: 32px; align-content: center; min-width: 90px;">
          <span class="hidden sm:inline">Transactions</span>
          <span class="sm:hidden">Txns</span>
        </button>

        <button @click="toggleTab('freebets')" :class="{ 'bg-indigo-500 text-white': activeTab === 'freebets' }"
          class="flex-shrink-0 sm:flex-1 px-2 sm:px-0 mx-1 sm:mx-2 border border-b-2 border-indigo-500 rounded-full text-xs sm:text-sm whitespace-nowrap"
          style="height: 32px; align-content: center; min-width: 80px;">
          <span class="hidden sm:inline">Free Bets</span>
          <span class="sm:hidden">FreeBet</span>
        </button>

        <button @click="toggleTab('outbox')" :class="{ 'bg-cyan-500 text-white': activeTab === 'outbox' }"
          class="flex-shrink-0 sm:flex-1 px-2 sm:px-0 mx-1 sm:mx-2 border border-b-2 border-cyan-500 rounded-full text-xs sm:text-sm whitespace-nowrap"
          style="height: 32px; align-content: center; min-width: 70px;">
          <span class="hidden sm:inline">OutBox</span>
          <span class="sm:hidden">OutBox</span>
        </button>

      </div>

      <!-- Sports Bets Table -->
      <div class="p-1 overflow-x-auto" v-show="activeTab === 'sports'">
        <div class="min-w-full">
          <SportBetsTable ref="betsTable" />
        </div>
      </div>

      <!-- Virtual Bets Table -->
      <div class="p-1 overflow-x-auto" v-show="activeTab === 'virtual'">
        <div class="min-w-full">
          <VirtualBetsTable ref="virtualTable" />
        </div>
      </div>

      <!-- Soft Gaming Table -->
      <div class="p-1 overflow-x-auto" v-show="activeTab === 'softgaming'">
        <div class="min-w-full">
          <SoftGamingTable ref="softGamingTable" />
        </div>
      </div>

      <!-- Casino Bets Table -->
      <div class="p-1 overflow-x-auto" v-show="activeTab === 'casino'">
        <div class="min-w-full">
          <VirtualBetsTable ref="casinoTable" />
        </div>
      </div>

      <!-- Deposits Table -->
      <div class="p-1 overflow-x-auto" v-show="activeTab === 'deposits'">
        <div class="min-w-full">
          <DepositsTable ref="depositsTable" />
        </div>
      </div>

      <!-- Withdrawals Table -->
      <div class="p-1 overflow-x-auto" v-show="activeTab === 'withdrawals'">
        <div class="min-w-full">
          <WithdrawalsTable ref="withdrawalsTable" />
        </div>
      </div>

      <!-- Transactions Table -->
      <div class="p-1 overflow-x-auto" v-show="activeTab === 'transactions'">
        <div class="min-w-full">
          <TransactionsTable ref="transactionsTable" />
        </div>
      </div>

      <!-- OutBox Table -->
      <div class="p-1 overflow-x-auto" v-show="activeTab === 'outbox'">
        <div class="min-w-full">
          <OutBoxTable ref="outBoxTable" />
        </div>
      </div>

      <!-- Repayments -->

    </div>



    <!-- Message -->
    <div v-if="isSMSModalOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4">
        <!-- Header -->
        <div class="flex justify-between items-center p-4 border-b">
          <div>
            <h4 class="text-lg font-semibold text-gray-800">Send Message to {{ customer.msisdn }}</h4>
            <p v-if="selectedCampaign" class="text-sm text-gray-500 mt-1">
              <span class="font-medium">Source:</span> {{ selectedCampaign.utm_source }} |
              <span class="font-medium">Campaign:</span> {{ selectedCampaign.utm_campaign }} |
              <span class="font-medium">Medium:</span> {{ selectedCampaign.utm_medium }}
            </p>
          </div>
          <button @click="isSMSModalOpen = false" class="text-gray-400 hover:text-gray-600 text-xl">&times;</button>
        </div>

        <!-- Form Body -->
        <div class="p-4">
          <div class="space-y-4">

            <!-- Message Content -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Message Content
                <span class="text-xs text-gray-500 ml-1">({{ individual_blast_params.blast_message.length }}/160)</span>
              </label>
              <textarea v-model="individual_blast_params.blast_message" rows="5"
                class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500"
                placeholder="Type your message here..." maxlength="160"></textarea>
            </div>

            <!-- Preview -->
            <div class="bg-gray-50 p-3 rounded-md">
              <label class="block text-sm font-medium text-gray-700 mb-2">Preview</label>
              <div class="bg-white p-2 rounded border border-gray-200 min-h-[60px]">
                <p class="text-sm text-gray-600 whitespace-pre-wrap">
                  {{ individual_blast_params.blast_message || 'Message preview will appear here' }}
                </p>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-end space-x-2 mt-4 pt-3 border-t">
            <button @click="isSMSModalOpen = false"
              class="px-4 py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
              Cancel
            </button>
            <button @click="sendSMS()" class="px-4 py-2 text-sm bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
              :disabled="!individual_blast_params.blast_message"
              :class="{ 'opacity-50 cursor-not-allowed': !individual_blast_params.blast_message }">
              Send Message
            </button>
          </div>
        </div>
      </div>
    </div>



  </div>

  <!-- Award Promotion Modal -->
  <award-bonus-modal :show="showPromotionModal" :customer="selectedCustomer" @close="showPromotionModal = false"
    @awarded="handleAwardSuccess" />
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import { mapActions } from "vuex";
import accounting from "accounting";
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
import SportBetsTable from '@/views/customer/components/sports_bets_table.vue'
import VirtualBetsTable from '@/views/customer/components/virtual_bets_table.vue'
import SoftGamingTable from '@/views/customer/components/soft_gaming_table.vue'
import DepositsTable from '@/views/customer/components/deposits_table.vue'
import WithdrawalsTable from '@/views/customer/components/withdrawals_table.vue'
import TransactionsTable from '@/views/customer/components/transactions_table.vue'
import OutBoxTable from '@/views/customer/components/outbox_table.vue'
import CustomLoading from '@/components/common/CustomLoading.vue';
import PageHeader from '@/components/common/PageHeader.vue';
import AwardBonusModal from './components/AwardBonusModal.vue';

export default {

  components: {
    CustomLoading,
    VueDatePicker,
    SportBetsTable,
    VirtualBetsTable,
    SoftGamingTable,
    DepositsTable,
    WithdrawalsTable,
    TransactionsTable,
    OutBoxTable,
    PageHeader,
    AwardBonusModal
  },
  data() {
    return {
      phone: "",
      // phone: "************",
      customer: null,
      customers: [],
      accounting,
      moment,
      blockReasons: [
        { text: 'Fraud', value: 1 },
        { text: 'Self', value: 2 },
      ],

      isOpen: false,
      isLoading: false,
      fullPage: true,

      isSMSModalOpen: false,
      individual_blast_params: {
        timestamp: Date.now(),
        phone_number: "",
        blast_message: "",
        sender_id: "MOSSBETS",
        campaign_name: "MOSSBETS",
      },

      moreParams: {
        mobile_number: '',
        timestamp: Date.now(),
        start: "",
        end: "",
        limit: 1
      },

      activeTab: 'sports',
      showTables: false,
      showDropdown: [],
      showCustomerDropdown: false,

      // Award modal
      showPromotionModal: false,
      selectedCustomer: null,
    }
  },

  methods: {
    ...mapActions([
      "getProfiles",
      "resendCustomerOTP",
      "updateCustomerAccountStatus",
      "updateWithdrawalStatus",
      "enableDisableBulk",
      "fillMsisdn",
      "fillCustomer",
      "toggleSideMenu",
      "getPromotions",
      "sendIndividualSMS"
    ]),

    toggleSideM() {
      this.toggleSideMenu()
    },

    checkHasPermissions(permission) {
      return this.$store.state.permissions.includes(permission)
    },

    goBack() {
      this.$router.go(-1);
    },

    toggleDropdown(index) {
      this.showDropdown[index] = !this.showDropdown[index];
    },

    toggleCustomerDropdown() {
      this.showCustomerDropdown = !this.showCustomerDropdown;
    },

    async toggleTab(tab) {
      this.activeTab = tab;

      const tabConfigs = {
        'sports': { ref: 'betsTable', method: 'setBets', prop: 'bets' },
        'virtual': { ref: 'virtualTable', method: 'setBets', prop: 'bets' },
        'softgaming': { ref: 'softGamingTable', method: 'setBets', prop: 'bets' },
        'deposits': { ref: 'depositsTable', method: 'setDeposits', prop: 'deposits' },
        'withdrawals': { ref: 'withdrawalsTable', method: 'setWithdrawals', prop: 'withdrawals' },
        'transactions': { ref: 'transactionsTable', method: 'setTransactions', prop: 'transactions' },
        'outbox': { ref: 'outBoxTable', method: 'setOutbox', prop: 'data' }
      };

      const config = tabConfigs[tab];
      if (config && this.$refs[config.ref] && this.$refs[config.ref][config.prop].length === 0) {
        await this.$refs[config.ref][config.method](this.phone);
      }
    },

    getProfitLoss(item) {
    // Convert strings to numbers and handle potential null/undefined values
    const deposits = parseFloat(item.total_deposits || 0)
    const withdrawals = parseFloat(item.total_withdrawals || 0)
    const balance = parseFloat(item.balance || 0)
    
    // P/L = Total Deposits - (Total Withdrawals + Current Balance)
    return deposits - (withdrawals + balance)
  },
  formatCurrency(value) {
    // Use your existing formatCurrency method or add this if not present
    return accounting.formatNumber(value, 2)
  },

    async searchPhone() {
      if (this.phone.length > 0) {
        if (this.phone.slice(0, 1) === '0') this.phone = this.phone.slice(1);
        this.moreParams.mobile_number = this.phone;
        this.moreParams.timestamp = Date.now();
        await this.fillMsisdn(this.phone);
        await this.setCustomer();
      }
    },

    async setCustomer() {
      this.isLoading = true;
      this.customer = null;
      const response = await this.getProfiles(this.moreParams);

      this.status = response.status;
      if (this.status === 200) {
        this.customer = response.message.result[0];
        this.customers = response.message.result;
        this.showDropdown = Array(this.customers.length).fill(false);

        await this.fillCustomer(this.customers[0]);
        this.status = 1;
        this.showTables = true;

        // Load data for active tab
        await this.loadTabData(this.activeTab);
      } else {
        this.customers = [];
        this.showDropdown = [];
        await this.fillCustomer({ msisdn: null });
        this.status = 0;
      }

      this.isLoading = false;
    },

    async loadTabData(tab) {
      const tabConfigs = {
        'sports': { ref: 'betsTable', method: 'setBets' },
        'virtual': { ref: 'virtualTable', method: 'setBets' },
        'softgaming': { ref: 'softGamingTable', method: 'setBets' },
        'deposits': { ref: 'depositsTable', method: 'setDeposits' },
        'withdrawals': { ref: 'withdrawalsTable', method: 'setWithdrawals' },
        'transactions': { ref: 'transactionsTable', method: 'setTransactions' },
        'outbox': { ref: 'outBoxTable', method: 'setOutbox' }
      };

      const config = tabConfigs[tab];
      if (config && this.$refs[config.ref]) {
        await this.$refs[config.ref][config.method](this.phone);
      }
    },

    capitalizeFirstLetter(value) {
      if (!value) return '';
      value = value.toLowerCase();
      return value.charAt(0).toUpperCase() + value.slice(1);
    },

    async performAction(actionConfig) {
      this.toggleCustomerDropdown();

      this.$swal.fire({
        title: actionConfig.title,
        text: actionConfig.text,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: actionConfig.confirmText,
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async () => {
          return await actionConfig.action(actionConfig.payload);
        },
      }).then(async result => {
        if (result.value.status === 200) {
          this.$swal.fire(actionConfig.successTitle, result.value.message, 'success');
          if (actionConfig.refreshAfterSuccess) {
            await this.setCustomer();
          }
        } else {
          this.$swal.fire('Error!', result.value.message, 'error');
        }
      });
    },

    // Action methods
    async resendOTP(item) {
      const payload = {
        timestamp: Date.now(),
        profile_id: item.id,
      };

      await this.performAction({
        title: 'Resend OTP?',
        text: "Doing this resends OTP code to customer!",
        confirmText: 'Yes, send!',
        successTitle: 'Sent!',
        action: this.resendCustomerOTP,
        payload,
        refreshAfterSuccess: false
      });
    },

    async updateAccountStatus(item, status) {
      const payload = {
        profile_id: item.id,
        status: status,
        reason: status === 3 ? "Suspend Account" : "",
        timestamp: Date.now(),
      };

      await this.performAction({
        title: 'Update Account Status?',
        text: "Doing this updates account status for customer!",
        confirmText: 'Yes, update!',
        successTitle: 'Updated!',
        action: this.updateCustomerAccountStatus,
        payload,
        refreshAfterSuccess: true
      });
    },

    async updateWithdrawals(item, status) {
      const payload = {
        profile_id: item.id,
        status: status,
        timestamp: Date.now(),
      };

      await this.performAction({
        title: 'Update Withdrawal Status?',
        text: "Doing this updates withdrawal status for customer!",
        confirmText: 'Yes, update!',
        successTitle: 'Updated!',
        action: this.updateWithdrawalStatus,
        payload,
        refreshAfterSuccess: true
      });
    },

    async sendSMS(item) {
      console.log("sendIndividualSMS: ", this.customer);
      this.individual_blast_params.phone_number = this.customer.msisdn;
      this.individual_blast_params.blast_message;
      this.individual_blast_params.timestamp = Date.now();
      this.individual_blast_params.sender_id = "MOSSBETS";
      this.individual_blast_params.campaign_name = "MOSSBETS";
      console.log("SE :", JSON.stringify(this.individual_blast_params))

      this.isSMSModalOpen = false;

      const res = await this.sendIndividualSMS(this.individual_blast_params);

      if (res.status === 200) {
        this.$swal.fire({
          icon: 'success',
          title: 'Message Sent Successfully!',
          text: 'SMS has been sent to ' + this.customer.msisdn,
          showConfirmButton: true,
          timer: 3000,
          timerProgressBar: true,
          customClass: {
            popup: 'animate__animated animate__fadeInDown'
          }
        });

        // Reset the form
        this.individual_blast_params.blast_message = '';

        // Refresh outbox tab if it exists
        if (this.activeTab === 'outbox' && this.$refs.outBoxTable) {
          await this.$refs.outBoxTable.setOutbox(this.phone);
        }
      } else {
        this.$swal.fire({
          icon: 'error',
          title: 'Failed to Send Message',
          text: res.message || 'Something went wrong while sending the message',
          showConfirmButton: true,
          confirmButtonColor: '#3085d6',
          customClass: {
            popup: 'animate__animated animate__shakeX'
          }
        });
      }
    },

    async enableDisableSMS(item, status) {
      const payload = {
        profile_id: item.id,
        can_market: status.toString(),
        timestamp: Date.now(),
      };

      await this.performAction({
        title: 'Enable/Disable SMS?',
        text: "Doing this enables/disables sms for customer!",
        confirmText: 'Yes, update!',
        successTitle: 'Updated!',
        action: this.enableDisableBulk,
        payload,
        refreshAfterSuccess: true
      });
    },

    awardPromotion(item) {
      this.selectedCustomer = item;
      this.showPromotionModal = true;
      this.toggleCustomerDropdown();
    },

    handleAwardSuccess() {
      // Refresh customer data after successful award
      this.setCustomer();
    },
  }
}
</script>

<style scoped>
/* Hide scrollbar for tab buttons on mobile */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Ensure tables are responsive */
@media (max-width: 640px) {
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
  }

  /* Make tab buttons more touch-friendly on mobile */
  button {
    touch-action: manipulation;
  }
}
</style>
