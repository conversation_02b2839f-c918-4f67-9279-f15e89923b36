<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Customers" pageSubtitle="Self Exclusion" />

    <!-- Search Filters Panel -->
    <div class="bg-white rounded-lg shadow-lg mx-3 mb-4">
      <div class="p-4 border-b">
        <h3 class="text-lg font-medium text-gray-700">Filters</h3>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
        <!-- Customer Identification Group -->
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Customer Identification</h4>
          <div class="space-y-2">
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Mobile Number</label>
              <input type="text" v-model="payload.mobile_number" placeholder="Enter mobile number" 
                     class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                     @keyup.enter="applyFilters()">
            </div>
          </div>
        </div>

        <!-- Add to Self Exclusion Button (to the right of Customer Identification) -->
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors flex flex-col justify-center">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Actions</h4>
          <div class="flex items-center">
            <button 
              class="px-4 py-2 bg-primary text-white font-medium rounded-md hover:opacity-90 transition-colors text-sm"
              @click="openExclusionModal = true"
            >
              Add to Self Exclusion
            </button>
          </div>
        </div>
      </div>
    </div>

      <auto-table
        :headers="tableHeaders"
        :data="profiles"
        :loading="isLoading"
        :total-items="total"
        :items-per-page="limit"
        :current-page-prop="offset"
        :server-side-pagination="true"
        :pagination="total > limit"
        :show-items-count="true"
        @page-change="gotToPage"
      >
        <!-- Mobile Column -->
        <template #msisdn="{ item }">
          <div>
            <span class="font-bold">{{ item.msisdn }}</span>
            <div class="text-xs text-green-600">{{ item.network }}</div>
          </div>
        </template>

    
        <!-- Status Column -->
        <template #status="{ item }">
          <div class="text-center">
            <span 
              class="inline-block px-3 py-1 rounded-md text-white"
              :class="{
                'bg-green-500': parseInt(item.status) === 0,
                'bg-red-500': parseInt(item.status) !== 0,
              }"
            >
              {{ parseInt(item.status) === 0 ? 'Active' :  'Excluded' }}
            </span>
          </div>
        </template>

        <!-- Actions Column -->
        <template #actions="{ item }">
          <action-dropdown 
            button-text="Actions" 
            :show-text="false"
            menu-class="z-50 origin-top-right"
          >
            <action-item @click="openEditModal(item)">
              <template #icon>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </template>
              Edit Phone Number
            </action-item>
            
            <action-item v-if="parseInt(item.status) === 1" @click="updateStatus(item, 0)">
              <template #icon>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </template>
              <span class="text-green-500">Activate</span>
            </action-item>
            
            <action-item v-else @click="updateStatus(item, 1)">
              <template #icon>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </template>
              <span class="text-orange-500">Exclude</span>
            </action-item>
            
            <div class="border-t border-gray-100 my-1"></div>
            
            <!-- <action-item @click="deleteExclusion(item)">
              <template #icon>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </template>
              <span class="text-red-500">Delete</span>
            </action-item> -->
          </action-dropdown>
        </template>
      </auto-table>
 

    <!-- Edit Phone Number Modal -->
    <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
         :class="{ 'opacity-100 pointer-events-auto': openEditPhoneModal, 'opacity-0 pointer-events-none': !openEditPhoneModal }">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50" @click="openEditPhoneModal = false"></div>
      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded-lg shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <!-- Title -->
          <div class="flex justify-between items-center pb-3 border-b">
            <h3 class="text-lg font-medium text-gray-900">Edit Phone Number</h3>
            <div class="modal-close cursor-pointer z-50" @click="openEditPhoneModal = false">
              <svg class="fill-current text-gray-500" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>
          
          <!-- Body -->
          <div class="my-4">
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">Mobile Number</label>
              <input type="text" v-model="editForm.msisdn" placeholder="Enter mobile number" 
                     class="w-full px-3 py-2 text-sm border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
            </div>
          </div>
          
          <!-- Footer -->
          <div class="flex justify-end pt-2 border-t">
            <button 
              class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md mr-2 hover:bg-gray-300 transition-colors"
              @click="openEditPhoneModal = false"
            >
              Cancel
            </button>
            <button 
              class="px-4 py-2 bg-primary text-white rounded-md hover:opacity-90 transition-colors"
              @click="updatePhoneNumber"
            >
              Update
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Self Exclusion Modal -->
    <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center"
         :class="{ 'opacity-100 pointer-events-auto': openExclusionModal, 'opacity-0 pointer-events-none': !openExclusionModal }">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50" @click="openExclusionModal = false"></div>
      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded-lg shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <!-- Title -->
          <div class="flex justify-between items-center pb-3 border-b">
            <h3 class="text-lg font-medium text-gray-900">Add to Self Exclusion</h3>
            <div class="modal-close cursor-pointer z-50" @click="openExclusionModal = false">
              <svg class="fill-current text-gray-500" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>
          
          <!-- Body -->
          <div class="my-4">
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">Mobile Number</label>
              <input type="text" v-model="exclusionForm.msisdn" placeholder="Enter mobile number" 
                     class="w-full px-3 py-2 text-sm border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
            </div>
          </div>
          
          <!-- Footer -->
          <div class="flex justify-end pt-2 border-t">
            <button 
              class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md mr-2 hover:bg-gray-300 transition-colors"
              @click="openExclusionModal = false"
            >
              Cancel
            </button>
            <button 
              class="px-4 py-2 bg-primary text-white rounded-md hover:opacity-90 transition-colors"
              @click="addToSelfExclusion"
            >
              Add to Self Exclusion
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import {mapActions} from "vuex";
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
import $ from "jquery";
import {AutoTable, ActionDropdown, ActionItem, CustomLoading} from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
    VueDatePicker,
  },
  data() {
    return {
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",
      //
      openModal: false,
      customer: null,
      isLoading: false,
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      payload: {
        status: "",
        mobile_number: "",
        acc_number: "",
        network: "",
        min_balance: "",
        max_balance: "",
        start: "",
        end: "",
        page: 1,
        limit: 100,
        skip_cache: "",
        timestamp: "",
        sort: "",
        product_id: "",
        group_id: "",
      },
      profiles: [],
      moment: moment,
      showDropdown: [],
      //
      date: null,
      dateFormat: 'MMM DD, YYYY',
      showAdvancedFilters: false,
      //

      //
      products: [],
      moreParams: {
        start: '',
        end: '',
        page: '1',
        limit: "100",
        timestamp: 'timestamp',
        skip_cache: '',
        product_status: '',
        product_name: '',
        product_id: '',
        export: '',
      },

      //cust
      form: {
        timestamp: "",
        msisdn: "",
        customer_name: "",
        email: "",
        id_type: "",
        national_id: "",
        group_id: "",
        product_id: "",
      },
      showDetailsModal: false,
      selectedCustomer: null,
      tableHeaders: [
        { key: 'id', label: 'ID', align: 'center' },
        { key: 'msisdn', label: 'Mobile', align: 'center' },
        { key: 'status', label: 'Status', align: 'center' },
        { key: 'created_at', label: 'Date', align: 'center' },
        { key: 'actions', label: 'Actions', align: 'center' }
      ],
      // Add new form for exclusion
      exclusionForm: {
        msisdn: "",
        timestamp: ""
      },
      // Add modal control
      openExclusionModal: false,
      // Add new properties for edit functionality
      openEditPhoneModal: false,
      editForm: {
        id: null,
        msisdn: "",
        timestamp: ""
      },
    }
  },
  //
  async mounted() {
    await this.setProfiles()
  },

  methods: {
    ...mapActions([
      "getProfilesSelfExclusion", 
      "addSelfExclusionCustomer", 
      "updateSelfExclusionCustomer", 
      "toggleSideMenu"
    ]),
    //
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    //
    async setProfiles() {
      let app = this
      app.isLoading = true
      app.payload.timestamp = Date.now()

      let response = await this.getProfilesSelfExclusion(app.payload)
      // console.log("Profiles: " + JSON.stringify(response))
      if (response.status === 200) {
        app.profiles = response.message.result
        this.total = parseInt(response.message.record_count)

        app.showDropdown = []
        for (let i = 0; i < app.profiles.length; i++) {
          app.showDropdown.push(false)
        }
      } else {
        app.profiles = []
        app.total = 0
      }

      app.isLoading = false
    },

    // Apply filters
    async applyFilters() {
      this.offset = 1
      this.payload.page = 1
      await this.setProfiles()
    },

    // Reset filters
    resetFilters() {
      this.payload = {
        status: "",
        mobile_number: "",
        acc_number: "",
        network: "",
        min_balance: "",
        max_balance: "",
        start: "",
        end: "",
        page: 1,
        limit: 10,
        skip_cache: "",
        timestamp: "",
        sort: "",
        product_id: "",
        group_id: "",
      }
      this.date = null
      this.offset = 1
      this.setProfiles()
    },

    //
    gotToPage(page) {
      let vm = this
      vm.payload.page = page
      vm.offset = page
      vm.setProfiles()
    },

    //
    async selectDate() {
      if (this.date) {
        this.payload.start = this.formatDate(this.date[0])
        this.payload.end = this.formatDate(this.date[1])
      } else {
        this.payload.start = ''
        this.payload.end = ''
      }
    },

    //
    formatDate(date) {
      let d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    //
    async viewReferrals(row) {
      this.closeDropdown()
      await this.$router.push({name: 'referrals', query: {msisdn: row.msisdn}})
    },

    //
    toggleSearchDropdown() {
      // Toggle the value of searchDropdown
      this.searchDropdown = !this.searchDropdown;
    },

    //
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },
    viewCustomerDetails(customer) {
      this.selectedCustomer = customer;
      this.showDetailsModal = true;
      this.closeDropdown();
    },
    getStatusText(status) {
      const statusMap = {
        '1': 'Active',
        '3': 'Deactivated',
        '0': 'Inactive'
      };
      return statusMap[status] || 'Unknown';
    },
    getLoginStatusText(status) {
      const statusMap = {
        '1': 'Active',
        '2': 'Pending Verification',
        '3': 'Locked',
        '4': 'Suspended',
        '5': 'Deactivated',
        '6': 'New'
      };
      return statusMap[status] || 'Unknown';
    },
    // Add new method for self exclusion
    async addToSelfExclusion() {
      let app = this
      
      if (!app.exclusionForm.msisdn) {
        app.$swal.fire('Error!', 'Please enter a mobile number', 'error')
        return
      }
      
      app.exclusionForm.timestamp = Date.now()
      const payload = app.exclusionForm

      app.$swal.fire({
        title: 'Are you sure?',
        text: "This will add the customer to self exclusion list!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          app.isLoading = true
          return await this.addSelfExclusionCustomer(payload)
        },
      })
      .then(async (result) => {
        app.isLoading = false
        if (result.value && result.value.status === 200) {
          app.openExclusionModal = false
          app.$swal.fire({
            title: 'Added!',
            text: result.value.message,
            icon: 'success'
          })
          app.exclusionForm.msisdn = "" // Clear the form
          await app.setProfiles() // Refresh the list
        } else if (result.value) {
          app.$swal.fire('Error!', result.value.message, 'error')
        }
      })
    },
    // Open edit modal and populate form
    openEditModal(item) {
      this.editForm.id = item.id
      this.editForm.msisdn = item.msisdn
      this.openEditPhoneModal = true
    },
    // Update phone number
    async updatePhoneNumber() {
      let app = this
      
      if (!app.editForm.msisdn) {
        app.$swal.fire('Error!', 'Please enter a mobile number', 'error')
        return
      }
      
      app.editForm.timestamp = Date.now()
      const payload = app.editForm

      app.$swal.fire({
        title: 'Update Phone Number?',
        text: "This will update the customer's phone number in self exclusion list!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          app.isLoading = true
          return await this.updateSelfExclusionCustomer(payload)
        },
      })
      .then(async (result) => {
        app.isLoading = false
        if (result.value && result.value.status === 200) {
          app.openEditPhoneModal = false
          app.$swal.fire({
            title: 'Updated!',
            text: result.value.message,
            icon: 'success'
          })
          await app.setProfiles() // Refresh the list
        } else if (result.value) {
          app.$swal.fire('Error!', result.value.message, 'error')
        }
      })
    },
    // Update status
    async updateStatus(item, status) {
      let app = this
      
      const payload = {
        id: item.id,
        status: status,
        timestamp: Date.now()
      }

      app.$swal.fire({
        title: status === 1 ? 'Activate Self Exclusion?' : 'Deactivate Self Exclusion?',
        text: status === 1 ? "This will activate the self exclusion!" : "This will deactivate the self exclusion!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, proceed!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          app.isLoading = true
          return await this.updateSelfExclusionCustomer(payload)
        },
      })
      .then(async (result) => {
        app.isLoading = false
        if (result.value && result.value.status === 200) {
          app.$swal.fire({
            title: 'Updated!',
            text: result.value.message,
            icon: 'success'
          })
          await app.setProfiles() // Refresh the list
        } else if (result.value) {
          app.$swal.fire('Error!', result.value.message, 'error')
        }
      })
    },
    // Delete exclusion
    async deleteExclusion(item) {
      let app = this
      
      const payload = {
        id: item.id,
        timestamp: Date.now()
      }

      app.$swal.fire({
        title: 'Delete Self Exclusion?',
        text: "This will permanently remove this self exclusion record!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, delete!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          app.isLoading = true
          return await this.deleteSelfExclusion(payload)
        },
      })
      .then(async (result) => {
        app.isLoading = false
        if (result.value && result.value.status === 200) {
          app.$swal.fire({
            title: 'Deleted!',
            text: result.value.message,
            icon: 'success'
          })
          await app.setProfiles() // Refresh the list
        } else if (result.value) {
          app.$swal.fire('Error!', result.value.message, 'error')
        }
      })
    },
  }
}
</script>

<style scoped>
/* Add any additional styles here */
.modal {
  transition: opacity 0.25s ease;
}

/* Ensure dropdowns are on top */
:deep(.action-dropdown) {
  z-index: 50 !important;
}

:deep(.action-dropdown .absolute) {
  z-index: 50 !important;
}


/* Date picker styles */
:deep(.dp__input) {
  padding: 0.25rem 2rem;
  font-size: 0.75rem;
  line-height: 1rem;
  border-radius: 0.375rem;
}

:deep(.dp__main) {
  font-size: 0.75rem;
}
</style>
