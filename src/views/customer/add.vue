<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">

    <div class="flex font-medium p-6 pb-1 border-b gap-4 mb-5">
      <div class="flex-shrink font-medium">
        <i v-if="!this.$store.state.isSideMenuOpen" class="fa fa-bars text-black text-lg" @click="toggleSideM"></i>
        <i v-else class="fa fa-close text-black text-lg" @click="toggleSideM"></i>
      </div>
      <div class="flex-grow font-medium  pb-1 ">Add New Customer -
        <small>({{ this.$store.state.investmentGroup.group_name }})</small></div>
    </div>


    <div class="block px-6 py-4 rounded-lg bg-white shadow-lg mx-3 border ">

      <div class="grid grid-cols-3 gap-4 ">
        <div class="block mb-4">
          <label class="text-xs mb-1 block font-medium">Customer Name</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.customer_name">
        </div>
        <div class="block mb-4">
          <label class="text-xs mb-1 block font-medium">Phone Number</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.msisdn">
        </div>
        <div class="block mb-4">
          <label class="text-xs mb-1 block font-medium">Email</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.email">
        </div>
      </div>

      <div class="grid grid-cols-3 gap-4 ">
        <div class="block">
          <label class="text-xs mb-1 block font-medium ">Identify Type</label>
          <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.id_type">
            <option value="" disabled selected>Select ID Type</option> <!-- Placeholder option -->
            <option v-for="type in identifier_types" :value="type.value">
              {{ type.text }}
            </option>
          </select>
        </div>

        <div class="block mb-4">
          <label class="text-xs mb-1 block font-medium">National Number</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.national_id">
        </div>
      </div>

      <div class="block">
        <label class="text-xs mb-1 block font-medium ">Product</label>
        <select class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.product_id">
          <option value="" disabled selected>Select Product</option> <!-- Placeholder option -->
          <option v-for="type in products" :value="type.value">
            {{ type.text }}
          </option>
        </select>
      </div>

      <div class="gap-4 mt-10 block text-sm text-right">
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2"
                     :to="{name: 'investment_groups'}">
          Back
        </router-link>
        <router-link class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" :to="{name: 'profiles'}">
          Customers
        </router-link>
        <button class="inline-block px-10 py-2 bg-primary rounded-md font-medium ml-2" @click="createCustomer()"
                id="addMember">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat>
          Add Customer
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";

export default {
  data() {
    return {
      items: [],
      loading: false,
      isLoading: false,
      form: {
        timestamp: "",
        msisdn: "",
        customer_name: "",
        email: "",
        id_type: "",
        national_id: "",
        group_id: `${this.$store.state.investmentGroup.group_id}`,
        product_id: "1",
      },
      identifier_types: [
        {text: 'NATIONAL ID', value: 'NATIONAL_ID'},
        {text: 'HUDUMA ID', value: 'HUDUMA_ID'},
        {text: 'PASSPORT', value: 'PASSPORT'},
        {text: 'ALIEN ID', value: 'ALIEN_ID'}
      ],

      //
      products: [],
      moreParams: {
        start: '',
        end: '',
        page: '1',
        limit: "10",
        timestamp: 'timestamp',
        skip_cache: '',
        product_status: '',
        product_name: '',
        product_id: '',
        export: '',
      },
    }
  },
  components: {
    Loading
  },
  async mounted() {
    await this.setProducts()
  },
  methods: {
    ...mapActions(["getProducts", "addProfile", "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },

    //
    //
    async createCustomer() {
      let app = this
      app.form.timestamp = Date.now()
      console.log("acl_list", JSON.stringify(app.form))

      if (!this.checkFormValidity(app.form)) {
        // Handle the case where there are invalid elements
        console.log("There are null or empty elements in the form.");
        app.$swal.fire({
          icon: 'error',
          title: 'Error',
          text: `Fill in all data.`
        });
        return
      }

      const payload = app.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this adds a new Product!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, Add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          app.loading = true
          $('#addMember').html(' Please Wait ...');
          return await this.addProfile(payload)

        },
      })
          .then(async (result) => {
            $('#addMember').html('Add Customer');
            app.loading = false
            if (result.value.status === 200) {
              app.$swal.fire({
                title: 'Added!',
                text: result.value.message,
                icon: 'success'
              }).then(async (result) => {
                await app.$router.push({name: 'roles'})
              })

            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    //
    async setProducts() {
      let app = this
      app.isLoading = true

      const params = new URLSearchParams();

      for (const key in app.moreParams) {
        if (app.moreParams.hasOwnProperty(key)) {
          params.append(key, app.moreParams[key]);
        }
      }

      const queryString = params.toString();

      let response = await this.getProducts(queryString)

      if (response.status === 200) {
        response.message.result.forEach(function (item) {
          let _products = {text: item.product_name, value: item.product_id}
          app.products.push(_products)
        })
        console.log("Products : " + JSON.stringify(app.products))
      } else {

      }
      app.isLoading = false
    },

    //
    checkFormValidity(payload) {
      for (const key in payload) {
        if (payload[key] === null || payload[key] === '') {
          return false;
        }
      }
      return true;
    },


  }
}
</script>
