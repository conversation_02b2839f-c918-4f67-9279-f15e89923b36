<template>
  <div class="bg-white rounded-lg shadow-lg overflow-hidden">
    <custom-loading :active="isLoading" :is-full-page="false" color="red" :blur="3" />

    <!-- Soft Gaming Table using AutoTable component -->
    <auto-table
      :data="bets"
      :loading="isLoading"
      :has-actions="false"
      :pagination="true"
      :server-side-pagination="true"
      :total-items="total"
      :items-per-page="limit"
      :current-page-prop="offset"
      :items-per-page-options="[10, 25, 50, 100]"
      :show-items-count="true"
      :decimal-places="decimalPlaces"
      :exclude-columns="excludeColumns"
      :header-styles="headerStyles"
      :column-styles="columnStyles"
      @page-change="handlePageChange"
      @items-per-page-change="handleLimitChange"
    >
      <!-- Customer Column -->
      <template #customer="{ item }">
        <div class="text-sm">
          <div class="font-medium">{{ item.customer }}</div>
          <div class="text-gray-500">{{ item.msisdn }}</div>
          <div class="text-gray-400 text-xs">{{ item.ip_address }}</div>
        </div>
      </template>

      <!-- Bet Reference Column -->
      <template #bet_reference="{ item }">
        <div class="text-sm font-mono">{{ item.bet_reference }}</div>
      </template>

      <template #created_by="{ item }">
        <strong class="badge bg-indigo-500 text-white">
          <template v-if="item.created_by && item.created_by.includes(':') && item.created_by.split(':')[1]">
            {{ item.created_by.split(':')[1].charAt(0).toUpperCase() + item.created_by.split(':')[1].slice(1) }}
          </template>
          <template v-else-if="item.created_by && item.created_by.includes(' ') && item.created_by.split(' ')[0]">
            {{ item.created_by.split(' ')[0].charAt(0).toUpperCase() + item.created_by.split(' ')[0].slice(1) }}
          </template>
          <template v-else>
            {{ item.created_by || 'N/A' }}
          </template>
        </strong>
    </template>

      <!-- Status Column -->
      <template #status="{ item }">
        <div class="text-center">
          <span class="px-2 py-1 text-xs font-medium rounded-full" :class="getStatusClass(item.status)">
            {{ getStatusText(item.status) }}
          </span>
        </div>
      </template>

      <!-- Created At Column -->
      <template #created_at="{ item }">
        <div class="text-xs text-gray-600">
          {{ moment(item.created_at).format('MMM DD, YYYY HH:mm') }}
        </div>
      </template>

      <!-- Bet Amount Column -->
      <template #bet_amount="{ item }">
        <div class="text-right font-medium">
          {{ formatCurrency(item.bet_amount) }}
        </div>
      </template>

      <!-- Possible Win Column -->
      <template #possible_win="{ item }">
        <div class="text-right font-medium text-green-600">
          {{ formatCurrency(item.possible_win) }}
        </div>
      </template>

    </auto-table>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import moment from "moment-timezone";
import { AutoTable, CustomLoading } from '@/components/common';

export default {
  name: "SoftGamingTable",
  components: {
    AutoTable,
    CustomLoading
  },
  data() {
    return {
      isLoading: false,
      total: 0,
      limit: 100,
      offset: 1,
      bets: [],
      phone: '',
      moreParams: {
        customer: '',
        page: 1,
        limit: 100,
        timestamp: Date.now(),
        skip_cache: false,
      },
      decimalPlaces: {
        bet_amount: 2,
        possible_win: 2,
        // total_odd: 2,
      },
      tableHeaders: [
        { key: 'bet_id', label: 'BetID', align: 'left' },
        { key: 'bet_reference', label: 'Bet Reference', align: 'left' },
        { key: 'created_at', label: 'Created At', align: 'left' },
        { key: 'customer', label: 'Customer', align: 'left' },
        { key: 'status', label: 'Status', align: 'left' },
      ],
      excludeColumns: [
        'trx_count',
        'provider_name',
        'client_id',
        'bet_currency',
        'total_games',
        'extra_data',
        'msisdn',
        'internal_game_id',
        'ip_address',
        'profile_id',
        'excise_tax',
        'witholding_tax',
        'kra_report',
        'bet_transaction_id',
        'bet_credit_transaction_id',
      ],
      columnStyles: {
        bet_amount: 'text-right font-bold',
        possible_win: 'text-right font-bold text-green-600',
        total_odd: 'text-left font-bold text-purple-700',
      },
      headerStyles: {
        bet_reference: 'text-left font-bold text-black-700 bg-purple-50 text-xs',
        created_at: 'text-left font-bold text-black-700 bg-purple-50 text-xs',
        customer: 'text-left font-bold text-black-700 bg-purple-50 text-xs',
      },
      moment
    }
  },
  methods: {
    ...mapActions(["getSoftGaming"]),

    async setBets(phoneNumber) {
      this.isLoading = true;
      this.phone = phoneNumber;
      this.moreParams.customer = phoneNumber;
      this.moreParams.page = this.offset;
      this.moreParams.limit = this.limit;
      this.moreParams.timestamp = Date.now();

      const params = new URLSearchParams(this.moreParams);
      const response = await this.getSoftGaming(params);

      this.bets = [];
      this.total = 0;

      if (response.status === 200) {
        this.bets = response.message.result || response.message || [];
        
        if (response.message.record_count) {
          this.total = parseInt(response.message.record_count);
        } else if (response.message.total_count) {
          this.total = parseInt(response.message.total_count);
        } else {
          this.total = this.bets.length;
        }
      } else {
        console.error("Error fetching soft gaming data:", response.message);
      }

      this.isLoading = false;
    },

    handlePageChange(page) {
      this.offset = page;
      this.moreParams.page = page;
      this.setBets(this.phone);
    },

    handleLimitChange(newLimit) {
      this.limit = newLimit;
      this.offset = 1;
      this.moreParams.limit = newLimit;
      this.moreParams.page = 1;
      this.setBets(this.phone);
    },

    formatCurrency(value) {
      if (!value || isNaN(value)) return '0.00';
      return parseFloat(value).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    getStatusClass(status) {
      const statusMap = {
        '0': 'bg-yellow-100 text-yellow-800',
        '1': 'bg-green-100 text-green-800',
        '3': 'bg-gray-100 text-gray-800',
      };
      return statusMap[status] || 'bg-gray-100 text-gray-800';
    },

    getStatusText(status) {
      const statusMap = {
        '1': 'Won',
        '0': 'Lost',
        '3': 'Cancelled',
      };
      return statusMap[status] || 'Unknown';
    }
  }
}
</script>

<style scoped>
.auto-table {
  font-size: 0.875rem;
}
</style>
