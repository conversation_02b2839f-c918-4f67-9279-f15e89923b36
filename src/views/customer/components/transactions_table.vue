<template>
  <div class="py-2 bg-white relative overflow-hidden">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <div class="block py-4 bg-white overflow-x-auto">
      <!-- Filters Section -->
      <div class="px-2 pb-4 flex flex-wrap gap-2 w-full">
        <!-- Date Range Picker -->
        <div class="w-64">
          <label class="block text-xs font-bold text-gray-700 mb-1">Date Range</label>
          <vue-date-picker
            v-model="date"
            :preset-ranges="presetRanges"
            :enable-time-picker="false"
            :is-range="true"
            :format="'yyyy-MM-dd'"
            class="w-full text-xs"
            @update:model-value="selectDate"
          />
        </div>

        <!-- Transaction ID Search -->
        <div class="w-48">
          <label class="block text-xs font-bold text-gray-700 mb-1">Transaction ID</label>
          <input
            type="text"
            class="block w-full px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm"
            v-model="moreParams.transaction_id"
            @keyup.enter="applyFilters()"
          >
        </div>

        <!-- Amount Search -->
        <div class="w-48">
          <label class="block text-xs font-bold text-gray-700 mb-1">Amount</label>
          <input
            type="text"
            class="block w-full px-2 py-1.5 text-xs text-gray-700 border rounded-md shadow-sm"
            v-model="moreParams.amount"
            @keyup.enter="applyFilters()"
          >
        </div>

        <!-- Apply Button -->
        <div class="flex items-end">
          <button
            @click="applyFilters()"
            class="px-3 py-1.5 text-xs bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
          >
            Apply
          </button>
        </div>

        <!-- Reset Button -->
        <div class="flex items-end">
          <button
            @click="resetFilters()"
            class="px-3 py-1.5 text-xs bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
          >
            Reset
          </button>
        </div>
      </div>

      <!-- Transactions Table -->
      <auto-table
        :headers="tableHeaders"
        :data="transactions"
        :loading="isLoading"
        :total-items="total"
        :items-per-page="limit"
        :current-page-prop="offset"
        :server-side-pagination="true"
        :pagination="total > limit"
        :show-items-count="true"
        :items-per-page-options="[10, 25, 50, 100]"
        @page-change="gotToPage"
        @items-per-page-change="handleLimitChange"
      >
        <!-- Transaction Type Column -->
        <template #transaction_type="{ item }">
          <div class="badge-container">
            <div
              class="badge transaction-type-badge"
              :class="{
                'type-credit': item.transaction_type === 'CREDIT',
                'type-debit': item.transaction_type === 'DEBIT',
                'type-refund': item.transaction_type === 'REFUND',
                'type-voided': item.transaction_type === 'VOIDED',
                'type-default': !['CREDIT', 'DEBIT', 'REFUND', 'VOIDED'].includes(item.transaction_type)
              }"
              @click="copyToClipboard(item.transaction_type)"
              title="Click to copy"
            >
              {{ item.transaction_type }}
            </div>
          </div>
        </template>

        <!-- Customer Column -->
        <template #msisdn="{ item }">
          <div class="badge-container">
            <div
              class="badge msisdn-badge"
              @click="copyToClipboard('+' + item.msisdn)"
              title="Click to copy"
            >
              +{{ item.msisdn }}
            </div>
          </div>
        </template>

        <!-- Amount Column -->
        <template #amount="{ item }">
          <div class="badge-container">
            <div
              class="badge amount-badge"
              @click="copyToClipboard(item.currency + '. ' + parseFloat(item.amount).toFixed(2))"
              title="Click to copy"
            >
              {{ item.currency }}. {{ parseFloat(item.amount).toFixed(2) }}
            </div>
          </div>
        </template>

        <!-- Description Column -->
        <template #description="{ item }">
          <div class="badge-container justify-start">
            <div
              class="badge description-badge"
              @click="copyToClipboard(item.description)"
              title="Click to copy"
            >
              {{ item.description }}
            </div>
          </div>
        </template>

        <!-- Source Column -->
        <template #source="{ item }">
          <div class="badge-container">
            <div
              class="badge source-badge"
              @click="copyToClipboard(item.source || 'N/A')"
              title="Click to copy"
            >
              {{ item.source || 'N/A' }}
            </div>
          </div>
        </template>

        <!-- Date Column -->
        <template #created_at="{ item }">
          <div class="badge-container justify-start">
            <div
              class="badge date-badge"
              @click="copyToClipboard(moment(item.created_at).format('llll'))"
              title="Click to copy"
            >
              {{ moment(item.created_at).format('llll') }}
            </div>
          </div>
        </template>
      </auto-table>

      <!-- Empty State -->
      <div v-if="transactions.length === 0 && !isLoading" class="py-8 text-center text-gray-500">
        No transaction data available
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import { mapActions } from "vuex";
import { endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths } from "date-fns";
import VueDatePicker from "@vuepic/vue-datepicker";
import { AutoTable, CustomLoading } from '@/components/common';

export default {
  components: {
    VueDatePicker,
    AutoTable,
    CustomLoading
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      total: 0,
      limit: 100,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      transactions: [],
      phone: null,
      tableHeaders: [
        { key: 'transaction_type', label: 'Trxn Type', align: 'center' },
        { key: 'msisdn', label: 'Customer', align: 'center' },
        { key: 'amount', label: 'Amount', align: 'center' },
        { key: 'description', label: 'Description', align: 'left', responsive: 'hidden-mobile' },
        { key: 'source', label: 'Source', align: 'center', responsive: 'hidden-mobile' },
        { key: 'created_at', label: 'Date', align: 'left' }
      ],
      moreParams: {
        mobile_number: '',
        profile_id: '',
        transaction_id: '',
        amount: '',
        start: '',
        end: '',
        page: '1',
        limit: "100",
        timestamp: Date.now().toString(),
        skip_cache: '',
      },
      date: null,
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],
    }
  },

  methods: {
    ...mapActions(["getTransactions", "toggleSideMenu"]),

    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    async selectDate() {
      if (!this.date || !Array.isArray(this.date) || this.date.length !== 2) return;

      this.moreParams.start = this.formatDate(this.date[0]);
      this.moreParams.end = this.formatDate(this.date[1]);
      this.applyFilters();
    },

    formatDate(date) {
      var d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    applyFilters() {
      this.moreParams.page = '1';
      this.offset = 1;
      this.setTransactions(this.phone);
    },

    resetFilters() {
      this.date = null;
      this.moreParams = {
        mobile_number: this.phone,
        profile_id: '',
        transaction_id: '',
        amount: '',
        start: '',
        end: '',
        page: '1',
        limit: "10",
        timestamp: Date.now().toString(),
        skip_cache: '',
      };
      this.offset = 1;
      this.setTransactions(this.phone);
    },

    // Pagination
    gotToPage(page) {
      this.moreParams.page = page.toString();
      this.offset = page;
      this.setTransactions(this.phone);
    },

    // Handle items per page change
    handleLimitChange(newLimit) {
      this.limit = newLimit;
      this.moreParams.limit = newLimit.toString();
      this.offset = 1;
      this.moreParams.page = '1';
      this.setTransactions(this.phone);
    },

    async setTransactions(num) {
      if (!num) return;

      this.isLoading = true;
      this.moreParams.mobile_number = num;
      this.phone = num;
      this.moreParams.timestamp = Date.now().toString();

      try {
        const params = new URLSearchParams();
        for (const key in this.moreParams) {
          if (this.moreParams.hasOwnProperty(key)) {
            params.append(key, this.moreParams[key]);
          }
        }
        const queryString = params.toString();

        const response = await this.getTransactions(queryString);

        if (response && response.status === 200) {
          this.transactions = response.message.result || [];
          this.total = parseInt(response.message.record_count || 0);
          this.showDropdown = Array(this.transactions.length).fill(false);
        } else {
          this.transactions = [];
          this.total = 0;
          this.showDropdown = [];
        }
      } catch (error) {
        console.error("Error fetching transactions:", error);
        this.transactions = [];
        this.total = 0;
      } finally {
        this.isLoading = false;
      }
    },
    copyToClipboard(text) {
      navigator.clipboard.writeText(text)
        .then(() => {
          // You could add a toast notification here if you have one
          console.log('Copied to clipboard:', text);
        })
        .catch(err => {
          console.error('Failed to copy text: ', err);
        });
    },

    // Format currency with commas and put negative figures in brackets (standardized from master)
    formatCurrency(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0.00';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

    // Format numbers with commas (no decimal places for counts) and put negative figures in brackets
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '0';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },
  }
}
</script>

<style scoped>
/* Badge container to ensure consistent alignment (standardized from master) */
.badge-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px;
}

.badge-container.justify-start {
  justify-content: flex-start;
}

.badge-container.justify-end {
  justify-content: flex-end;
}

/* Base badge style (standardized from master) */
.badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.75rem;
  min-width: 100px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  letter-spacing: 0.3px;
}

.badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

/* Transaction Type Badge (standardized from master) */
.transaction-type-badge {
  min-width: 90px;
  color: white;
}

/* Transaction type styles */
.type-credit {
  background-color: #2ecc71; /* Green */
}

.type-debit {
  background-color: #e74c3c; /* Red */
}

.type-refund {
  background-color: #9b59b6; /* Purple */
}

.type-voided {
  background-color: #95a5a6; /* Gray */
}

.type-default {
  background-color: #95a5a6; /* Gray */
}

/* Mobile/Phone badges (standardized from master) */
.msisdn-badge, .mobile-badge, .phone-badge {
  background-color: #e9ecef;
  color: #495057;
  font-family: monospace;
  min-width: 120px;
}

/* Amount Badge */
.amount-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  background-color: #e3f2fd;
  color: #0d47a1;
  font-weight: 700;
  font-size: 0.75rem;
  min-width: 120px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Description Badge */
.description-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  background-color: #f5f5f5;
  color: #616161;
  font-size: 0.75rem;
  min-width: 150px;
  text-align: left;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Source Badge */
.source-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  background-color: #f3e5f5;
  color: #6a1b9a;
  font-size: 0.75rem;
  min-width: 100px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Date Badge */
.date-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  background-color: #e8eaf6;
  color: #283593;
  font-size: 0.7rem;
  min-width: 180px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
