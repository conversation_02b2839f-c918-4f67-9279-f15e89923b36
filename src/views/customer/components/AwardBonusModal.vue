<template>
  <div v-if="show" class="fixed inset-0 bg-gray-800 bg-opacity-75 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-xl shadow-2xl w-full max-w-2xl overflow-hidden">
      <!-- Header -->
      <div class="bg-gradient-to-r from-indigo-600 to-blue-600 px-6 py-4 flex justify-between items-center">
        <h2 class="text-lg font-bold text-white">Award Promotion</h2>
        <button @click="$emit('close')" class="text-white hover:text-gray-200 focus:outline-none">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="p-6">
        <!-- Customer Info -->
        <div v-if="customer" class="mb-6 bg-gray-50 p-4 rounded-lg">
          <div class="flex items-center space-x-3">
            <div class="bg-indigo-100 p-2 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-gray-800">{{ customer.name || 'Customer' }}</h3>
              <p class="text-sm text-gray-600">{{ customer.msisdn }} ({{ customer.acc_number }})</p>
            </div>
          </div>
        </div>

        <!-- Form -->
        <form @submit.prevent="submitForm">

          <!-- Promotion Selection -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Select Promotion</label>
            <div class="relative">
              <select
                v-model="selectedPromotion"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                required
              >
                <option value="" disabled>Select a promotion</option>
                <option
                  v-for="promo in filteredPromotions"
                  :key="promo.promo_id"
                  :value="promo.promo_setting_id"
                >
                  {{ promo.promo_name }} - {{ promo.component }} ({{ promo.bonus_amount }}) - {{ promo.expiry_period_in_hours }}h
                </option>
              </select>
            </div>
            <p class="mt-1 text-xs text-gray-500">
              Select the promotion to award to this customer
            </p>
          </div>

          <!-- Amount -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Amount</label>
            <input
              type="text"
              v-model="amount"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Bonus amount"
              required
            />
            <p class="mt-1 text-xs text-gray-500">
              Amount is automatically set from the selected promotion
            </p>
          </div>

          <!-- Expiry Period -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Expiry Period</label>
            <input
              type="number"
              v-model="expiryPeriod"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Enter expiry period in hours"
              required
              min="1"
              step="1"
            />
            <p class="mt-1 text-xs text-gray-500">
              Expiry period in hours (prefilled from selected promotion)
            </p>
          </div>

          <!-- Reason -->
          <!-- <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Reason</label>
            <textarea
              v-model="reason"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Enter reason for awarding"
              rows="3"
              required
            ></textarea>
            <p class="mt-1 text-xs text-gray-500">
              Provide a reason for awarding this promotion
            </p>
          </div> -->

          <!-- Selected Promotion Details -->
          <div v-if="selectedPromoDetails" class="mb-6 bg-gray-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-700 mb-2">Selected Promotion Details</h4>
            <div class="grid grid-cols-2 gap-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600">Promotion:</span>
                <span class="font-medium">{{ selectedPromoDetails.promo_name }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Setting ID:</span>
                <span class="font-medium">{{ selectedPromoDetails.promo_setting_id }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Promo ID:</span>
                <span class="font-medium">{{ selectedPromoDetails.promo_id }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Type:</span>
                <span class="font-medium">
                  {{ selectedPromoDetails.promo_type }}
                  <span class="ml-1 px-1.5 py-0.5 text-xs font-bold rounded-full"
                    :class="{
                      'bg-blue-100 text-blue-800': selectedPromoDetails.component === 'SPORTS',
                      'bg-purple-100 text-purple-800': selectedPromoDetails.component === 'DEPOSIT',
                      'bg-yellow-100 text-yellow-800': selectedPromoDetails.component === 'CASINO',
                      'bg-gray-100 text-gray-800': !['SPORTS', 'DEPOSIT', 'CASINO'].includes(selectedPromoDetails.component)
                    }">
                    {{ selectedPromoDetails.component }}
                  </span>
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Amount:</span>
                <span class="font-medium">{{ selectedPromoDetails.bonus_amount }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Min Odds:</span>
                <span class="font-medium">{{ selectedPromoDetails.min_odds }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Min Stake:</span>
                <span class="font-medium">{{ selectedPromoDetails.min_stake }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Expiry:</span>
                <span class="font-medium">{{ selectedPromoDetails.expiry_period_in_hours }} hours</span>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end space-x-3">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 bg-indigo-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none"
              :disabled="isLoading"
            >
              <span v-if="isLoading">Processing...</span>
              <span v-else>Award Promotion</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex';

export default {
  name: 'AwardBonusModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    customer: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      isLoading: false,
      selectedPromotion: '',
      amount: '',
      reason: '',
      expiryPeriod: 24, // Default 24 hours
      promotions: []
    };
  },
  computed: {
    filteredPromotions() {
      // Show all promotions without filtering by type
      return this.promotions;
    },
    selectedPromoDetails() {
      if (!this.selectedPromotion) return null;
      console.log("this.selectedPromotion: " + this.selectedPromotion)
      console.log("this.promotions: " +
       JSON.stringify(this.promotions.find(promo => promo.promo_setting_id === this.selectedPromotion)))
      return this.promotions.find(promo => promo.promo_setting_id === this.selectedPromotion);
    }
  },
  methods: {
    ...mapActions(['getPromotions', 'awardCustomerBonusFreeBet']),

    async fetchPromotions() {
      this.isLoading = true;

      try {
        const payload = {
          timestamp: Date.now(),
          limit: '100',
          skipCache: '1',
          status: '1' // Only active promotions
        };

        const response = await this.getPromotions(payload);

        if (response.status === 200) {
          this.promotions = response.message.result;
        } else {
          this.$swal.fire('Error', 'Failed to load promotions', 'error');
        }
      } catch (error) {
        console.error('Error fetching promotions:', error);
        this.$swal.fire('Error', 'Failed to load promotions', 'error');
      } finally {
        this.isLoading = false;
      }
    },

    async submitForm() {
      if (!this.selectedPromotion) {
        this.$swal.fire('Error', 'Please select a promotion', 'error');
        return;
      }

      if (!this.expiryPeriod || this.expiryPeriod <= 0) {
        this.$swal.fire('Error', 'Please enter a valid expiry period', 'error');
        return;
      }

      if (!this.amount) {
        this.$swal.fire('Error', 'Amount is required', 'error');
        return;
      }

      this.isLoading = true;

      try {
        // Determine the type based on the selected promotion's component and promo_type
        // const promoType = this.selectedPromoDetails.component === 'SPORTS' &&
        //                   this.selectedPromoDetails.promo_type.toLowerCase().includes('TRIVIA_FREEBET') ? 'freebet' : 'bonus';

        console.log("promoType: " + this.selectedPromoDetails.promo_type)
        const promoType = this.selectedPromoDetails.promo_type =="Trivia Freebet" ? 'freebet' : 'bonus';

        // Create payload with required fields from the selected item
        const payload = {
          profile_id: this.customer.id,
          promo_id: this.selectedPromotion, // Using promo_setting_id as promo_id
          amount: this.amount, // Using the amount from the input field
          ip_address: '', // This will be determined by the backend
          type: promoType,
          expiry_period: this.expiryPeriod,
          timestamp: Date.now()
        };

        console.log("payload: " + JSON.stringify(payload))

        // return;

        // Choose the action based on the determined type
        const action = this.awardCustomerBonusFreeBet;
        const response = await action(payload);

        if (response.status === 200) {
          this.$swal.fire(
            'Success',
            `Promotion awarded successfully`,
            'success'
          );
          this.$emit('close');
          this.$emit('awarded');
        } else {
          this.$swal.fire('Error', response.message || 'Failed to award', 'error');
        }
      } catch (error) {
        console.error('Error awarding:', error);
        this.$swal.fire('Error', 'An unexpected error occurred', 'error');
      } finally {
        this.isLoading = false;
      }
    }
  },
  mounted() {
    this.fetchPromotions();
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.fetchPromotions();
        this.selectedPromotion = '';
        this.amount = ''; // Reset amount
        this.reason = '';
        this.expiryPeriod = 24; // Reset to default 24 hours
      }
    },
    selectedPromotion(newVal) {
      // When a promotion is selected, update the expiry period and amount
      if (newVal && this.selectedPromoDetails) {
        // Set expiry period from the selected promotion
        this.expiryPeriod = this.selectedPromoDetails.expiry_period_in_hours || 24;

        // Set amount from the selected promotion
        this.amount = this.selectedPromoDetails.bonus_amount || '';
      } else {
        // Reset amount if no promotion is selected
        this.amount = '';
      }
    }
  }
};
</script>
