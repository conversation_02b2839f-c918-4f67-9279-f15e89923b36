<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <page-header pageName="Customers" pageSubtitle="Old" />

    <!-- Search Filters Panel -->
    <div class="bg-white rounded-lg shadow-lg mx-3 mb-4">
      <div class="p-4 border-b">
          <h3 class="text-lg font-medium text-gray-700">Filters</h3>
        </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
        <!-- Customer Identification Group -->
        <div class="filter-card p-2 border rounded-md hover:border-indigo-300 transition-colors">
          <h4 class="text-sm font-medium text-gray-700 mb-1">Customer Identification</h4>
          <div class="space-y-2">
            <div>
              <label class="block text-xs font-bold text-gray-700 mb-1">Mobile Number</label>
              <input type="text" v-model="payload.mobile_number" placeholder="Enter mobile number" 
                     class="w-full px-3 py-2 text-xs border rounded-md focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                     @keyup.enter="applyFilters()">
            </div>
          </div>
        </div>

      </div>
      
      <!-- Balance Filters -->
      <div class="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
       
       
      </div>
    </div>

    <div class="block py-4 bg-white overflow-x-auto">
      <auto-table
        :headers="tableHeaders"
        :data="profiles"
        :loading="isLoading"
        :total-items="total"
        :items-per-page="limit"
        :current-page-prop="offset"
        :server-side-pagination="true"
        :pagination="total > limit"
        :show-items-count="true"
        @page-change="gotToPage"
      >
        <!-- Account Number Column -->
        <template #acc_number="{ item }">
          <div class="font-bold">{{ item.acc_number }}</div>
        </template>

        <!-- Mobile Column -->
        <template #msisdn="{ item }">
          <div>
            <span class="font-bold">{{ item.msisdn }}</span>
            <div class="text-xs text-green-600">{{ item.network }}</div>
          </div>
        </template>

        <!-- Balance Column -->
        <template #balance="{ item }">
          <div>
            <div class="font-bold">KES {{ parseFloat(item.balance).toFixed(2) }}</div>
            <div class="text-xs text-green-600">Bonus: KES {{ parseFloat(item.bonus).toFixed(2) }}</div>
          </div>
        </template>

        <!-- Status Column -->
        <template #status="{ item }">
          <div class="text-center">
            <span 
              class="inline-block px-3 py-1 rounded-md text-white"
              :class="{
                'bg-green-500': parseInt(item.status) === 0,
                'bg-red-500': parseInt(item.status) !== 0,
              }"
            >
              {{ parseInt(item.status) === 0 ? 'Moved' :  'Not Moved' }}
            </span>
          </div>
        </template>

    </auto-table>

  </div>
  </div>
</template>

<script>
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import {mapActions} from "vuex";
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
import $ from "jquery";
import {AutoTable, ActionDropdown, ActionItem, CustomLoading} from '@/components/common';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  components: {
    AutoTable,
    ActionDropdown,
    ActionItem,
    CustomLoading,
    PageHeader,
    VueDatePicker,
  },
  data() {
    return {
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",
      //
      openModal: false,
      customer: null,
      isLoading: false,
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      payload: {
        status: "",
        mobile_number: "",
        acc_number: "",
        network: "",
        min_balance: "",
        max_balance: "",
        start: "",
        end: "",
        page: 1,
        limit: 100,
        skip_cache: "",
        timestamp: "",
        sort: "",
        product_id: "",
        group_id: "",
      },
      profiles: [],
      moment: moment,
      showDropdown: [],
      //
      date: null,
      dateFormat: 'MMM DD, YYYY',
      showAdvancedFilters: false,
      //

      //
      products: [],
      moreParams: {
        start: '',
        end: '',
        page: '1',
        limit: "100",
        timestamp: 'timestamp',
        skip_cache: '',
        product_status: '',
        product_name: '',
        product_id: '',
        export: '',
      },

      //cust
      form: {
        timestamp: "",
        msisdn: "",
        customer_name: "",
        email: "",
        id_type: "",
        national_id: "",
        group_id: "",
        product_id: "",
      },
      showDetailsModal: false,
      selectedCustomer: null,
      tableHeaders: [
        { key: 'msisdn', label: 'Mobile',align: 'left' },
        { key: 'balance', label: 'Balances',align: 'left' },
        { key: 'status', label: 'Status',align: 'center' },
      ],
    }
  },
  //
  async mounted() {
    await this.setProfiles()
  },

  methods: {
    ...mapActions(["getProfilesOld", "toggleSideMenu",]),
    //
    toggleSideM() {
      this.toggleSideMenu()
    },

    goBack() {
      this.$router.go(-1); // Navigates to the previous page
    },

    //
    async setProfiles() {
      let app = this
      app.isLoading = true
      app.payload.timestamp = Date.now()

      let response = await this.getProfilesOld(app.payload)
      // console.log("Profiles: " + JSON.stringify(response))
      if (response.status === 200) {
        app.profiles = response.message.result
        this.total = parseInt(response.message.record_count)

        app.showDropdown = []
        for (let i = 0; i < app.profiles.length; i++) {
          app.showDropdown.push(false)
        }
      } else {
        app.profiles = []
        app.total = 0
      }

      app.isLoading = false
    },

    // Apply filters
    async applyFilters() {
      this.offset = 1
      this.payload.page = 1
      await this.setProfiles()
    },

    // Reset filters
    resetFilters() {
      this.payload = {
        status: "",
        mobile_number: "",
        acc_number: "",
        network: "",
        min_balance: "",
        max_balance: "",
        start: "",
        end: "",
        page: 1,
        limit: 10,
        skip_cache: "",
        timestamp: "",
        sort: "",
        product_id: "",
        group_id: "",
      }
      this.date = null
      this.offset = 1
      this.setProfiles()
    },

    //
    async addToCustomer() {
      let app = this
      app.form.timestamp = Date.now()
      // console.log("form", JSON.stringify(app.form))

      const payload = app.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this adds Product to Customer!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, Add!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          app.loading = true
          $('#addMember').html(' Please Wait ...');
          return await this.addProfile(payload)

        },
      })
          .then(async (result) => {
            $('#addMember').html('Add');
            app.loading = false
            if (result.value.status === 200) {
              app.$swal.fire({
                title: 'Added!',
                text: result.value.message,
                icon: 'success'
              }).then(async (result) => {
                await app.$router.push({name: 'roles'})
              })

            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    //
    gotToPage(page) {
      let vm = this
      vm.payload.page = page
      vm.offset = page
      vm.setProfiles()
    },

    //
    async selectDate() {
      if (this.date) {
        this.payload.start = this.formatDate(this.date[0])
        this.payload.end = this.formatDate(this.date[1])
      } else {
        this.payload.start = ''
        this.payload.end = ''
      }
    },

    //
    formatDate(date) {
      let d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    //
    async viewReferrals(row) {
      this.closeDropdown()
      await this.$router.push({name: 'referrals', query: {msisdn: row.msisdn}})
    },

    //
    toggleSearchDropdown() {
      // Toggle the value of searchDropdown
      this.searchDropdown = !this.searchDropdown;
    },

    //
    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },
    viewCustomerDetails(customer) {
      this.selectedCustomer = customer;
      this.showDetailsModal = true;
      this.closeDropdown();
    },
    getStatusText(status) {
      const statusMap = {
        '1': 'Active',
        '3': 'Deactivated',
        '0': 'Inactive'
      };
      return statusMap[status] || 'Unknown';
    },
    getLoginStatusText(status) {
      const statusMap = {
        '1': 'Active',
        '2': 'Pending Verification',
        '3': 'Locked',
        '4': 'Suspended',
        '5': 'Deactivated',
        '6': 'New'
      };
      return statusMap[status] || 'Unknown';
    }
  }
}
</script>

<style scoped>
/* Add any additional styles here */
.modal {
  transition: opacity 0.25s ease;
}

/* Ensure dropdowns are on top */
:deep(.action-dropdown) {
  z-index: 50 !important;
}

:deep(.action-dropdown .absolute) {
  z-index: 50 !important;
}


/* Date picker styles */
:deep(.dp__input) {
  padding: 0.25rem 2rem;
  font-size: 0.75rem;
  line-height: 1rem;
  border-radius: 0.375rem;
}

:deep(.dp__main) {
  font-size: 0.75rem;
}
</style>
