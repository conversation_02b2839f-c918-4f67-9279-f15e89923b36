<template>
  <div class="absolute top-0 bottom-0 left-0 right-0 overflow-auto bg-white">

    <div class="flex font-medium p-6 pb-1 border-b gap-4 mb-5">
      <div class="flex-shrink font-medium">
        <i v-if="!this.$store.state.isSideMenuOpen" class="fa fa-bars text-black text-lg" @click="toggleSideM"></i>
        <i v-else class="fa fa-close text-black text-lg" @click="toggleSideM"></i>
      </div>
      <div class="flex-grow font-medium  pb-1 ">Referrals</div>
    </div>


    <div class="block rounded-lg bg-white shadow-lg  px-6 py-3 mx-3  mb-4 border ">
      <loading v-model:active="isLoading" transition="fade" blur="10px" color="#B53737" :is-full-page="fullPage"/>
      <table class="w-full mb-4 table">
        <thead class="border-b-2 text-xs text-left">
        <tr class="table-row">
          <th class="py-2">Customer ID</th>
          <th class="py-2">Mobile</th>
          <th class="py-2">Referred By</th>
          <th class="py-2">Date</th>
          <!--          <th class="py-2 text-center">Status</th>
                    <th class="py-2 text-center">Action</th>-->
        </tr>
        </thead>
        <tbody class="text-xs text-gray-600 divide-y">
        <tr v-for="(item,index) in referrals" :key="item.id">

          <td class="py-2">{{ item.cust_id }}</td>

          <td class="py-2 ">{{ item.msisdn }}</td>

          <td class="py-2">{{ item.referred_by }}</td>
          <td class="py-2">
            <span> {{ moment(item.created).format('lll') }}</span>
          </td>

          <!--
                    <td class="py-2 text-center">
                      <button v-if="parseInt(item.status) === 1" class="inline-block px-3 py-1 rounded-md text-white"
                              style="background-color: green">
                        Active
                      </button>
                      <button v-else-if="parseInt(item.status) === 3"
                              class="inline-block px-3 py-1 rounded-md text-white"
                              style="background-color: red">
                        DeActivated
                      </button>
                      <button v-else
                              class="inline-block px-3 py-1 rounded-md text-white"
                              style="background-color: purple">
                        Inactive
                      </button>
                    </td>

                    <td class="py-2 text-center relative w-24">
                      <div class="relative inline-block">
                        <button class="px-3 py-1 flex items-center space-x-1"
                                @click="toggleDropdown(index)"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                               stroke="#000000" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                  d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                          </svg>
                        </button>

                        <div v-if="showDropdown[index]" class="absolute right-0 mt-2 bg-white border rounded-md shadow-lg z-10"
                             style="width: 250px; text-align: left;">
                          <ul class="py-2 ">
                            <li @click="addProduct(item)">
                              <a class="block px-3 py-2 cursor-pointer hover:bg-gray-200">Add Product</a>
                            </li>

                            <li v-if="parseInt(item.status) === 1"
                                @click.prevent="deActivate(item)">
                              <a class="block px-3 py-2 cursor-pointer hover:bg-green-200"> DeActivate
                                <b>{{ item.first_name }}</b></a>
                            </li>

                            <li v-if="parseInt(item.status) !== 1"
                                @click.prevent="activate(item)">
                              <a class="block px-3 py-2 cursor-pointer hover:bg-green-200">Activate
                                <b>{{ item.first_name }}</b></a>
                            </li>

                          </ul>
                        </div>

                      </div>
                    </td>
          -->

        </tr>
        </tbody>
      </table>

      <!--Pagination-->
      <div class="flex w-full text-xs items-center" v-show="total>limit">
        <div class="flex-shrink" v-show="offset === 0">{{ offset + 1 }} to {{ limit }} of {{ total }}</div>
        <div class="flex-shrink" v-show="offset > 0">{{ ((offset * limit) - limit) + 1 }} to {{ limit * offset }} of
          {{ total }}
        </div>
        <div class="flex-grow text-right" v-show="total > limit">
          <div class="inline-block bg-white border rounded-md divide-x">
            <button class="p-2 px-3" v-show="Math.ceil(total/limit) > 1" @click="gotToPage(offset-1)">&larr;</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===1}" @click="gotToPage(1)">1</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1"
                    @click="gotToPage(2)">2
            </button>
            <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
            <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}"
                    v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="gotToPage(offset)">{{ offset }}
            </button>

            <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}"
                    v-show="Math.ceil(total/limit) > 4" @click="gotToPage(Math.ceil(total/limit))">
              {{ Math.ceil(total / limit) }}
            </button>
            <button class="p-2 px-3" v-show="(offset*limit) < total" @click="gotToPage(offset+1)">&rarr;</button>
          </div>
        </div>
      </div>

    </div>

  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import {mapActions} from "vuex";
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'

export default {
  data() {
    return {
      // search
      searchClient: "",
      searchDropdown: false,
      searchDropdownPlaceholder: "",
      clientName: "",
      //
      openModal: false,
      customer: null,
      isLoading: false,
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      payload: {
        status: "",
        mobile_number: "",
        start: "",
        end: "",
        page: 1,
        limit: 10,
        skip_cache: "",
        timestamp: "",
        sort: "",
      },
      referrals: [],
      moment: moment,
      showDropdown: [],
      //

      //
      products: [],
      moreParams: {
        start: '',
        end: '',
        page: '1',
        limit: "10",
        timestamp: 'timestamp',
        skip_cache: '',
        mobile_number: '',
        export: '',
      },

    }
  },
  components: {
    Loading,
    VueDatePicker,
  },
  //
  async mounted() {
    await this.setForm()
    await this.setReferrals()
  },

  methods: {
    ...mapActions(['getReferrals', "toggleSideMenu",]),
    toggleSideM() {
      this.toggleSideMenu()
    },
    //
    async setForm() {
      let app = this
      // get from router params
      app.payload.mobile_number = this.$route.query.msisdn ?? "";
      console.log("Form par: " + JSON.stringify(app.payload))
    },

    //
    capitalizeFirstLetter(value) {
      if (!value) return '';
      value = value.toLowerCase()
      return value.charAt(0).toUpperCase() + value.slice(1);
    },

    //
    gotToPage(page) {
      let vm = this
      vm.payload.offset = page
      vm.offset = page
      vm.setReferrals()
    },

    //
    async selectDate() {
      let vm = this
      this.payload.start = vm.formatDate(this.date[0])
      this.payload.end = vm.formatDate(this.date[1])

      await this.setReferrals()
    },

    formatDate(date) {
      let d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    async setReferrals() {
      let app = this
      app.isLoading = true
      app.payload.timestamp = Date.now()

      let response = await this.getReferrals(app.payload)
      if (response.status === 200) {
        app.referrals = response.message.result
        this.total = parseInt(response.message.record_count)

        app.showDropdown = []
        for (let i = 0; i < app.referrals.length; i++) {
          app.showDropdown.push(false)
        }
      } else {
        app.referrals = []
        app.total = 0
      }

      app.isLoading = false
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },
    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },
  }
}
</script>
