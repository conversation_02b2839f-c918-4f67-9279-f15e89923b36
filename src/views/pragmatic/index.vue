<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <PageHeader title="Pragmatic Operations" />

    <!-- Action Cards Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
      
      <!-- Cancel Bonus -->
      <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
        <h3 class="text-lg font-semibold text-gray-800 mb-3">Cancel Bonus</h3>
        <div class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Bonus Code</label>
            <input 
              v-model="forms.cancel.bonusCode" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., SUMMER-2024"
            />
          </div>
          <button 
            @click="executeAction('cancel')"
            :disabled="loading || !forms.cancel.bonusCode"
            class="w-full px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <vue-loaders-ball-beat color="white" scale="0.5" v-show="loading && currentAction === 'cancel'"></vue-loaders-ball-beat>
            <span v-show="!(loading && currentAction === 'cancel')">Cancel Bonus</span>
          </button>
        </div>
      </div>

      <!-- Get Player FRB -->
      <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
        <h3 class="text-lg font-semibold text-gray-800 mb-3">Get Player FRB</h3>
        <div class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Player ID</label>
            <input 
              v-model="forms.getplayerfrb.playerId" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., player12345"
            />
          </div>
          <button 
            @click="executeAction('getplayerfrb')"
            :disabled="loading || !forms.getplayerfrb.playerId"
            class="w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <vue-loaders-ball-beat color="white" scale="0.5" v-show="loading && currentAction === 'getplayerfrb'"></vue-loaders-ball-beat>
            <span v-show="!(loading && currentAction === 'getplayerfrb')">Get Player FRB</span>
          </button>
        </div>
      </div>

      <!-- Add Players -->
      <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
        <h3 class="text-lg font-semibold text-gray-800 mb-3">Add Players</h3>
        <div class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Bonus Code</label>
            <input 
              v-model="forms.addplayers.bonusCode" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., SUMMER-2024"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Player List (comma separated)</label>
            <textarea 
              v-model="playerListText" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="player123, player456, player789"
              rows="3"
            ></textarea>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Request ID</label>
            <input 
              v-model="forms.addplayers.requestId" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., req-12345"
            />
          </div>
          <button 
            @click="executeAction('addplayers')"
            :disabled="loading || !forms.addplayers.bonusCode || !playerListText || !forms.addplayers.requestId"
            class="w-full px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <vue-loaders-ball-beat color="white" scale="0.5" v-show="loading && currentAction === 'addplayers'"></vue-loaders-ball-beat>
            <span v-show="!(loading && currentAction === 'addplayers')">Add Players</span>
          </button>
        </div>
      </div>

      <!-- Add Player -->
      <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
        <h3 class="text-lg font-semibold text-gray-800 mb-3">Add Player</h3>
        <div class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Bonus Code</label>
            <input 
              v-model="forms.addplayer.bonusCode" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., PERSONAL-123"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Player ID</label>
            <input 
              v-model="forms.addplayer.playerId" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., player123"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Rounds</label>
            <input 
              v-model.number="forms.addplayer.rounds" 
              type="number" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="10"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Expiration Date</label>
            <input 
              v-model="forms.addplayer.expirationDate" 
              type="datetime-local" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <button 
            @click="executeAction('addplayer')"
            :disabled="loading || !forms.addplayer.bonusCode || !forms.addplayer.playerId || !forms.addplayer.rounds || !forms.addplayer.expirationDate"
            class="w-full px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <vue-loaders-ball-beat color="white" scale="0.5" v-show="loading && currentAction === 'addplayer'"></vue-loaders-ball-beat>
            <span v-show="!(loading && currentAction === 'addplayer')">Add Player</span>
          </button>
        </div>
      </div>

      <!-- Create Player Spin -->
      <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
        <h3 class="text-lg font-semibold text-gray-800 mb-3">Create Player Spin</h3>
        <div class="space-y-3">
          <div class="grid grid-cols-2 gap-2">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Bonus Code</label>
              <input 
                v-model="forms.creatplayerspin.bonusCode" 
                type="text" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="PERSONAL-456"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Player ID</label>
              <input 
                v-model="forms.creatplayerspin.playerId" 
                type="text" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="player456"
              />
            </div>
          </div>
          <div class="grid grid-cols-2 gap-2">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Currency</label>
              <input 
                v-model="forms.creatplayerspin.currency" 
                type="text" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="USD"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Rounds</label>
              <input 
                v-model.number="forms.creatplayerspin.rounds" 
                type="number" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="15"
              />
            </div>
          </div>
          <div class="grid grid-cols-2 gap-2">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
              <input 
                v-model="forms.creatplayerspin.startDate" 
                type="datetime-local" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Expiration Date</label>
              <input 
                v-model="forms.creatplayerspin.expirationDate" 
                type="datetime-local" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Request ID</label>
            <input 
              v-model="forms.creatplayerspin.requestId" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="req-67890"
            />
          </div>
          <button 
            @click="executeAction('creatplayerspin')"
            :disabled="loading || !isCreatePlayerSpinValid"
            class="w-full px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <vue-loaders-ball-beat color="white" scale="0.5" v-show="loading && currentAction === 'creatplayerspin'"></vue-loaders-ball-beat>
            <span v-show="!(loading && currentAction === 'creatplayerspin')">Create Player Spin</span>
          </button>
        </div>
      </div>

      <!-- Get Bet Scales -->
      <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
        <h3 class="text-lg font-semibold text-gray-800 mb-3">Get Bet Scales</h3>
        <div class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Game IDs (comma separated)</label>
            <textarea 
              v-model="gameIdsText" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="vs20wolfgold, vs25starburst"
              rows="2"
            ></textarea>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Currencies (comma separated)</label>
            <input 
              v-model="currenciesText" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="KES, USD"
            />
          </div>
          <button 
            @click="executeAction('getbetscales')"
            :disabled="loading || !gameIdsText || !currenciesText"
            class="w-full px-4 py-2 bg-yellow-600 text-white text-sm font-medium rounded-md hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <vue-loaders-ball-beat color="white" scale="0.5" v-show="loading && currentAction === 'getbetscales'"></vue-loaders-ball-beat>
            <span v-show="!(loading && currentAction === 'getbetscales')">Get Bet Scales</span>
          </button>
        </div>
      </div>

    </div>

    <!-- Response Display -->
    <div v-if="lastResponse" class="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-4">
      <h3 class="text-lg font-semibold text-gray-800 mb-3">Last Response</h3>
      <div class="bg-white border rounded p-3">
        <pre class="text-sm text-gray-700 whitespace-pre-wrap">{{ JSON.stringify(lastResponse, null, 2) }}</pre>
      </div>
    </div>

  </div>
</template>

<script>
import { mapActions } from 'vuex';
import PageHeader from '@/components/common/PageHeader.vue';

export default {
  name: 'PragmaticOperations',
  components: {
    PageHeader
  },
  data() {
    return {
      loading: false,
      currentAction: null,
      lastResponse: null,
      playerListText: '',
      gameIdsText: '',
      currenciesText: '',
      forms: {
        cancel: {
          bonusCode: ''
        },
        getplayerfrb: {
          playerId: ''
        },
        addplayers: {
          bonusCode: '',
          requestId: ''
        },
        addplayer: {
          bonusCode: '',
          playerId: '',
          rounds: null,
          expirationDate: ''
        },
        creatplayerspin: {
          bonusCode: '',
          playerId: '',
          currency: '',
          startDate: '',
          expirationDate: '',
          rounds: null,
          requestId: ''
        }
      }
    };
  },
  computed: {
    isCreatePlayerSpinValid() {
      const form = this.forms.creatplayerspin;
      return form.bonusCode && form.playerId && form.currency && 
             form.startDate && form.expirationDate && form.rounds && form.requestId;
    }
  },
  methods: {
    ...mapActions(['pragmaticAction']),
    
    async executeAction(actionType) {
      this.loading = true;
      this.currentAction = actionType;
      
      let payload = { type: actionType };
      
      try {
        switch (actionType) {
          case 'cancel':
            payload = {
              type: 'cancel',
              bonusCode: this.forms.cancel.bonusCode
            };
            break;
            
          case 'getplayerfrb':
            payload = {
              type: 'getplayerfrb',
              playerId: this.forms.getplayerfrb.playerId
            };
            break;
            
          case 'addplayers':
            payload = {
              type: 'addplayers',
              bonusCode: this.forms.addplayers.bonusCode,
              playerList: this.playerListText.split(',').map(p => p.trim()).filter(p => p),
              requestId: this.forms.addplayers.requestId
            };
            break;
            
          case 'addplayer':
            payload = {
              type: 'addplayer',
              bonusCode: this.forms.addplayer.bonusCode,
              playerId: this.forms.addplayer.playerId,
              rounds: this.forms.addplayer.rounds,
              expirationDate: this.formatDateTime(this.forms.addplayer.expirationDate)
            };
            break;
            
          case 'creatplayerspin':
            payload = {
              type: 'creatplayerspin',
              bonusCode: this.forms.creatplayerspin.bonusCode,
              playerId: this.forms.creatplayerspin.playerId,
              currency: this.forms.creatplayerspin.currency,
              startDate: this.formatDateTime(this.forms.creatplayerspin.startDate),
              expirationDate: this.formatDateTime(this.forms.creatplayerspin.expirationDate),
              gameList: [
                {
                  gameId: "vs25starburst",
                  betValues: [
                    { betPerLine: 0.25, currency: this.forms.creatplayerspin.currency }
                  ]
                }
              ],
              rounds: this.forms.creatplayerspin.rounds,
              requestId: this.forms.creatplayerspin.requestId
            };
            break;
            
          case 'getbetscales':
            payload = {
              type: 'getbetscales',
              gameIds: this.gameIdsText.split(',').map(g => g.trim()).filter(g => g),
              currencies: this.currenciesText.split(',').map(c => c.trim()).filter(c => c)
            };
            break;
        }
        
        const response = await this.pragmaticAction(payload);
        this.lastResponse = response;
        
        if (response.status === 200) {
          this.$swal.fire({
            title: 'Success!',
            text: 'Operation completed successfully',
            icon: 'success',
            timer: 2000,
            showConfirmButton: false
          });
        } else {
          this.$swal.fire({
            title: 'Error',
            text: response.message?.message || 'Operation failed',
            icon: 'error'
          });
        }
        
      } catch (error) {
        console.error('Error executing action:', error);
        this.$swal.fire({
          title: 'Error',
          text: 'An unexpected error occurred',
          icon: 'error'
        });
      } finally {
        this.loading = false;
        this.currentAction = null;
      }
    },
    
    formatDateTime(dateTimeLocal) {
      if (!dateTimeLocal) return '';
      // Convert from datetime-local format to "YYYY-MM-DD HH:mm:ss"
      return dateTimeLocal.replace('T', ' ') + ':00';
    }
  }
};
</script>
