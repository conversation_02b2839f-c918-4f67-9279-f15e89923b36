import { createStore } from 'vuex'
import createPersistedState from "vuex-persistedstate"
import axios from "axios";
import router from "@/router";
import md5 from "md5";
import tournaments from "@/views/sports_book/tournaments.vue";

// Add this right after the imports and before the state definition
const getEnvConfig = () => {
    const host = window.location.hostname;
  ////  console.log("Current hostname:", host);

    if (host.includes('localhost') || host.includes('127.0.0.1')) {
        const config = {
            api: import.meta.env.VITE_DEV_BASE_API || "https://dash.api.dev.mossbets.bet/",
            authKey: import.meta.env.VITE_DEV_AUTH_KEY || "4ia2yg.Gs42I0h7(%!{FjDAt{.R5Z-",
            appKey: import.meta.env.VITE_DEV_APP_KEY || "QGohdFLe3w-AWjfdy7jXrZvtIMUrmPwpocQtCsRPZsF-QIuw7AKAw4",
        };
      ////  console.log("Using DEV config:", config);
        return config;
    } else if (host.includes('staging') || host.includes("dev")) {
        const config = {
            api: import.meta.env.VITE_DEV_BASE_API || "https://dash.api.dev.mossbets.bet/",
            authKey: import.meta.env.VITE_DEV_AUTH_KEY || "4ia2yg.Gs42I0h7(%!{FjDAt{.R5Z-",
            appKey: import.meta.env.VITE_DEV_APP_KEY || "QGohdFLe3w-AWjfdy7jXrZvtIMUrmPwpocQtCsRPZsF-QIuw7AKAw4",
        };
      ////  console.log("Using STAGING config:", config);
        return config;
    }
    const config = {
        api: import.meta.env.VITE_PROD_BASE_API || "https://dash.api.prod.mossbets.bet/",
        authKey: import.meta.env.VITE_PROD_AUTH_KEY || "W&RwQ}#ckljoR;No!yH]aa6Pg{X3W5",
        appKey: import.meta.env.VITE_PROD_APP_KEY || "zd1CbIsDb8-QW1DMH7aV2LIQoMxwQ3YyLCNGt9fdLJP-0UeibHh967",
    };
  ////  console.log("Using PROD config:", config);
    return config;
};


const state = {
    //
    isSideMenuOpen: true,
    // ...getEnvConfig(), // This will spread the appropriate config

    // api: import.meta.env.VITE_DEV_BASE_API || "https://dash.api.dev.mossbets.bet/",
    // authKey: import.meta.env.VITE_DEV_AUTH_KEY || "4ia2yg.Gs42I0h7(%!{FjDAt{.R5Z-",
    // appKey: import.meta.env.VITE_DEV_APP_KEY || "QGohdFLe3w-AWjfdy7jXrZvtIMUrmPwpocQtCsRPZsF-QIuw7AKAw4",
    //
    api: import.meta.env.VITE_PROD_BASE_API || "https://dash.api.prod.mossbets.bet/",
    authKey: import.meta.env.VITE_PROD_AUTH_KEY || "W&RwQ}#ckljoR;No!yH]aa6Pg{X3W5",
    appKey: import.meta.env.VITE_PROD_APP_KEY || "zd1CbIsDb8-QW1DMH7aV2LIQoMxwQ3YyLCNGt9fdLJP-0UeibHh967",
    authKeyWeb: "WEB_APP",
    tokenKey: null,
    hash_key: null,

    loggedUser: null,
    user_name: null,
    user: null,
    profile: null,
    promo: null,
    customer: null,
    bet: null,
    paybill: null,
    authChannel: null,
    roleData: null,
    betDetails: null,
    clients: [],
    client_id: null,
    message: null,
    pragmatic: null,

    permission: null,
    role: null,
    isSuperRole: false,
    super_roles: [1, 2, 8],
    permissions: [],
};

const getters = {
    //
    isSideMenuOpen: (state) => state.isSideMenuOpen,
    //
    isAuthenticated: state => !!state.tokenKey,
    StatePermissions: state => state.permissions,
    StateSuperRoles: state => state.super_roles,
    StateRole: state => state.role,
    StateApi: state => state.api,
    StateAppKey: state => state.appKey,
    StateTokenKey: state => state.tokenKey,
    StateAuthKey: state => state.authKey,
    StateHashKey: state => state.hash_key,
    StateAuthKeyWeb: state => state.authKeyWeb,
};
const actions = {

    toggleSideMenu({ commit }) {
        commit("TOGGLE_SIDE_MENU");
    },
    toggleSideMenuInit({ commit }) {
        commit("TOGGLE_SIDE_MENU_INIT");
    },

    //
    hasSuperRole({ getters, commit }) {
        let ans = getters.StateSuperRoles.includes(getters.StateRole);
        commit("setIsSuperRole", ans)
        return ans
    },

    //
    async LogOut({ commit }) {
        commit('setLogOut')
    },

    sortNestedArray(array) {
        array.sort();
        array.forEach((value, index) => {
            if (Array.isArray(value)) {
                array[index] = this.sortNestedArray(value);
            } else if (typeof value === 'object' && value !== null) {
                array[index] = this.sortNestedObject(value);
            } else {
                array[index] = value;
            }
        });
        return array;
    },

    sortNestedObject(obj) {
        const sortedObj = {};
        Object.keys(obj).sort().forEach(key => {
            const value = obj[key];
            if (Array.isArray(value)) {
                sortedObj[key] = this.sortNestedArray([...value]);
            } else if (typeof value === 'object' && value !== null) {
                sortedObj[key] = this.sortNestedObject(value);
            } else {
                sortedObj[key] = value;
            }
        });
        return sortedObj;
    },

    // Basic implementation of the md5 hash function
    md5(input) {
        const crypto = require('crypto');
        return crypto.createHash('md5').update(input).digest('hex');
    },

    async HashCreate({ commit, getters }, request) {
        // console.log(`HashCreate payload --- ${JSON.stringify(request)}`);

        let hashKey = "";
        let sortedKeys = Object.keys(request).sort();

        for (let key of sortedKeys) {
            let value = request[key];

            // console.log(`Main Obj: ${key}`);
            if (Array.isArray(value) || typeof value === 'object') {
                if (Array.isArray(value)) {
                    // console.log(`Array.isArray:`, value);
                    for (let i = 0; i < value.length; i++) {
                        let val = value[i];
                        // console.log(`Am here with index: ${i} with Val: ${val} of JSON: ${JSON.stringify(val)}`);
                        hashKey += `&${i}=${md5(JSON.stringify(val))}`;
                        // console.log(`And hashKey: ${hashKey}`);
                        // console.log(`And hashKey: ${hashKey}`);
                    }
                } else {
                    // console.log(`Object:Hash`);
                    for (let keyVal in value) {
                        let val = value[keyVal];
                        // console.log(`Am here with keyVal: ${keyVal} with Val: ${val} of JSON: ${JSON.stringify(val)}`);
                        hashKey += `&${keyVal}=${md5(JSON.stringify(val))}`;
                        // console.log(`And hashKey: ${hashKey}`);
                    }
                }
                continue;
            }

            hashKey += `&${key}=${value}`;
        }

        // let hash = md5(hashKey.substring(1) + '' + tokenKey);

        let hash = hashKey.slice(1)
        // let md5 = require('md5');
        let key = md5(hash + getters.StateAppKey)
        await commit('setHashKey', key)
        return hash;
    },

    ///----- Auth
    async LogIn({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        const url = getters.StateApi + 'user/v1/user_login';
        const headers = {
            'X-Authorization': getters.StateAuthKey,
            'X-App-Key': getters.StateAppKey,
            'X-Hash-Key': getters.StateHashKey,
        }
       console.log("BASE  dscs : ", JSON.stringify(headers))
        await axios.post(url, payload, {
            headers: headers
        })
            .then(async function (response) {
                // console.log("login success:: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data

                if (res.status === 200) {
                    // console.log("reseee:: " + JSON.stringify(res.message.data))
                    // if (res.message.data.token) {
                    //     await commit('setLoggedUser', res.message.data)
                    //     await commit('setAuth', res.message.data.token)
                    //     await commit('setRole', res.message.data.rid)
                    //     if (res.message.data.permissions) {
                    //         // Get Ids to an array
                    //         let permissions = res.message.data.permissions.map(item => item.id)
                    //         await commit('setPermissions', permissions)
                    //     }
                    // }
                }
            })
            .catch(async function (error) {
                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code
                }
            })

        return res
    },

    // Login with verification code
    async LogInCode({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        const url = getters.StateApi + 'user/v1/verify_login';
      ////  console.log("LOGIN VERIFY : ", url)
        await axios.post(url, payload, {
            headers: {
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Hash-Key': getters.StateHashKey,
            }
        })
            .then(async function (response) {
              ////  console.log("login verify success:: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data

                if (res.status === 200) {
                    if (res.message.data.token) {
                        await commit('setLoggedUser', res.message.data)
                        await commit('setAuth', res.message.data.token)
                        await commit('setRole', res.message.data.rid)
                        if (res.message.data.permissions) {
                            // Get Ids to an array
                            let permissions = res.message.data.permissions.map(item => item.id)
                            await commit('setPermissions', permissions)
                        }
                    }
                }
            })
            .catch(async function (error) {
                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code
                }
            })

        return res
    },

    //
    async passwordForgot({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'user/v1/reset_password', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(async function (error) {
                // console.log('error: ' + JSON.stringify(error.response.data))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    //
    async passwordReset({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'user/v1/change_password', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(async function (error) {
              ////  console.log('error: ' + JSON.stringify(error.response.data))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    //------------- Dash ---------------------------------------------//
    //
    async Stats({ commit, getters, dispatch }, payload) {
        let res = {}
      ////  console.log("Stats:", JSON.stringify(payload))
        dispatch('HashCreate', payload)
        let url = new URLSearchParams(payload).toString();
        await axios.get(getters.StateApi + 'ds/v1/view?' + url, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("Stats: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }

            })
            .catch(function (error) {
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    //
    async getDashSummaryReports({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        let url = new URLSearchParams(payload).toString();

        const headers= {
            'X-Hash-Key': getters.StateHashKey,
            'X-Authorization': getters.StateAuthKey,
            'X-App-Key': getters.StateAppKey,
            'X-Access': getters.StateTokenKey
        }
        console.log("Das kjsdknb: ", JSON.stringify(headers))

        await axios.get(getters.StateApi + 'ds/v1/summary?' + url, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("Stats: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }

            })
            .catch(function (error) {
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // getDashSummaryAverageReports
    async getDashSummaryAverageReports({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)

        const headers= {
            'X-Hash-Key': getters.StateHashKey,
            'X-Authorization': getters.StateAuthKey,
            'X-App-Key': getters.StateAppKey,
            'X-Access': getters.StateTokenKey
        }
        console.log("kjsdknb: ", JSON.stringify(headers))

        let params = new URLSearchParams(payload).toString();
        await axios.get(getters.StateApi + 'ds/v1/summary/averages/?' + params, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("Stats: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }

            })
            .catch(function (error) {
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // getGamesSummary
    async getGamesSummary({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        let params = new URLSearchParams(payload).toString();
        await axios.get(getters.StateApi + 'ds/v1/game/summary?' + params, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Profiles Old
    async getProfilesOld({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        const params = new URLSearchParams();

        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }

        await axios.get(getters.StateApi + 'customer/v1/profiles/old?' + params, {
            headers: {
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Hash-Key': getters.StateHashKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getProfiles: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Profiles Self Exclusion
    async getProfilesSelfExclusion({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        const params = new URLSearchParams();

        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }

        await axios.get(getters.StateApi + 'customer/v1/profiles/self_exclusion?' + params, {
            headers: {
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Hash-Key': getters.StateHashKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getProfiles: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Add Self Exclusion Customer
    async addSelfExclusionCustomer({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'customer/v1/profiles/self_exclusion/add', payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              ////  console.log("addSelfExclusionCustomer: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log("addSelfExclusionCustomer error: " + JSON.stringify(error))
                res.status = error.response.status
                if (res.status === 422)
                    res.message = error.response.data.statusDescription
                else if (res.status === 403)
                    res.message = []
                else if (res.status === 401)
                    res.message = []
                else
                    res.message = error.response.data.data.message

                if (res.status === 401 || res.status === 403 || res.status === 406) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Update Self Exclusion Customer
    async updateSelfExclusionCustomer({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'customer/v1/profiles/self_exclusion/update/' + payload.id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              ////  console.log("updateSelfExclusionCustomer: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log("updateSelfExclusionCustomer error: " + JSON.stringify(error))
                res.status = error.response.status
                if (res.status === 422)
                    res.message = error.response.data.statusDescription
                else if (res.status === 403)
                    res.message = []
                else if (res.status === 401)
                    res.message = []
                else
                    res.message = error.response.data.data.message

                if (res.status === 401 || res.status === 403 || res.status === 406) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Profiles
    async getProfiles({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        const params = new URLSearchParams();

        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }

        await axios.get(getters.StateApi + 'customer/v1/profiles?' + params, {
            headers: {
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Hash-Key': getters.StateHashKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getProfiles: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Add Profile
    async addProfile({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'adm/v1/group/profile_create', payload, {
            headers: {
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Hash-Key': getters.StateHashKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
              ////  console.log("success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Send OTP
    async resendCustomerOTP({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)

        let profile_id = payload.profile_id
        delete payload.profile_id

        await axios.post(getters.StateApi + 'customer/v1/resend_otp/' + profile_id, payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("resendCustomerOTP : " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(async function (error) {
              ////  console.log('error: ' + JSON.stringify(error.response.data))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Profiles
    async getReferrals({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        const params = new URLSearchParams();

        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }

        await axios.get(getters.StateApi + 'rpt/v1/list/referrals?' + params, {
            headers: {
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Hash-Key': getters.StateHashKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getProfiles: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Blocked IPs
    async getBlockedIPs({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        const params = new URLSearchParams();

        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }

        await axios.get(getters.StateApi + 'bets/v1/blocked_ips?' + params, {
            headers: {
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Hash-Key': getters.StateHashKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getBlockedIPs: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },
    // Update Blocked IP
    async updateBlockedIP({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'bets/v1/blocked_ips/update/' + payload.id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              ////  console.log("updateBlockedIP: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                }
                else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },


    async getBlockedIPProfiles({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        const params = new URLSearchParams();

        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }

        await axios.get(getters.StateApi + 'bets/v1/blocked_ips/profiles?' + params, {
            headers: {
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Hash-Key': getters.StateHashKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getBlockedIPs: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Greener Monday
    async getGreenerMonday({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        // query string 
        const param = new URLSearchParams(payload).toString()

        await axios.post(getters.StateApi + 'ds/v1/greener_monday?' + param, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getGreenerMonday: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Sports Bets Summary
    async getSportsBetsSummary({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        // query string 
        const param = new URLSearchParams(payload).toString()

        await axios.post(getters.StateApi + 'ds/v1/sports_bets_summary?' + param, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getSportsBetsSummary: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Virtual Bets Summary
    async getVirtualBetsSummary({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        // query string 
        const param = new URLSearchParams(payload).toString()

        await axios.post(getters.StateApi + 'ds/v1/virtual_bets_summary?' + param, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getVirtualBetsSummary: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Softgames Bet Summary
    async getSoftGamingBetsSummary({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        // query string 
        const param = new URLSearchParams(payload).toString()

        await axios.post(getters.StateApi + 'ds/v1/softgames_bets_summary?' + param, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getSoftGamingBetsSummary: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // ---- Games -------------------------------------------------//
    // Games
    async getGames({ commit, getters, dispatch }, params) {
        let res = {}
      ////  console.log("getGames: " + getters.StateApi + 'games/v1/list?' + params)
        dispatch('HashCreate', params)
        await axios.get(getters.StateApi + 'games/v1/list?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getGames: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Game Categories
    async getGameCategories({ commit, getters, dispatch }, params) {
        let res = {}
      ////  console.log("getGameCategories: " + getters.StateApi + 'games/v1/game_categories?' + params)
        dispatch('HashCreate', params)
        await axios.get(getters.StateApi + 'games/v1/game_categories?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getGameCategories: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Update Games
    async updateGames({ commit, getters, dispatch }, payload) {
        let res = {}
      ////  console.log("updateGames payload: " + JSON.stringify(payload))
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'games/v1/update_games', payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("updateGames: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Create Game Category
    async createGameCategory({ commit, getters, dispatch }, payload) {
        let res = {}
      ////  console.log("createGameCategory payload: " + JSON.stringify(payload))
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'games/v1/game_categories/create', payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Update Game Category
    async updateGameCategory({ commit, getters, dispatch }, payload) {
        let res = {}
      ////  console.log("updateGameCategory payload: " + JSON.stringify(payload))
        const categoryId = payload.id
        delete payload.id
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'games/v1/game_categories/edit/' + categoryId, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Add Games to Category
    async addGamesToCategory({ commit, getters, dispatch }, payload) {
        let res = {}
      ////  console.log("addGamesToCategory payload: " + JSON.stringify(payload))
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'games/v1/add_games_to_category', payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Update Game Status
    async updateGameStatus({ commit, getters, dispatch }, payload) {
        let res = {}
      ////  console.log("updateGameStatus payload: " + JSON.stringify(payload))
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'games/v1/update/'+payload.game_id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Update Individual Game
    async updateIndividualGame({ commit, getters, dispatch }, payload) {
        let res = {}
      ////  console.log("updateIndividualGame payload: " + JSON.stringify(payload))
        const gameId = payload.game_id
        delete payload.game_id
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'games/v1/list/update/' + gameId, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // ---- Fixtures -------------------------------------------------//
    // Fixtures ManualResult
    async getFixturesManualResult({ commit, getters }, params) {
        let res = {}
        await axios.get(getters.StateApi + 'games/v1/manual_results/?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getFixturesManualResult: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Update Fixture Scores
    async updateFixtureScores({ commit, getters }, payload) {
        let res = {}
        await axios.post(getters.StateApi + 'games/v1/update_scores/' + payload.match_id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("updateFixtureScores: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Fixtures ManualResult Archive
    async getArchieveManualResult({ commit, getters }, params) {
        let res = {}
        await axios.get(getters.StateApi + 'games/v1/manual_results/archive/?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getArchieveManualResult: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },
    // Update Fixture Scores Archive
    async updateFixtureScoresArchive({ commit, getters }, payload) {
        let res = {}
        await axios.post(getters.StateApi + 'games/v1/update_scores/archive/' + payload.match_id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("updateFixtureScores: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Get BetSlip Count
    async getBetSlipCount({ commit, getters }, params) {
        let res = {}
      ////  console.log("getBetSlipCount", JSON.stringify(params));
        await axios.post(getters.StateApi + 'games/v1/bet_slip_count?', params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getBetSlipCount: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Fixtures
    async getFixtures({ commit, getters }, params) {
        let res = {}
        await axios.get(getters.StateApi + 'games/v1/fixtures/?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getFixtures: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Update Fixture
    async updateFixture({ commit, getters }, payload) {
        let res = {}

        await axios.post(getters.StateApi + 'games/v1/edit/' + payload.match_id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("updateFixture: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },


    // Markets
    async getMarkets({ commit, getters }, params) {
        let res = {}
        await axios.get(getters.StateApi + 'games/v1/list/markets/?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getMarkets: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Update Market
    async updateMarket({ commit, getters }, payload) {
        let res = {}
        let market_id = ''
        market_id = payload.market_id
        delete payload.market_id

        await axios.post(getters.StateApi + 'games/v1/list/markets/edit/' + market_id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("updateTournament: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Odds Live
    async getOddsLive({ commit, getters }, params) {
        let res = {}
        await axios.get(getters.StateApi + 'games/v1/list/odds_live/?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getOddsLive: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Countries
    async getCountries({ commit, getters }, params) {
        let res = {}

        await axios.get(getters.StateApi + 'games/v1/list/countries/?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getBets: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Fixtures
    async getSports({ commit, getters }, params) {
        let res = {}

        await axios.get(getters.StateApi + 'games/v1/list/sports/?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getSports: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Tournaments
    async getTournaments({ commit, getters }, params) {
        let res = {}

        await axios.get(getters.StateApi + 'games/v1/list/tournaments?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getTournaments: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Update Tournaments
    async updateTournament({ commit, getters }, payload) {
        let res = {}
        let tournament_id = ''
        tournament_id = payload.tournament_id
        delete payload.tournament_id

        await axios.post(getters.StateApi + 'games/v1/list/tournaments/edit/' + tournament_id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("updateTournament: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Sports Bets
    async getSportsBets({ commit, getters }, payload) {
        let res = {}

        const params = new URLSearchParams();

        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }
        await axios.get(getters.StateApi + 'bets/v1/sports/?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getBets: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Bet Slips
    async getSportsBetSlips({ commit, getters }, params) {
        let res = {}

        await axios.get(getters.StateApi + 'bets/v1/bet_slip/?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getBets: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Resettle Bet
    async resettleBetSlip({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)

      ////  console.log("resettleBetSlip payload 3: ", JSON.stringify(payload))

        // Use the /resettle/{betId} endpoint
        await axios.post(getters.StateApi + 'bets/v1/resettle/' + payload.bet_id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("resettleBetSlip: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              ////  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Menu Highlights
    async getMenuHighlights({ commit, getters }, payload) {
        let res = {}

        const params = new URLSearchParams();

        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }

        await axios.get(getters.StateApi + 'bonus/v1/menu_highlights?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getMenuHighlights: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Update Menu Highlights
    async updateMenuHighlight({ commit, getters }, payload) {
        let res = {}
        let menu_id = ''
        menu_id = payload.menu_id
        delete payload.menu_id

        await axios.post(getters.StateApi + 'bonus/v1/menu_highlights/edit/' + menu_id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("updateMenuHighlight: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Virtual Bets
    async getVirtualBets({ commit, getters }, payload) {
        let res = {}

        const params = new URLSearchParams();

        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }

        await axios.get(getters.StateApi + 'bets/v1/virtual?' /*+ payload.game_type + '/?'*/ + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getVirtualBets: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Soft Gaming
    async getSoftGaming({ commit, getters, dispatch }, params) {
        let res = {}
        dispatch('HashCreate', params)
        await axios.get(getters.StateApi + 'bets/v1/soft_gaming?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getSoftGaming: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Casino Bets
    async getCasinoBets({ commit, getters }, payload) {
        let res = {}

        const params = new URLSearchParams();

        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }

        await axios.get(getters.StateApi + 'bets/v1/sports/' + payload.game_type + '/?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getBets: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Bet Audits
    async getBetAudits({ commit, getters }, payload) {
        let res = {}

        const params = new URLSearchParams();

        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }

        await axios.get(getters.StateApi + 'bets/v1/audits?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getBets: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Update Bet Audits
    async updateBetAudit({ commit, getters }, payload) {
        let res = {}

        await axios.post(getters.StateApi + 'bets/v1/audits/update/' + payload.id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              //  console.log("updateBetAudit: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Bet Limits
    async getBetLimits({ commit, getters }, payload) {
        let res = {}

        const params = new URLSearchParams();

        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }

        await axios.get(getters.StateApi + 'bets/v1/limits?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getBets: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Update Bet Limits
    async updateBetLimit({ commit, getters }, payload) {
        let res = {}

        await axios.post(getters.StateApi + 'bets/v1/limits/update/' + payload.id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              //  console.log("updateBetAudit: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },


    // ---- Deposits & Withdrawals -------------------------------------------------//
    // Deposits
    async getDeposits({ commit, getters }, params) {
        let res = {}
        await axios.get(getters.StateApi + 'trxns/v1/deposits?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getDeposits: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    async repostDeposit({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'adm/v1/deposit/repost', payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              //  console.log("addUser success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
              //  console.log("addUser error: " + JSON.stringify(error))
                res.status = error.response.status
                if (res.status === 422)
                    res.message = error.response.data.statusDescription
                else if (res.status === 403)
                    res.message = []
                else if (res.status === 401)
                    res.message = []
                else
                    res.message = error.response.data.data.message

                if (res.status === 401 || res.status === 403 || res.status === 406) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Withdrawals
    async getWithdrawals({ commit, getters }, params) {
        let res = {}
        await axios.get(getters.StateApi + 'trxns/v1/withdrawals?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getWithdrawals: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Wallet Approvals
    async getWalletApprovals({ commit, getters }, params) {
        let res = {}
        await axios.get(getters.StateApi + 'trxns/v1/wallet-approvals?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getWalletApprovals: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Update Wallet Approvals
    async updateWalletApprovals({ commit, getters }, payload) {
        let res = {}
      //  console.log(JSON.stringify(payload))
        await axios.post(getters.StateApi + 'trxns/v1/approval-risk/' + payload.id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {

                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Block withdrawals
    async updateWithdrawalStatus({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        let profile_id = payload.profile_id
        delete payload.profile_id
      //  console.log(JSON.stringify(payload))
        await axios.post(getters.StateApi + 'customer/v1/withdrawals/' + profile_id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              //  console.log("blockWithdrawals: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch
            (function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // enableDisableBulk
    async enableDisableBulk({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'customer/v1/bulk_sms/' + payload.profile_id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              //  console.log("enableDisableBulk: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                }
                else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })
        return res
    },

    // Transactions
    async getTransactions({ commit, getters }, params) {
        let res = {}
        await axios.get(getters.StateApi + 'trxns/v1/all?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getDeposits: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Outbox
    async getOutbox({ commit, getters }, params) {
        let res = {}
        await axios.get(getters.StateApi + 'rpt/v1/list/outbox?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getOutbox: " + JSON.stringify(response))
                if (response.data.code === "Error") {
                    res.status = response.data.data.code
                    res.message = response.data.data.message
                } else {
                    res.status = response.data.data.code
                    res.message = response.data.data.data

                }
                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Taxes
    async getTaxes({ commit, getters }, params) {
        let res = {}

        await axios.get(getters.StateApi + 'rpt/v1/list/taxes?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getTaxes: " + JSON.stringify(response.data))
                if (response.data.code === "Error") {
                    res.status = 404;
                    res.message = "";
                } else {
                    res.status = response.data.data.code
                    res.message = response.data.data.data

                    if (res.status === 401) {
                        commit('setLogOut')
                    }
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Tax Summary
    async getTaxSummary({ commit, getters }, params) {
        let res = {}
        await axios.get(getters.StateApi + 'rpt/v1/list/tax_summary?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getOutbox: " + JSON.stringify(response.data.code))
                if (response.data.code === "Error") {
                    res.status = 404;
                    res.message = "";
                } else {
                    res.status = response.data.data.code
                    res.message = response.data.data.data

                    if (res.status === 401) {
                        commit('setLogOut')
                    }
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Tax Payments
    async getTaxPayments({ commit, getters }, params) {
        let res = {}
        await axios.get(getters.StateApi + 'rpt/v1/list/tax_payments?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getOutbox: " + JSON.stringify(response.data.code))
                if (response.data.code === "Error") {
                    res.status = 404;
                    res.message = "";
                } else {
                    res.status = response.data.data.code
                    res.message = response.data.data.data

                    if (res.status === 401) {
                        commit('setLogOut')
                    }
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }

                }
            })

        return res
    },

    // Update Customer Account Status
    async updateCustomerAccountStatus({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'customer/v1/blacklist/' + payload.profile_id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              //  console.log("updateCustomerAccountStatus: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Get SMS Filters
    async getSmsFilters({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'rpt/v1/list/sms_filters', payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getSmsFilters: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Send BulkSMS
    async sendBulkSMS({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'customer/v1/send_blast/', payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              //  console.log("sendBulkSMS: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 403 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Process Scheduled SMS (Create/Schedule)
    async processScheduledSMS({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'customer/v1/schedule_sms', payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              //  console.log("processScheduledSMS: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log("processScheduledSMS error: " + JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 403 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Get Scheduled SMS
    async getScheduledSMS({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        const params = new URLSearchParams();

        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }

        await axios.get(getters.StateApi + 'customer/v1/get_scheduled_sms?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              //  console.log("getScheduledSMS: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log("getScheduledSMS error: " + JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Execute Scheduled SMS
    async executeScheduledSMS({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'customer/v1/process_scheduled_sms', payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              //  console.log("executeScheduledSMS: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log("executeScheduledSMS error: " + JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 403 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Get Reports
    async getSpecialReports({ commit, getters, dispatch }, params) {
        let res = {}
        dispatch('HashCreate', { timestamp: Date.now() })
        await axios.get(getters.StateApi + 'rpt/v1/special_reports?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              //  console.log("getSpecialReports: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log("getSpecialReports error: " + JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 403 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // Send Individual SMS
    async sendIndividualSMS({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)

        await axios.post(getters.StateApi + 'customer/v1/send_individual_sms', payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              //  console.log("sendIndividualSMS response:", JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log("sendIndividualSMS error:", JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 403 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    // ---- Promotion -------------------------------------------------//
    async getPromotionFilters({ commit, getters }, payload) {
        let res = {}
        // make params
        let params = new URLSearchParams();
        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }
        await axios.get(getters.StateApi + 'bonus/v1/promotions/types?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getDeposits: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    async getPromotions({ commit, getters }, payload) {
        let res = {}
        // make params
        let params = new URLSearchParams();
        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }
        await axios.get(getters.StateApi + 'bonus/v1/promotions?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getDeposits: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    async createPromotion({ commit, getters }, payload) {
        let res = {}

        await axios.post(getters.StateApi + 'bonus/v1/promotions/create', payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              //  console.log("createPromotion: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    async editPromotion({ commit, getters }, payload) {
        let res = {}
        // make params
        // let promo_id = payload.promo_id
        // delete payload.promo_id

        await axios.post(getters.StateApi + 'bonus/v1/promotions/edit/' + payload.promo_id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              //  console.log("editPromotion: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    async awardCustomerBonusFreeBet({ commit, getters }, payload) {
        let res = {}

        await axios.post(getters.StateApi + 'bonus/v1/award_bonus_freebet', payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              //  console.log("awardCustomerBonusFreeBet: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },


    async getCampaignOrigins({ commit, getters }, payload) {
        let res = {
            status: 0,
            message: null
        };

        try {
            const params = new URLSearchParams();
            for (const key in payload) {
                if (payload.hasOwnProperty(key)) {
                    params.append(key, payload[key]);
                }
            }

            const response = await axios.get(getters.StateApi + 'bonus/v1/campaign_origins?' + params, {
                headers: {
                    'x-authorization': getters.StateAuthKey,
                    'x-app-key': getters.StateAppKey,
                    'x-hash-key': getters.StateHashKey,
                    'x-access': getters.StateTokenKey,
                },
            });

            if (response.data && response.data.data) {
                res.status = response.data.data.code;
                res.message = response.data.data.data;

                if (res.status === 401) {
                    commit('setLogOut');
                }
            }
        } catch (error) {
            console.error(error);

            if (error.response) {
                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data?.statusDescription;
                    res.status = error.response.status;
                } else if (error.response.data?.data) {
                    res.message = error.response.data.data.message;
                    res.status = error.response.data.data.code;

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut');
                    }
                }
            } else {
                res.status = 500;
                res.message = 'Network Error';
            }
        }

        return res;
    },

    async getCampaignTransactions({ commit, getters }, payload) {
        let res = {}
        // make params
        let params = new URLSearchParams();
        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }

      //  console.log("getCampaignTransactions: ", JSON.stringify(payload))

        await axios.post(getters.StateApi + 'bonus/v1/campaign_origins/transactions/' + payload.campaign_id + '?', payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getCampaignTransactions: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    async getLeaderboard({ commit, getters }, payload) {
        let res = {}
        // make params
        let params = new URLSearchParams();
        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }

        await axios.get(getters.StateApi + 'bonus/v1/leaderboard?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getLeaderboard: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    async updateLeaderboard({ commit, getters }, payload) {
        let res = {}
        // make params
        // let params = new URLSearchParams();
        // for (const key in payload) {
        //     if (payload.hasOwnProperty(key)) {
        //         params.append(key, payload[key]);
        //     }
        // }

      //  console.log("updateLeaderboard:> ", JSON.stringify(payload.id))

        await axios.post(getters.StateApi + 'bonus/v1/leaderboard/update/' + payload.id, payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getLeaderboard: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },



    // ---- PayBills -------------------------------------------------//
    async getPaybills({ commit, getters }, payload) {
        let res = {}
        // make params
        let params = new URLSearchParams();
        for (const key in payload) {
            if (payload.hasOwnProperty(key)) {
                params.append(key, payload[key]);
            }
        }
        await axios.get(getters.StateApi + 'rpt/v1/list/paybills?' + params, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("getDeposits: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                if (error.response.status === 422 || error.response.status === 500) {
                    res.message = error.response.data.statusDescription
                } else {
                    res.message = error.response.data.data.message
                    res.status = error.response.data.data.code

                    if (res.status === 401 || res.status === 406 || res.status === 500) {
                        commit('setLogOut')
                    }
                }
            })

        return res
    },

    async addPayBill({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'adm/v1/paybill_create', payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("addPayBill success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log("addPayBill error: " + JSON.stringify(error))
                res.status = error.response.status
                if (res.status === 422)
                    res.message = error.response.data.statusDescription
                else if (res.status === 403)
                    res.message = []
                else if (res.status === 401)
                    res.message = []
                else
                    res.message = error.response.data.data.message

                if (res.status === 401 || res.status === 403 || res.status === 406) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async editPayBill({ commit, getters, dispatch }, payload) {
        const payloadWithoutId = { ...payload };
        delete payloadWithoutId.id;
        let res = {}
        dispatch('HashCreate', payloadWithoutId)
        await axios.post(getters.StateApi + 'adm/v1/paybill_edit/' + payload.id, payloadWithoutId, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("addPayBill success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log("addPayBill error: " + JSON.stringify(error))
                res.status = error.response.status
                if (res.status === 422)
                    res.message = error.response.data.statusDescription
                else if (res.status === 403)
                    res.message = []
                else if (res.status === 401)
                    res.message = []
                else
                    res.message = error.response.data.data.message

                if (res.status === 401 || res.status === 403 || res.status === 406) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async editPayBillWLIPAddresses({ commit, getters, dispatch }, payload) {
        const payloadWithoutId = { ...payload };
        delete payloadWithoutId.id;
        let res = {}
        dispatch('HashCreate', payloadWithoutId)
        await axios.post(getters.StateApi + 'adm/v1/paybill_ipaddress/' + payload.id, payloadWithoutId, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("addPayBill success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log("addPayBill error: " + JSON.stringify(error))
                res.status = error.response.status
                if (res.status === 422)
                    res.message = error.response.data.statusDescription
                else if (res.status === 403)
                    res.message = []
                else if (res.status === 401)
                    res.message = []
                else
                    res.message = error.response.data.data.message

                if (res.status === 401 || res.status === 403 || res.status === 406) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async switchPaybillRoute({ commit, getters, dispatch }, payload) {
        const payloadWithoutId = { ...payload };
        delete payloadWithoutId.id;
        let res = {}
        dispatch('HashCreate', payloadWithoutId)
        await axios.post(getters.StateApi + 'adm/v1/payouts/route/' + payload.id, payloadWithoutId, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("addPayBill success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log("addPayBill error: " + JSON.stringify(error))
                res.status = error.response.status
                if (res.status === 422)
                    res.message = error.response.data.statusDescription
                else if (res.status === 403)
                    res.message = []
                else if (res.status === 401)
                    res.message = []
                else
                    res.message = error.response.data.data.message

                if (res.status === 401 || res.status === 403 || res.status === 406) {
                    commit('setLogOut')
                }
            })

        return res
    },

    async disableEnablePaybill({ commit, getters, dispatch }, payload) {
        const payloadWithoutId = { ...payload };
        delete payloadWithoutId.id;
        let res = {}
        dispatch('HashCreate', payloadWithoutId)
        await axios.post(getters.StateApi + 'adm/v1/enable/payouts/' + payload.id, payloadWithoutId, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("disableEnablePaybill success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log("addPayBill error: " + JSON.stringify(error))
                res.status = error.response.status
                if (res.status === 422)
                    res.message = error.response.data.statusDescription
                else if (res.status === 403)
                    res.message = []
                else if (res.status === 401)
                    res.message = []
                else
                    res.message = error.response.data.data.message

                if (res.status === 401 || res.status === 403 || res.status === 406) {
                    commit('setLogOut')
                }
            })

        return res
    },


    // ---- System -------------------------------------------------//
    // Get System Users
    async getSystemUsers({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        // make payload to query string
        payload = new URLSearchParams(payload).toString()
        // await axios.get(getters.StateApi + 'adm/v1/system/users/list?' + payload, {
        await axios.get(getters.StateApi + 'user/v1/system/users/list?' + payload, {
            headers: {
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Hash-Key': getters.StateHashKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getSystemUsers: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Add System User
    async addSystemUser({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'user/v1/create', payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
              //  console.log("addSystemUser success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log("addUser error: " + JSON.stringify(error))
                res.status = error.response.status
                if (res.status === 422)
                    res.message = error.response.data.statusDescription
                else if (res.status === 403)
                    res.message = []
                else if (res.status === 401)
                    res.message = []
                else
                    res.message = error.response.data.data.message

                if (res.status === 401 || res.status === 403 || res.status === 406) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Update System User
    async updateSystemUser({ commit, getters, dispatch }, payload) {
        const payloadWithoutId = { ...payload };
        delete payloadWithoutId.user_id;

        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'user/v1/edit/' + payload.user_id, payloadWithoutId, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
              //  console.log("updateSystemUser success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log("updateUser error: " + JSON.stringify(error))
                res.status = error.response.status
                if (res.status === 422)
                    res.message = error.response.data.statusDescription
                else if (res.status === 403)
                    res.message = []
                else if (res.status === 401)
                    res.message = []
                else
                    res.message = error.response.data.data.message

                if (res.status === 401 || res.status === 403 || res.status === 406) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Get System Roles
    async getSystemRoles({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        const params = new URLSearchParams({
            status: payload.status || "",
            user_name: payload.user_name || "",
            start: payload.start || "",
            end: payload.end || "",
            page: payload.page || "",
            skip_cache: payload.skip_cache || false,
            limit: payload.limit || 10,
            timestamp: payload.timestamp || Date.now(),
            sort: payload.sort || ""
        });

        await axios.get(getters.StateApi + 'user/v1/system/roles?' + params.toString(), {
            headers: {
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Hash-Key': getters.StateHashKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getSystemRoles: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                // console.log("getSystemRoles res: " + JSON.stringify(res))
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Add System Role
    async addSystemRole({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'user/v1/system/roles/create', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
              //  console.log("addSystemRole success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Update System Role
    async updateSystemRole({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)

        await axios.post(getters.StateApi + 'user/v1/system/roles/update/' + payload.role_id, payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
              //  console.log("success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Get System Permissions
    async getSystemPermissions({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        let queryString = new URLSearchParams(payload).toString();

        await axios.get(getters.StateApi + 'user/v1/system/permissions?' + queryString, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
                // console.log(JSON.stringify("error:: ",error))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Add System Permissions
    async addSystemPermissions({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'user/v1/system/permissions/create', payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
              //  console.log("success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Update System Permissions
    async updateSystemPermissions({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)

        await axios.post(getters.StateApi + 'user/v1/system/permissions/update/' + payload.role_id, payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
              //  console.log("success: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // Send OTP
    async sendUserOTP({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)

        let user_id = payload.user_id
        delete payload.user_id

        await axios.post(getters.StateApi + 'user/v1/resend_otp/' + user_id, payload, {
            headers: {
                'X-Hash-Key': getters.StateHashKey,
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                res.status = response.data.data.code
                res.message = response.data.data

                if (res.status === 401) {
                    commit('setLogOut')
                }

            })
            .catch(function (error) {
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },

    // ---- Pragmatic -------------------------------------------------//
    // Pragmatic API Call
    async pragmaticAction({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        await axios.post(getters.StateApi + 'pragmatic/v1/check_fbs', payload, {
            headers: {
                'x-authorization': getters.StateAuthKey,
                'x-app-key': getters.StateAppKey,
                'x-hash-key': getters.StateHashKey,
                'x-access': getters.StateTokenKey,
            }
        })
            .then(async function (response) {
                // console.log("sendUserOTP: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                res.message = response.data.data.message
            })
            .catch(async function (error) {
              //  console.log('error: ' + JSON.stringify(error.response.data))

                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    // commit('setLogOut')
                }
            })

        return res
    },

    // Get Auth Channels
    async getAuthChannels({ commit, getters, dispatch }, payload) {
        let res = {}
        dispatch('HashCreate', payload)
        // make payload to query string
        payload = new URLSearchParams(payload)
        await axios.get(getters.StateApi + 'rpt/v1/list/channels?' + payload, {
            headers: {
                'X-Authorization': getters.StateAuthKey,
                'X-App-Key': getters.StateAppKey,
                'X-Hash-Key': getters.StateHashKey,
                'X-Access': getters.StateTokenKey
            }
        })
            .then(async function (response) {
                // console.log("getSystemUsers: " + JSON.stringify(response.data))
                res.status = response.data.data.code
                if (response.data.code === 'Error') {
                    res.message = []
                } else {
                    res.message = response.data.data.data
                }
                if (res.status === 401) {
                    commit('setLogOut')
                }
            })
            .catch(function (error) {
              //  console.log(JSON.stringify(error))
                res.status = error.response.status

                if (res.status === 422) {
                    res.message = error.response.data.statusDescription
                } else if (res.status === 401 || res.status === 403) {
                    res.status = error.response.data.data.code
                    res.message = error.response.data.data.message
                } else {
                    res.message = error.response.data.data.message
                }

                if (res.status === 401) {
                    commit('setLogOut')
                }
            })

        return res
    },


    ///-------------------------------------------------------------------------///
    async fillSystemUser({ commit }, payload) {
        await commit('setSystemUser', payload)
        return true
    },
    async fillPromo({ commit }, payload) {
        await commit('setPromo', payload)
        return true
    },
    async fillUserName({ commit }, payload) {
        await commit('setUserName', payload)
        return true
    },

    async fillMessage({ commit }, payload) {
        await commit('setMessage', payload)
        return true
    },

    async fillRole({ commit }, payload) {
        await commit('setRoleData', payload)
        return true
    },

    async fillClientList({ commit }, payload) {
        await commit('setClientList', payload)
        return true
    },

    async fillClientId({ commit }, payload) {
        await commit('setClientId', payload)
        return true
    },

    async fillBetDetails({ commit }, payload) {
        await commit('setBetDetails', payload)
        return true
    },

    async fillPragmatic({ commit }, payload) {
        await commit('setPragmatic', payload)
        return true
    },

    async handleErrorResponse(fromFunc, error, commit) {
        const res = {
            status: error.response.status,
            message: []
        };

        switch (res.status) {
            case 401:
            case 403:
            case 406:
                // Clear message array for specific status codes
                break;
            default:
                // Set message from error response data
                res.message = error.response.data?.data?.message || [];
                break;
        }
        // Logout
        if (res.status === 401) {
            commit('setLogOut');
        }
    },


    async fillPermissions({ commit }, payload) {
        await commit('setPermissions', payload)
        return true
    },

    async fillMsisdn({ commit }, msisdn) {
        await commit('setMsisdn', msisdn)
        return true
    },
    async fillCustomer({ commit }, data) {
        await commit('setCustomer', data)
        return true
    },

    // Neew
    async fillPayBill({ commit }, pb) {
        await commit('setPayBill', pb)
        return true
    },
    async fillAuthChannel({ commit }, channel) {
        await commit('setAuthChannel', channel)
        return true
    },

    async fillBet({ commit }, data) {
        await commit('setBet', data)
        return true
    },

};


const mutations = {
    TOGGLE_SIDE_MENU(state) {
        state.isSideMenuOpen = !state.isSideMenuOpen;
    },
    TOGGLE_SIDE_MENU_INIT(state) {
        state.isSideMenuOpen = false;
    },

    setAuth(state, token) {
        state.tokenKey = token
    },
    setLoggedUser(state, data) {
        state.loggedUser = data
    },
    setPermissions(state, data) {
        state.permissions = data
    },
    setHashKey(state, token) {
        state.hash_key = token
    },
    setUserName(state, data) {
        state.user_name = data
    },
    setProfile(state, data) {
        state.profile = data
    },
    setMsisdn(state, msisdn) {
        state.msisdn = msisdn
    },
    setCustomer(state, data) {
        state.customer = data
    },
    setBet(state, data) {
        state.bet = data
    },
    setIsSuperRole(state, role) {
      //  console.log(`isSuperRole??:`, role)
        state.isSuperRole = role
    },
    setLogOut(state) {
        state.tokenKey = null
        router.push({ name: 'login' });
    },
    setPromo(state, data) {
        state.promo = data
    },
    setSystemUser(state, data) {
        state.user = data
    },
    setPermission(state, permission) {
        state.permission = permission
    },
    setRole(state, rid) {
      //  console.log(`role??:`, rid)
        state.role = rid
    },
    setRoleData(state, data) {
        state.roleData = data
    },
    setMessage(state, data) {
        state.message = data
    },

    // neew
    setPayBill(state, pb) {
        state.paybill = pb
    },
    setAuthChannel(state, channel) {
        state.authChannel = channel
    },
    setBetDetails(state, data) {
        state.betDetails = data
    },
    setClientList(state, data) {
        state.clients = data
    },
    setClientId(state, data) {
        state.client_id = data
    },
    setPragmatic(state, data) {
        state.pragmatic = data
    },
};

// Create a new store instance.
const store = createStore({
    state() {
        return state
    },
    mutations: mutations,
    actions: actions,
    getters: getters,
    plugins: [createPersistedState()]
})

export default store;
