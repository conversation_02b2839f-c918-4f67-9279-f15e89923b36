<template>
  <div class="flex flex-col min-h-screen">
    <!-- <TopBar /> -->
    <div class="flex flex-1 pt-[60px]">
      <SideBar class="w-[250px] h-[calc(100vh-60px)] fixed overflow-y-auto bg-[#2c3e50] text-white top-[60px] left-0" />
      <div class="flex-1 ml-[250px] mt-[30px] p-5">
        <slot />
      </div>
    </div>
  </div>
</template>

<script>
import TopBar from '@/app/TopBar.vue'
import SideBar from '@/app/SideBar.vue'

export default {
  name: 'AppLayout',
  components: {
    TopBar,
    SideBar,
  }
}
</script>

<style scoped>
/* Can be removed since we're using Tailwind classes */
</style>