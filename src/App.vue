<template>
  <div>
    <!-- Show SideBar only for authenticated routes -->
    <SideBar v-if="isAuthenticated && !isGuestRoute">
      <router-view />
    </SideBar>

    <!-- Show router-view directly for guest routes (login, etc.) -->
    <router-view v-if="!isAuthenticated || isGuestRoute" />
  </div>
</template>

<script>
import SideBar from './app/SideBar.vue';
import { mapGetters } from 'vuex';

export default {
  name: 'App',
  components: {
    SideBar
  },
  computed: {
    ...mapGetters(['isAuthenticated']),
    isGuestRoute() {
      return this.$route.meta && this.$route.meta.guest;
    }
  }
}
</script>
