<template>
  <div class="block py-4 bg-white rounded-lg shadow-lg mx-3 border overflow-x-auto">
    <table class="w-full mb-12 table min-w-full">
      <thead class="border-b-2 text-xs text-left">
      <tr class="table-row">
        <th v-for="(header, index) in headers" :key="index" class="py-2 px-2 text-left">
          {{ header.label }}
        </th>
        <th class="py-2 px-2 text-center" v-if="hasActions">Actions</th>
      </tr>
      </thead>
      <tbody class="text-xs text-gray-600 divide-y">
      <tr v-for="(item, index) in items" :key="item.id">
        <td v-for="(header, index) in headers" :key="index" class="py-2 px-2">
          <slot :name="header.key" :item="item">
            {{ item[header.key] ?? "-" }}
          </slot>
        </td>
        <!-- Actions Column -->
        <td class="py-2 px-2 text-center" v-if="hasActions">
          <slot name="actions" :item="item" :index="index"></slot>
        </td>
      </tr>
      </tbody>
    </table>

    <!-- Pagination -->
    <div class="flex-grow text-right" v-show="total > limit">
      <div class="inline-block bg-white border rounded-md divide-x">
        <button class="p-2 px-3" v-show="Math.ceil(total/limit) > 1" @click="$emit('pageChange', offset-1)">&larr;</button>
        <button class="p-2 px-4" :class="{'bg-gray-100':offset===1}" @click="$emit('pageChange', 1)">1</button>
        <button class="p-2 px-4" :class="{'bg-gray-100':offset===2}" v-show="Math.ceil(total/limit) > 1"
                @click="$emit('pageChange', 2)">2
        </button>
        <button class="p-2 px-4" v-show="Math.ceil(total/limit) > 3">...</button>
        <button class="p-2 px-4" :class="{'bg-gray-100':offset===offset}"
                v-show="Math.ceil(total/limit) > 3 && offset >= 3" @click="$emit('pageChange', offset)">{{ offset }}
        </button>

        <button class="p-2 px-4" :class="{'bg-gray-100':offset===Math.ceil(total/limit)}"
                v-show="Math.ceil(total/limit) > 4" @click="$emit('pageChange', Math.ceil(total/limit))">
          {{ Math.ceil(total / limit) }}
        </button>
        <button class="p-2 px-3" v-show="(offset*limit) < total" @click="$emit('pageChange', offset+1)">&rarr;</button>
      </div>
    </div>

  </div>
</template>

<script>
export default {
  props: {
    headers: { type: Array, required: true }, // Table headers with labels & keys
    items: { type: Array, required: true }, // Data items
    hasActions: { type: Boolean, default: false }, // Show/hide Actions column
    total: { type: Number, default: 0 }, // Total items for pagination
    limit: { type: Number, default: 10 }, // Items per page
    offset: { type: Number, default: 1 }, // Current page
  },
  data() {
    return {
      showDropdown: [], // Track dropdown open state per row
    };
  },
  computed: {
    totalPages() {
      return Math.ceil(this.total / this.limit);
    },
  },
  methods: {
    toggleDropdown(index) {
      // Close other dropdowns and toggle only the selected one
      this.$set(this.showDropdown, index, !this.showDropdown[index]);
    },
  },
};
</script>