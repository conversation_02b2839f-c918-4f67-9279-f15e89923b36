<template>
  <div>
    <button 
      class="inline-flex items-center px-4 py-2 rounded-md bg-green-600 text-white hover:bg-green-700 transition-colors duration-200 ease-in-out"
      @click.prevent="exportToExcel"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      {{ buttonText || 'Export to Excel' }}
    </button>
  </div>
</template>

<script>
import * as XLSX from 'xlsx';
import moment from 'moment';

export default {
  name: 'ExcelExport',
  props: {
    headers: {
      type: Object,
      required: true
    },
    items: {
      type: Array,
      required: true
    },
    fileTitle: {
      type: String,
      default: 'Export'
    },
    buttonText: {
      type: String,
      default: ''
    },
    sheetName: {
      type: String,
      default: 'Sheet1'
    }
  },
  methods: {
    exportToExcel() {
      if (this.items.length < 1) {
        this.$swal.fire('No Data', 'There is no data to export', 'warning');
        return;
      }

      // Format the data
      const formattedItems = this.items.map(item => {
        const formattedItem = {};
        for (const key in this.headers) {
          if (item.hasOwnProperty(key)) {
            // Format date fields
            if (key.includes('date') && item[key]) {
              formattedItem[this.headers[key]] = moment(item[key]).format('YYYY-MM-DD HH:mm:ss');
            } 
            // Format number fields
            else if (
              (key.includes('amount') || 
               key.includes('total') || 
               key.includes('tax') || 
               key.includes('stake') || 
               key.includes('payout') || 
               key.includes('ggr')) && 
              !isNaN(item[key])
            ) {
              formattedItem[this.headers[key]] = parseFloat(item[key]);
            } 
            // Other fields
            else {
              formattedItem[this.headers[key]] = item[key];
            }
          } else {
            formattedItem[this.headers[key]] = '';
          }
        }
        return formattedItem;
      });

      // Create workbook and worksheet
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(formattedItems);

      // Set column widths
      const colWidths = [];
      for (const header in this.headers) {
        colWidths.push({ wch: Math.max(this.headers[header].length * 1.5, 12) });
      }
      ws['!cols'] = colWidths;

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(wb, ws, this.sheetName);

      // Generate Excel file
      const fileName = `${this.fileTitle}.xlsx`;
      XLSX.writeFile(wb, fileName);

      // Emit event
      this.$emit('exported');
    }
  }
};
</script>

<style scoped>
button {
  font-weight: 600;
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
