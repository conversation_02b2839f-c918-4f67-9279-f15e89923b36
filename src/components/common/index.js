import AutoTable from './AutoTable.vue';
import TablePagination from './TablePagination.vue';
import ActionDropdown from './ActionDropdown.vue';
import ActionItem from './ActionItem.vue';
import CustomLoading from './CustomLoading.vue';
import MobileFriendlyDatePicker from './MobileFriendlyDatePicker.vue';

// Export AutoTable as DataTable for backward compatibility
const DataTable = AutoTable;

export {
  AutoTable,
  DataTable,
  TablePagination,
  ActionDropdown,
  ActionItem,
  CustomLoading,
  MobileFriendlyDatePicker
};

// This allows for importing all components at once
// Example: import { AutoTable, TablePagination, ActionDropdown, ActionItem } from '@/components/common';
