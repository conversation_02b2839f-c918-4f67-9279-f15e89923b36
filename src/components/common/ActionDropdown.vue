<template>
  <div class="relative inline-block text-left">
    <!-- Dropdown Button -->
    <button 
      @click="toggleDropdown"
      type="button"
      class="inline-flex items-center justify-center rounded-md p-2 text-gray-600 hover:text-gray-900 focus:outline-none"
      :class="buttonClass"
    >
      <span v-if="showText" class="mr-2">{{ buttonText }}</span>
      <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
      </svg>
    </button>

    <!-- Dropdown Menu -->
    <div
      v-if="isOpen"
      :class="[
        'absolute mt-2 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none',
        menuClass,
        menuWidth ? `w-${menuWidth}` : 'w-56',
        dropdownPosition
      ]"
      style="min-width: 10rem; z-index: 9999;"
    >
      <div class="py-1">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ActionDropdown',
  props: {
    buttonText: {
      type: String,
      default: 'Actions'
    },
    showText: {
      type: Boolean,
      default: false
    },
    buttonClass: {
      type: String,
      default: ''
    },
    menuClass: {
      type: String,
      default: ''
    },
    menuWidth: {
      type: [String, Number],
      default: null
    },
    closeOnAction: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isOpen: false,
      dropdownPosition: 'right-0' // Default position
    };
  },
  methods: {
    toggleDropdown() {
      this.isOpen = !this.isOpen;

      if (this.isOpen) {
        this.$nextTick(() => {
          // Check if dropdown would be cut off on the right side
          this.adjustDropdownPosition();
          document.addEventListener('click', this.handleClickOutside);
        });
      }
    },
    adjustDropdownPosition() {
      const dropdown = this.$el.querySelector('.absolute');
      if (!dropdown) return;
      
      const rect = dropdown.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      
      // If dropdown extends beyond the right edge of the viewport
      if (rect.right > viewportWidth) {
        this.dropdownPosition = 'left-0';
      } else {
        this.dropdownPosition = 'right-0';
      }
    },
    handleClickOutside(event) {
      const dropdown = this.$el;

      if (!dropdown.contains(event.target)) {
        this.isOpen = false;
        document.removeEventListener('click', this.handleClickOutside);
      }
    },
    closeDropdown() {
      this.isOpen = false;
      document.removeEventListener('click', this.handleClickOutside);
    }
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
  }
};
</script>

<style scoped>
/* Add any custom styles here */
.relative {
  position: relative;
}

/* Ensure dropdown is visible even when near the edge of the table */
.absolute {
  position: absolute;
  max-height: 80vh; /* Limit height to prevent overflow */
  overflow-y: auto; /* Add scrolling if needed */
  z-index: 9999 !important; /* Ensure it's above other elements */
}
</style>
