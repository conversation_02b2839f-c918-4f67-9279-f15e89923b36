# Common Components

This directory contains reusable components that can be used throughout the application.

## DataTable

A flexible and reusable data table component with built-in pagination, sorting, and customizable cell rendering.

### Basic Usage

```vue
<template>
  <data-table
    :headers="headers"
    :items="items"
  />
</template>

<script>
import { DataTable } from '@/components/common';

export default {
  components: {
    DataTable
  },
  data() {
    return {
      headers: [
        { key: 'id', label: 'ID', align: 'center', width: '80px' },
        { key: 'name', label: 'Name', align: 'left', width: '200px' },
        { key: 'email', label: 'Email', align: 'left' }
      ],
      items: [
        { id: 1, name: '<PERSON>', email: '<EMAIL>' },
        { id: 2, name: '<PERSON>', email: '<EMAIL>' }
      ]
    };
  }
};
</script>
```

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| headers | Array | required | Array of objects with `key`, `label`, `align`, and optional `width` properties. The Actions column is automatically positioned at the far right. |
| items | Array | required | Array of data objects to display |
| hasActions | Boolean | false | Whether to show an actions column |
| itemKey | String | 'id' | Property to use as unique key for items |
| pagination | Boolean | true | Whether to enable pagination |
| serverSidePagination | Boolean | false | Whether pagination is handled by the server |
| totalItems | Number | 0 | Total number of items (for server-side pagination) |
| itemsPerPage | Number | 10 | Number of items to display per page |
| currentPageProp | Number | 1 | Current page number |
| sortable | Boolean | false | Whether to enable column sorting |
| defaultSortKey | String | '' | Default column to sort by |
| defaultSortOrder | String | 'asc' | Default sort order ('asc' or 'desc') |
| emptyMessage | String | 'No data available' | Message to display when there are no items |
| showItemsCount | Boolean | true | Whether to show item count |
| autoSizeColumns | Boolean | true | Whether to automatically size columns based on content type |

### Events

| Event | Parameters | Description |
|-------|------------|-------------|
| page-change | page | Emitted when page changes |
| sort-change | { key, order } | Emitted when sort changes |

### Slots

#### Default Slots

For each column, you can provide a named slot using the column's `key`:

```vue
<data-table :headers="headers" :items="items">
  <template #name="{ item }">
    <strong>{{ item.name }}</strong>
  </template>
</data-table>
```

#### Actions Slot

```vue
<data-table :headers="headers" :items="items" :has-actions="true">
  <template #actions="{ item }">
    <button @click="editItem(item)">Edit</button>
    <button @click="deleteItem(item)">Delete</button>
  </template>
</data-table>
```

### Server-Side Pagination Example

```vue
<template>
  <!-- Loading indicator should be handled separately -->
  <custom-loading :active="loading" :is-full-page="false" color="red" />

  <data-table
    :headers="headers"
    :items="items"
    :total-items="totalItems"
    :current-page-prop="currentPage"
    :server-side-pagination="true"
    @page-change="handlePageChange"
  />
</template>

<script>
import { DataTable, CustomLoading } from '@/components/common';

export default {
  components: {
    DataTable,
    CustomLoading
  },
  data() {
    return {
      headers: [...],
      items: [],
      loading: false,
      totalItems: 0,
      currentPage: 1
    };
  },
  methods: {
    async handlePageChange(page) {
      this.currentPage = page;
      await this.fetchData(page);
    },
    async fetchData(page) {
      this.loading = true;
      try {
        const response = await api.getData({ page });
        this.items = response.data;
        this.totalItems = response.total;
      } finally {
        this.loading = false;
      }
    }
  },
  mounted() {
    this.fetchData(1);
  }
};
</script>
```

## TablePagination

A pagination component used by DataTable, but can also be used independently.

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| total | Number | required | Total number of items |
| limit | Number | 10 | Number of items per page |
| currentPage | Number | 1 | Current page number |

### Events

| Event | Parameters | Description |
|-------|------------|-------------|
| page-change | page | Emitted when page changes |

## ActionDropdown

A reusable dropdown menu component for displaying action options.

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| buttonText | String | 'Actions' | Text to display on the dropdown button (also used as tooltip) |
| showText | Boolean | false | Whether to show text on the button (false shows 3-dots menu icon) |
| buttonClass | String | '' | Additional CSS classes for the button |
| menuClass | String | '' | Additional CSS classes for the dropdown menu |
| menuWidth | String/Number | null | Width of the dropdown menu (e.g., '48' for w-48) |
| closeOnAction | Boolean | true | Whether to close the dropdown when an action is clicked |

### Methods

| Method | Description |
|--------|-------------|
| toggleDropdown() | Toggles the dropdown open/closed |
| closeDropdown() | Closes the dropdown |

### Example

```vue
<template>
  <!-- With 3-dots menu icon (default) -->
  <action-dropdown button-text="Options" :show-text="false">
    <action-item text="Edit" color="blue" @click="editItem">
      <template #icon>
        <svg class="mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
      </template>
    </action-item>
    <action-item text="Delete" color="red" @click="deleteItem">
      <template #icon>
        <svg class="mr-3 h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
      </template>
    </action-item>
  </action-dropdown>

  <!-- With text button -->
  <action-dropdown button-text="Actions" :show-text="true">
    <!-- Action items... -->
  </action-dropdown>
</template>

<script>
import { ActionDropdown, ActionItem } from '@/components/common';

export default {
  components: {
    ActionDropdown,
    ActionItem
  },
  methods: {
    editItem() {
      // Edit logic
    },
    deleteItem() {
      // Delete logic
    }
  }
};
</script>
```

## ActionItem

A component for individual action items within an ActionDropdown.

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| text | String | '' | Text to display for the action |
| color | String | 'default' | Color scheme for the action (default, blue, red, green, yellow, gray) |
| itemClass | String | '' | Additional CSS classes for the action item |
| sectionClass | String | '' | Additional CSS classes for the section containing the action item |
| disabled | Boolean | false | Whether the action is disabled |
| title | String | '' | Tooltip text for the action |

### Events

| Event | Parameters | Description |
|-------|------------|-------------|
| click | event | Emitted when the action is clicked |

## CustomLoading

A flexible loading spinner component that can be used as a replacement for vue-loading-overlay.

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| active | Boolean | false | Whether the loading spinner is visible |
| isFullPage | Boolean | true | Whether the spinner covers the full page or just its container |
| color | String | 'red' | Color of the spinner (red, blue, green, yellow, indigo, purple, gray) |
| backgroundColor | String | 'white' | Background color of the overlay |
| opacity | Number | 0.7 | Opacity of the background overlay (0-1) |
| blur | Number | 0 | Blur effect on the background (0-10) |
| label | String | 'Loading...' | Text to display below the spinner |
| showLabel | Boolean | true | Whether to show the label text |
| zIndex | Number | 9999 | Z-index of the loading overlay |

### Example

```vue
<template>
  <!-- Basic usage -->
  <custom-loading :active="isLoading" />

  <!-- With custom styling -->
  <custom-loading
    :active="isLoading"
    :is-full-page="true"
    color="blue"
    :opacity="0.8"
    :blur="5"
    label="Processing..."
  />

  <!-- Container-scoped loading (not full page) -->
  <div class="relative">
    <custom-loading :active="isComponentLoading" :is-full-page="false" />
    <!-- Component content -->
  </div>
</template>

<script>
import { CustomLoading } from '@/components/common';

export default {
  components: {
    CustomLoading
  },
  data() {
    return {
      isLoading: false,
      isComponentLoading: false
    };
  },
  methods: {
    async fetchData() {
      this.isLoading = true;
      try {
        // Fetch data
      } finally {
        this.isLoading = false;
      }
    }
  }
};
</script>
```