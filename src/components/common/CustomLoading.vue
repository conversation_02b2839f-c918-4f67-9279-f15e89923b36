<template>
  <div v-if="active" class="custom-loading-overlay" :class="{ 'is-full-page': isFullPage }">
    <div class="custom-loading-container">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2" :class="colorClass"></div>
      <div v-if="showLabel" class="loading-text mt-3 text-sm font-medium" :class="textColorClass">
        {{ label }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomLoading',
  props: {
    active: {
      type: Boolean,
      default: false
    },
    isFullPage: {
      type: Boolean,
      default: true
    },
    color: {
      type: String,
      default: 'red',
      validator: (value) => ['red', 'blue', 'green', 'yellow', 'indigo', 'purple', 'gray'].includes(value)
    },
    backgroundColor: {
      type: String,
      default: 'white'
    },
    opacity: {
      type: Number,
      default: 0.7,
      validator: (value) => value >= 0 && value <= 1
    },
    blur: {
      type: Number,
      default: 0,
      validator: (value) => value >= 0 && value <= 10
    },
    label: {
      type: String,
      default: 'Loading...'
    },
    showLabel: {
      type: Boolean,
      default: true
    },
    zIndex: {
      type: Number,
      default: 9999
    }
  },
  computed: {
    colorClass() {
      const colorMap = {
        'red': 'border-red-500',
        'blue': 'border-blue-500',
        'green': 'border-green-500',
        'yellow': 'border-yellow-500',
        'indigo': 'border-indigo-500',
        'purple': 'border-purple-500',
        'gray': 'border-gray-500'
      };

      return colorMap[this.color] || 'border-red-500';
    },
    textColorClass() {
      const colorMap = {
        'red': 'text-red-500',
        'blue': 'text-blue-500',
        'green': 'text-green-500',
        'yellow': 'text-yellow-500',
        'indigo': 'text-indigo-500',
        'purple': 'text-purple-500',
        'gray': 'text-gray-500'
      };

      return colorMap[this.color] || 'text-red-500';
    }
  }
};
</script>

<style scoped>
.custom-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: v-bind('zIndex');
}

.custom-loading-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: v-bind('backgroundColor');
  opacity: v-bind('opacity');
  backdrop-filter: v-bind('`blur(${blur}px)`');
}

.custom-loading-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-text {
  font-weight: 500;
}

/* Full page styling */
.custom-loading-overlay.is-full-page {
  position: fixed;
}
</style>
