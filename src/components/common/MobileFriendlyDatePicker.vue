<template>
  <div class="mobile-friendly-datepicker">
    <VueDatePicker
      v-model="internalValue"
      v-bind="$attrs"
      :class="['responsive-datepicker', pickerClass]"
      :multi-calendars="!isMobile"
      @update:model-value="handleUpdate"
      @change="handleChange"
    />
  </div>
</template>

<script>
import VueDatePicker from '@vuepic/vue-datepicker';

export default {
  name: 'MobileFriendlyDatePicker',
  components: {
    VueDatePicker
  },
  props: {
    modelValue: {
      type: [Date, Array, String, null],
      default: null
    },
    pickerClass: {
      type: String,
      default: 'w-full text-sm'
    }
  },
  emits: ['update:modelValue', 'change'],
  data() {
    return {
      windowWidth: window.innerWidth,
      internalValue: this.modelValue
    }
  },
  computed: {
    isMobile() {
      return this.windowWidth <= 768;
    }
  },
  watch: {
    modelValue(newVal) {
      this.internalValue = newVal;
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize);
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    handleResize() {
      this.windowWidth = window.innerWidth;
    },
    handleUpdate(value) {
      this.internalValue = value;
      this.$emit('update:modelValue', value);
    },
    handleChange(value) {
      this.$emit('change', value);
    }
  }
}
</script>

<style scoped>
/* Enhanced Mobile-Friendly Date Picker Styles */
.mobile-friendly-datepicker :deep(.dp__input) {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  border-radius: 0.5rem;
  border: 1px solid #d1d5db;
  width: 100%;
  background-color: white;
  transition: all 0.2s ease-in-out;
}

.mobile-friendly-datepicker :deep(.dp__input:hover) {
  border-color: #a5b4fc;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.mobile-friendly-datepicker :deep(.dp__input:focus) {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.mobile-friendly-datepicker :deep(.dp__main) {
  font-size: 0.875rem;
}

.mobile-friendly-datepicker :deep(.dp__preset_ranges) {
  font-size: 0.875rem;
}

.mobile-friendly-datepicker :deep(.dp__action_buttons) {
  font-size: 0.875rem;
  padding: 0.5rem;
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
  .mobile-friendly-datepicker :deep(.dp__input) {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    min-height: 48px; /* Better touch target */
  }

  .mobile-friendly-datepicker :deep(.dp__main) {
    font-size: 1rem;
  }

  .mobile-friendly-datepicker :deep(.dp__menu) {
    max-width: calc(100vw - 2rem);
    margin: 0 1rem;
    border-radius: 0.75rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .mobile-friendly-datepicker :deep(.dp__preset_ranges) {
    font-size: 0.875rem;
    max-height: 250px;
    overflow-y: auto;
    padding: 0.5rem;
  }

  .mobile-friendly-datepicker :deep(.dp__preset_range) {
    padding: 0.75rem 1rem;
    margin-bottom: 0.25rem;
    border-radius: 0.5rem;
    min-height: 44px;
    display: flex;
    align-items: center;
  }

  .mobile-friendly-datepicker :deep(.dp__calendar_header) {
    padding: 1rem 0.5rem;
  }

  .mobile-friendly-datepicker :deep(.dp__calendar_item) {
    padding: 0.5rem;
    font-size: 0.875rem;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .mobile-friendly-datepicker :deep(.dp__action_buttons) {
    padding: 1rem;
    gap: 0.75rem;
  }

  .mobile-friendly-datepicker :deep(.dp__action_button) {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    min-height: 44px;
    border-radius: 0.5rem;
  }

  /* Time picker improvements for mobile */
  .mobile-friendly-datepicker :deep(.dp__time_input) {
    font-size: 1.25rem;
    padding: 0.75rem;
    min-height: 48px;
  }

  .mobile-friendly-datepicker :deep(.dp__inc_dec_button) {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Tablet responsive improvements */
@media (min-width: 769px) and (max-width: 1024px) {
  .mobile-friendly-datepicker :deep(.dp__input) {
    padding: 0.5rem 0.75rem;
    font-size: 0.9375rem;
  }

  .mobile-friendly-datepicker :deep(.dp__main) {
    font-size: 0.9375rem;
  }
}

/* Ensure date picker overlay doesn't get clipped */
.mobile-friendly-datepicker :deep(.dp__menu_wrap) {
  z-index: 9999;
}

.mobile-friendly-datepicker :deep(.dp__overlay) {
  z-index: 9998;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .mobile-friendly-datepicker :deep(.dp__input) {
    background-color: #374151;
    border-color: #4b5563;
    color: white;
  }

  .mobile-friendly-datepicker :deep(.dp__input:hover) {
    border-color: #6b7280;
  }

  .mobile-friendly-datepicker :deep(.dp__input:focus) {
    border-color: #8b5cf6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
  }
}
</style>
