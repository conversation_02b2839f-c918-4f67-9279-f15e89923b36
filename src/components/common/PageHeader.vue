<template>
  <div class="fixed top-0 left-0 right-0 z-30 flex items-center p-4 border-b gap-4 transition-all duration-300 ease-in-out"
       :class="[
         isMobile ? 'pl-4' : (this.$store.state.isSideMenuOpen ? 'pl-64' : 'pl-20')
       ]"
       style="background-color: rgba(16, 22, 36, 0.95);">
    <!-- Left Section: <PERSON>u Toggle and Back Button -->
    <div class="flex items-center space-x-3">
      <div class="flex items-center justify-center w-10 h-10 bg-white rounded-full cursor-pointer shadow-sm hover:bg-gray-100 transition-colors"
           @click="toggleSideMenu">
        <i v-if="!$store.state.isSideMenuOpen"
           class="fa fa-bars text-gray-800 text-lg"></i>
        <i v-else class="fa fa-close text-gray-800 text-lg"></i>
      </div>

      <button
          class="px-4 py-2 text-sm font-medium text-white bg-gray-700 rounded-full hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 shadow-sm transition-colors"
          :class="{ 'hidden sm:block': isMobile }"
          @click="goBack">
        <i class="fa fa-arrow-left mr-1"></i> <span class="hidden sm:inline">Back</span>
      </button>
    </div>

    <!-- Middle Section: Title -->
    <div class="flex-1 text-center">
      <h1 class="text-xl md:text-2xl font-bold text-white">
        <span style="color: rgb(41, 172, 128);">{{ pageName }}</span>
        <span class="text-gray-300 pl-2 hidden sm:inline">{{ pageSubtitle }}</span>
      </h1>
    </div>

    <!-- Right Section: Empty for balance -->
    <div class="w-8 sm:w-32"></div>
  </div>

  <!-- Spacer to prevent content from being hidden under the fixed header -->
  <div class="h-20"></div>
</template>

<script>
import { mapActions } from 'vuex'

export default {
  props: {
    pageName: {
      type: String,
      required: true
    },
    pageSubtitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      windowWidth: window.innerWidth,
    }
  },
  computed: {
    isMobile() {
      return this.windowWidth <= 768;
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    ...mapActions(['toggleSideMenu']),
    handleResize() {
      this.windowWidth = window.innerWidth;
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>
