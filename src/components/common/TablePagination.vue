<template>
  <div class="flex-grow text-right" v-show="totalPages > 0">
    <div class="inline-block bg-white border rounded-md divide-x">
      <!-- Previous page button -->
      <button 
        class="p-2 sm:p-3 px-3 sm:px-5" 
        :disabled="currentPage === 1"
        :class="{'opacity-50 cursor-not-allowed': currentPage === 1}"
        @click="$emit('page-change', currentPage - 1)" 
        v-show="totalPages > 1"
      >
        &larr;
      </button>
      
      <!-- First page -->
      <button 
        class="p-2 sm:p-3 px-3 sm:px-5" 
        :class="{'bg-gray-100': currentPage === 1}"
        @click="$emit('page-change', 1)"
      >
        1
      </button>
      
      <!-- Second page -->
      <button 
        class="p-2 sm:p-3 px-3 sm:px-5" 
        :class="{'bg-gray-100': currentPage === 2}"
        v-show="totalPages > 1"
        @click="$emit('page-change', 2)"
      >
        2
      </button>
      
      <!-- Ellipsis for many pages -->
      <button 
        class="p-2 sm:p-3 px-3 sm:px-5" 
        v-show="totalPages > 3 && currentPage > 3"
      >
        ...
      </button>
      
      <!-- Current page (if not 1, 2, or last) -->
      <button 
        class="p-2 sm:p-3 px-3 sm:px-5 bg-gray-100" 
        v-show="totalPages > 3 && currentPage > 2 && currentPage < totalPages"
        @click="$emit('page-change', currentPage)"
      >
        {{ currentPage }}
      </button>
      
      <!-- Ellipsis for many pages -->
      <button 
        class="p-2 sm:p-3 px-3 sm:px-5" 
        v-show="totalPages > 4 && currentPage < totalPages - 1"
      >
        ...
      </button>
      
      <!-- Last page (if more than 2 pages) -->
      <button 
        class="p-2 sm:p-3 px-3 sm:px-5" 
        :class="{'bg-gray-100': currentPage === totalPages}"
        v-show="totalPages > 2"
        @click="$emit('page-change', totalPages)"
      >
        {{ totalPages }}
      </button>
      
      <!-- Next page button -->
      <button 
        class="p-2 sm:p-3 px-3 sm:px-5" 
        :disabled="currentPage === totalPages"
        :class="{'opacity-50 cursor-not-allowed': currentPage === totalPages}"
        @click="$emit('page-change', currentPage + 1)" 
        v-show="totalPages > 1 && currentPage < totalPages"
      >
        &rarr;
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TablePagination',
  props: {
    total: { 
      type: Number, 
      required: true 
    },
    limit: { 
      type: Number, 
      default: 10 
    },
    currentPage: { 
      type: Number, 
      default: 1 
    }
  },
  computed: {
    totalPages() {
      return Math.ceil(this.total / this.limit);
    }
  }
}
</script>
