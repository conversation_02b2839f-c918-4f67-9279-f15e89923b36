<template>
  <div v-if="show" class="fixed inset-0 bg-gray-500 bg-opacity-50 z-50 flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-lg overflow-hidden w-full max-w-4xl p-6">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-bold">{{ title }}</h3>
        <button @click="$emit('close')" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
      </div>
      
      <div class="overflow-auto max-h-[70vh]">
        <div v-if="isValidJson" class="bg-gray-50 p-4 rounded-md">
          <pre class="text-xs text-left whitespace-pre-wrap break-words">{{ formattedJson }}</pre>
        </div>
        <div v-else class="bg-red-50 p-4 rounded-md text-red-600">
          Invalid JSON data
        </div>
      </div>
      
      <div class="mt-4 flex justify-end">
        <button 
          @click="$emit('close')" 
          class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'JsonViewerModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: 'JSON Data'
    },
    jsonData: {
      type: [String, Object],
      default: null
    }
  },
  computed: {
    isValidJson() {
      return this.jsonData !== null;
    },
    formattedJson() {
      if (!this.jsonData) return '';
      
      try {
        // If jsonData is already an object, stringify it
        if (typeof this.jsonData === 'object') {
          return JSON.stringify(this.jsonData, null, 2);
        }
        
        // If jsonData is a string, try to parse it first
        const parsed = JSON.parse(this.jsonData);
        return JSON.stringify(parsed, null, 2);
      } catch (error) {
        console.error('Error formatting JSON:', error);
        return this.jsonData; // Return as is if parsing fails
      }
    }
  }
};
</script>

<style scoped>
pre {
  font-family: 'Courier New', Courier, monospace;
}
</style>
