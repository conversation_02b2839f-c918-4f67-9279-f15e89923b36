<template>
  <div class="py-1" :class="sectionClass">
    <a 
      @click="handleClick"
      class="group flex items-center px-4 py-2 text-sm cursor-pointer"
      :class="[
        itemClass,
        colorClasses[color] || '',
        disabled ? 'opacity-50 cursor-not-allowed' : ''
      ]"
      :title="title"
    >
      <slot name="icon"></slot>
      <slot>{{ text }}</slot>
    </a>
  </div>
</template>

<script>
export default {
  name: 'ActionItem',
  props: {
    text: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: 'default',
      validator: (value) => ['default', 'blue', 'red', 'green', 'yellow', 'gray', 'indigo'].includes(value)
    },
    itemClass: {
      type: String,
      default: ''
    },
    sectionClass: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    }
  },
  computed: {
    colorClasses() {
      return {
        'default': 'text-gray-700 hover:bg-gray-100',
        'blue': 'text-blue-700 hover:bg-blue-50',
        'red': 'text-red-700 hover:bg-red-50',
        'green': 'text-green-700 hover:bg-green-50',
        'yellow': 'text-yellow-700 hover:bg-yellow-50',
        'gray': 'text-gray-700 hover:bg-gray-100',
        'indigo': 'text-indigo-700 hover:bg-indigo-50'
      };
    }
  },
  methods: {
    handleClick(event) {
      if (this.disabled) {
        event.preventDefault();
        return;
      }
      
      this.$emit('click', event);
      
      // Find parent dropdown and close it if needed
      let parent = this.$parent;
      while (parent) {
        if (parent.$options.name === 'ActionDropdown' && parent.closeOnAction) {
          parent.closeDropdown();
          break;
        }
        parent = parent.$parent;
      }
    }
  }
};
</script>

<style scoped>
/* Add any custom styles here */
</style>
