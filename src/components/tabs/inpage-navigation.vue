<template>
  <div class="row">
    <div class="col">
      <div class="btn-group btn-block">
        <router-link :to="{ name: 'user' }" :class="'btn btn-outline-dark ' + user">System Users</router-link>
        <router-link :to="{ name: 'roles' }" :class="'btn btn-outline-dark ' + roles">System Roles</router-link>
        <router-link v-if="[1,2,3].includes(this.$store.state.role)" :to="{ name: 'configs' }" :class="'btn btn-outline-dark ' + configs">System Configs</router-link>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "inpage-navigation",
  props: {
    active: String // user | permission
  },
  data(){
    return {
      page: this.active,
      user: '',
      roles: '',
      configs: '',

    }
  },
  methods: {
    renderActive(){
      let active = this.page
      if (active === 'user') this.user = 'active'
      if (active === 'roles') this.roles = 'active'
      if (active === 'configs') this.configs = 'active'
    }
  },
  mounted() {
    this.renderActive()
  }
}
</script>

<style scoped>

</style>
