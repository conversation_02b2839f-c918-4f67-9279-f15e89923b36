<template>
  <div class="mb-4">
    <label :for="id" class="block text-sm font-medium text-gray-700">
      {{ label }}
      <span v-if="alias" class="font-light">({{ alias }})</span>
    </label>
    <div class="flex items-center space-x-2">
      <input
          :id="id"
          :type="type"
          v-model="inputValue"
          class="mt-1 block w-full border rounded-md shadow-sm border-gray-500 p-2 focus:border-indigo-500 focus:ring-indigo-500"
          :placeholder="placeholder"
          :required="required"
          v-on="computedEvents"
      />
      <!-- Optional Button -->
      <button
          v-if="buttonLabel"
          class="px-4 py-2 bg-indigo-600 text-white rounded-md shadow-md hover:bg-indigo-700 transition"
          @click="handleButtonClick"
      >
        {{ buttonLabel }}
      </button>
    </div>
    <p v-if="errorMessage" class="text-red-500 text-xs mt-1">{{ errorMessage }}</p>
  </div>
</template>

<script>
export default {
  props: {
    modelValue: String,
    alias: String,
    label: { type: String, default: "Market Alias" },
    id: { type: String, default: "market-alias" },
    placeholder: { type: String, default: "Enter value" },
    type: { type: String, default: "text" },
    required: { type: Boolean, default: false },
    events: { type: Object, default: () => ({}) },
    buttonLabel: { type: String, default: "" },
    buttonAction: { type: Function, default: null },
    enterKeyAction: { type: Function, default: null },
  },
  emits: ["update:modelValue"],
  data() {
    return {
      inputValue: this.modelValue,
      errorMessage: "",
    };
  },
  computed: {
    computedEvents() {
        const boundEvents = { ...this.$attrs }; // ✅ Capture dynamic events like @keyup.enter

        if (this.enterKeyAction) {
          boundEvents["keyup.enter"] = this.enterKeyAction; // ✅ Ensure keyup.enter is properly bound
        }

        return boundEvents;

    }
    //   const boundEvents = { ...this.events };
    //   console.log("sfsad: ", boundEvents);
    //
    //   if (this.enterKeyAction) {
    //     boundEvents["keyup.enter"] = this.enterKeyAction; // ✅ Ensure keyup.enter works
    //   }
    //
    //   return boundEvents;
    // }
  },
  methods: {
    handleButtonClick() {
      if (this.buttonAction) {
        this.buttonAction(this.inputValue);
      }
    },
  },
};
</script>