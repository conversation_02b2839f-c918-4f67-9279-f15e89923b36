<template>
  <div class="fixed top-0 bottom-0 left-0 right-0 bg-black bg-opacity-80 z-20"></div>
  <div class="z-30 relative max-w-4xl mt-24 bg-white mx-auto rounded-md">
    <div class="px-6 py-3 text-lg font-medium border-b">
      View {{item.game_name}} Bonus
      <button v-if="parseInt(item.bonus.status) === 1" @click.prevent="editRow(3)" class="inline-block px-4 py-1 rounded-md text-white bg-red-600 text-sm float-right">Deactivate</button>
      <button v-else @click.prevent="editRow(1)" class="inline-block px-4 py-1 rounded-md text-white bg-green-500 text-sm float-right">Activate</button>
    </div>
    <div class="block px-6 py-3">

      <div class="align-top" v-if="item.bonus">
        <div class="py-3 text-lg font-medium">
          Type: <span class="inline-block px-4 py-1 rounded-md text-white bg-blue-500">{{bonusType()}}</span>
        </div>
        <div v-if="!edit" class="py-1">
          Min Odd: {{ item.bonus.bonus_data.minimum_odd }}
        </div>
        <div v-if="!edit" class="py-1">
          Expiry Date: {{ item.bonus.expiry_date }}
        </div>
        <div v-if="!edit" class="py-1">
          Selections:
          <table class="text-xs gap-y-4">
            <thead>
            <tr>
              <td class="pr-4 py-1 font-medium">Games</td>
              <td class="pr-4 py-1 font-medium">Percentage</td>
            </tr>
            </thead>
            <tbody>
            <tr v-for="selection in item.bonus.bonus_data.selections">
              <td class="pr-4 text-center">{{ selection.games }}</td>
              <td class="pr-4 text-center">{{ (parseFloat(selection.value) * 100).toFixed(0) }}%</td>
            </tr>
            </tbody>
          </table>
        </div>
        <div v-if="edit">
          <div class="grid grid-cols-4 gap-4 mb-4">
            <div class="block">
              <label class="text-xs mb-1 block ">Minimum Odds</label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.minimum_odd">
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Expiry Date(UTC)</label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="datetime-local" v-model="form.expiry_date">
            </div>
          </div>
          <div class="text-xs mb-1 block">Selections</div>
          <div class="grid grid-cols-4 gap-4 mb-2" v-for="(selection,index) in form.selections">
            <div class="block">
              <label class="text-xs mb-1 block ">No of Games</label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="selection.games">
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Value X 100 (%)</label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="selection.value">
            </div>
            <div class="block">
              <button class="inline-block px-4 py-2 mt-5 bg-red-600 text-white border rounded-md" @click.prevent="removeRow(index)">X</button>
              <button v-if="index === form.selections.length - 1" class="inline-block px-4 py-2 mt-5 bg-green-600 text-white border rounded-md" @click.prevent="addRow">+</button>
            </div>
          </div>
        </div>
        <div class="py-3">
          <button v-if="edit" class="inline-block px-4 py-2 bg-blue-500 border rounded-md text-white" @click.prevent="updateRow()">Save</button>
          <button v-else class="inline-block px-4 py-2 bg-blue-500 border rounded-md text-white" @click.prevent="setEdit">Edit</button>
          <button class="inline-block px-4 py-2 bg-neutral-100 border rounded-md float-right" @click.prevent="hideModal()">Close</button>
        </div>
      </div>
      <div class="px-6 py-2 text-lg font-medium" v-else>No color scheme define for this game</div>
    </div>
  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
import {tr} from "vuejs3-datepicker/src/components/datepicker/locale";
export default {
  data() {
    return {
      loading: false,
      form: {
        id: null,
        minimum_odd: 0,
        selections: [],
        expiry_date: null
      },
      edit: false
    }
  },
  components: {
    Loading
  },
  props: {
    item: Object
  },
  methods: {
    ...mapActions(['updateClientBonus']),
    hideModal() {
      this.$parent.showBonus = false
    },
    bonusType(){
      let name = 'MultiBet Bonus'
      if (parseInt(this.item.bonus.bonus_type) === 1){
        name = 'MultiBet Bonus'
      }
      return name
    },

    setEdit(){
      this.edit = true
    },

    async editRow(status) {
      let app = this

      const payload = { id: this.item.bonus.id, status: status}

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this update this bonus!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.updateClientBonus(payload)
        },
      })
          .then(async (result) => {
            if (result.value.status === 200) {
              app.$swal.fire('Updated!', result.value.message, 'success')
              app.hideModal()
              await app.$parent.getGames()
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    async updateRow() {
      let app = this

      if (app.form.minimum_odd < 1){
        app.$swal.fire('Error!', "Minimum odds cannot be less than 1", 'error')
        return
      }

      let error = false
      let error1 = false
      for (let i = 0; i < app.form.selections.length; i++) {
        if (parseInt(app.form.selections[i].games) < 1){
          error = true
          break
        }
        if (parseInt(app.form.selections[i].value) < 0){
          error1 = true
          break
        }
      }
      if (error){
        app.$swal.fire('Error!', "No of Games cannot be less than 1", 'error')
        return
      }
      if (error1){
        app.$swal.fire('Error!', "Value cannot be less than 0", 'error')
        return
      }

      const payload = { id: app.form.id, bonus_data: { minimum_odd: app.form.minimum_odd, selections: app.form.selections }, expiry_date: app.expiry_date }

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this update this bonus!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.updateClientBonus(payload)
        },
      })
          .then(async (result) => {
            if (result.value.status === 200) {
              app.$swal.fire('Updated!', result.value.message, 'success')
              app.hideModal()
              await app.$parent.getGames()
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },

    addRow(){
      let item = { games: 0, value: 0}
      this.form.selections.push(item)
    },

    removeRow(index){
      this.form.selections.splice(index, 1)
    }
  },
  mounted() {
    this.form.id = this.item.bonus.id
    this.form.minimum_odd = this.item.bonus.bonus_data.minimum_odd
    this.form.selections = this.item.bonus.bonus_data.selections
    this.form.expiry_date = this.item.bonus.expiry_date
  }
}
</script>
