<template>
  <div class="fixed top-0 bottom-0 left-0 right-0 bg-black bg-opacity-80 z-20"></div>
  <div class="z-30 relative max-w-4xl mt-24 bg-white mx-auto rounded-md">
    <div class="px-6 py-3 text-lg font-medium border-b">Update {{item.game_name}} Settings</div>
    <div class="block px-6 py-3">

      <div class="grid grid-cols-1 gap-4 mb-4">
        <div class="block">
          <div class="grid grid-cols-4 gap-4 mb-4">
            <div class="block">
              <label class="text-xs mb-1 block ">Default Min Stake </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number" v-model="form.minimum_stake" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Default Max Stake </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number" v-model="form.maximum_stake" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Default Max Win </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.maximum_win" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Priority </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number" v-model="form.priority" min="1" max="10">
            </div>
          </div>
        </div>
        <div v-for="currency in currencies">
        <div class="block mb-2">
          <label class="text-xs mb-1 block ">Settings for (<strong>{{ currency }}</strong>) (Denominations separate them by a comma(,))</label>
        </div>
        <div class="grid grid-cols-4 gap-4 mb-4">
          <div class="block">
            <label class="text-xs mb-1 block ">Denominations</label>
            <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.denomination[currency].range" >
          </div>
          <div class="block">
            <label class="text-xs mb-1 block ">Min Stake </label>
            <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number" v-model="form.denomination[currency].min_stake" >
          </div>
          <div class="block">
            <label class="text-xs mb-1 block ">Max Stake </label>
            <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number" v-model="form.denomination[currency].max_stake" >
          </div>
          <div class="block">
            <label class="text-xs mb-1 block ">Max Win </label>
            <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.denomination[currency].max_win" >
          </div>
        </div>
        </div>
        <div class="block" v-if="this.$store.state.client_id === 0">
          <label class="text-xs mb-1 block ">Game Live URL </label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.game_url" >
        </div>
        <div class="block" v-if="this.$store.state.client_id === 0">
          <div class="grid grid-cols-4 gap-4 mb-4">
            <div class="block">
              <label class="text-xs mb-1 block ">Primary Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.primary_color" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Secondary Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.secondary_color" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Accent Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.accent_color" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Active BG Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.active_background" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Market BG Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.market_bg_color" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Active Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.active_color" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Market Btn Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.market_button_color" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Market Btn BG Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.market_button_background" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Odd Wrapper BG Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.market_wrap_odd_background" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Even Wrapper BG Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.market_wrap_even_background" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Odd Btn BG Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.odd_button_background" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Odd Border Btn Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.border_color_odd_button" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Odd Btn Active BG Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.odd_button_active_background" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Odd Border Btn Active Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.border_color_odd_button_active" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Odds Title Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.odds_title_color" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Odds Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.odds_color" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Odds Title Active Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.odds_title_active_color" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Odds Active Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.odds_active_color" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Odds Container BG Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.odds_container_background" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Container BG Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.my_container_background" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Betslip BG Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.betslip_background" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Betslip Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.betslip_color" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Betslip Market Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.betslip_market_color" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Betslip Odd Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.betslip_odd_color" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Placebet Btn Color Code </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.placebet_button_color" >
            </div>
          </div>
        </div>
      </div>
      <div class="gap-4 block text-sm text-right">
        <button class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" @click="hideModal()">Cancel</button>
        <button class="inline-block px-4 py-2 bg-primary rounded-md font-medium ml-2" id="addMember" @click="editRow">
          <vue-loaders-ball-beat color="red" scale="1" v-show="loading"></vue-loaders-ball-beat> Update
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
export default {
  data() {
    return {
      loading: false,
      form: {
        minimum_stake: '',
        maximum_stake: '',
        maximum_win: '',
        game_url: '',
        client_id: '',
        denomination: '',
        priority: '',
        primary_color: '',
        secondary_color: '',
        accent_color: '',
        active_background: '',
        market_bg_color: '',
        active_color: '',
        market_button_color: '',
        market_button_background: '',
        market_wrap_odd_background: '',
        market_wrap_even_background: '',
        odd_button_background: '',
        border_color_odd_button: '',
        odd_button_active_background: '',
        border_color_odd_button_active: '',
        odds_title_color: '',
        odds_color: '',
        odds_title_active_color: '',
        odds_active_color: '',
        odds_container_background: '',
        my_container_background: '',
        betslip_background: '',
        betslip_color: '',
        betslip_market_color: '',
        betslip_odd_color: '',
        placebet_button_color: '',
        id: ''
      },
      currencies: [],
    }
  },
  components: {
    Loading
  },
  props: {
    item: Object
  },
  mounted() {
    let app = this
    this.form.minimum_stake = this.item.minimum_stake
    this.form.maximum_stake = this.item.maximum_stake
    this.form.maximum_win = this.item.maximum_win
    this.form.game_url = this.item.game_url
    this.form.client_id = this.item.client_id
    this.form.denomination = JSON.parse(this.item.denomination)
    this.form.priority = this.item.priority
    this.form.primary_color = this.item.color_scheme?.primary_color ?? ''
    this.form.secondary_color = this.item.color_scheme?.secondary_color ?? ''
    this.form.accent_color = this.item.color_scheme?.accent_color ?? ''
    this.form.active_background = this.item.color_scheme?.active_background ?? ''
    this.form.market_bg_color = this.item.color_scheme?.market_bg_color ?? ''
    this.form.active_color = this.item.color_scheme?.active_color ?? ''
    this.form.market_button_color = this.item.color_scheme?.market_button_color ?? ''
    this.form.market_button_background = this.item.color_scheme?.market_button_background ?? ''
    this.form.market_wrap_odd_background = this.item.color_scheme?.market_wrap_odd_background ?? ''
    this.form.market_wrap_even_background = this.item.color_scheme?.market_wrap_even_background ?? ''
    this.form.odd_button_background = this.item.color_scheme?.odd_button_background ?? ''
    this.form.border_color_odd_button = this.item.color_scheme?.border_color_odd_button ?? ''
    this.form.odd_button_active_background = this.item.color_scheme?.odd_button_active_background ?? ''
    this.form.border_color_odd_button_active = this.item.color_scheme?.border_color_odd_button_active ?? ''
    this.form.odds_title_color = this.item.color_scheme?.odds_title_color ?? ''
    this.form.odds_color = this.item.color_scheme?.odds_color ?? ''
    this.form.odds_title_active_color = this.item.color_scheme?.odds_title_active_color ?? ''
    this.form.odds_active_color = this.item.color_scheme?.odds_active_color ?? ''
    this.form.odds_container_background = this.item.color_scheme?.odds_container_background ?? ''
    this.form.my_container_background = this.item.color_scheme?.my_container_background ?? ''
    this.form.betslip_background = this.item.color_scheme?.betslip_background ?? ''
    this.form.betslip_color = this.item.color_scheme?.betslip_color ?? ''
    this.form.betslip_market_color = this.item.color_scheme?.betslip_market_color ?? ''
    this.form.betslip_odd_color = this.item.color_scheme?.betslip_odd_color ?? ''
    this.form.placebet_button_color = this.item.color_scheme?.placebet_button_color ?? ''
    this.form.id = this.item.id

    this.currencies = this.item.currency.split(',')

    let denom = this.form.denomination

    let obj = {};
    for (let i = 0; i < app.currencies.length; i++) {
      if (denom[app.currencies[i]]) {
        obj[app.currencies[i]] = denom[app.currencies[i]];
      } else {
        obj[app.currencies[i]] = {
          range: "10,20,30,40,50",
          min_stake: 10,
          max_stake: 20000,
          max_win: 1000000,
        };
      }
    }
    console.log(`list: ${JSON.stringify(obj)}`)
    this.form.denomination = obj
  },
  methods: {
    ...mapActions(['updateClientGames']),
    hideModal() {
      this.$parent.showUpdateGame = false
    },

    async editRow() {
      let app = this

      const payload = this.form
      payload.denomination = JSON.stringify(this.form.denomination)

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this update this client game!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.updateClientGames(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Updated!', result.value.message, 'success')
              app.hideModal()
              await app.$parent.getGames()
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },
  }
}
</script>
