<template>
<Transition>
    <div class="block">
        <div class="fixed top-0 bottom-0 left-0 right-0 bg-black bg-opacity-80 z-20"></div>
        <div class="z-30 relative max-w-xl mt-24 bg-white mx-auto rounded-md">
            <div class="px-6 py-3 text-lg font-medium border-b">Top Up Account</div>
            <div class="block px-6 py-3">
                <div class="relative block mb-4 z-0">
                    <label class="text-xs mb-1 block">Receipt #</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="receipt">
                </div>
                <div class="relative block mb-4 z-0">
                    <label class="text-xs mb-1 block">Amount</label>
                    <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number" v-model="amount">

                </div>

                <div class="gap-4 block text-sm text-right">
                    <button class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" @click="hideModal()">Cancel</button>
                    <button class="inline-block px-4 py-2 bg-neutral-300 rounded-md font-medium ml-2" @click="AccountTopUp()" id="completePay">Complete Payment</button>
                </div>
            </div>
        </div>
    </div>
</Transition>
</template>

<script>
import store from "../store"
import $ from 'jquery'
import Swal from 'sweetalert2/dist/sweetalert2.js'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
export default {
    data() {
        return {
            showDropdown: false,
            amount: "",
            receipt:"",
            isLoading: false,
        }
    },
    methods: {
        hideModal() {
            this.$parent.showAccountTopUp = false
        },
        AccountTopUp(){
            let  vm  = this
            $('#completePay').html('Please Wait...');
            $.post({
                url: store.state.rootUrl + 'clients/v1/account/topup',
                post: "POST",
                data: JSON.stringify({
                    amount: vm.amount,
                    receiptNumber: vm.receipt
                }),
                headers: {
                    "X-Token-Key": vm.$cookies.get("accessToken"),
                    "X-Requested-With": "XMLHttpRequest",
                    "X-App-Key": store.state.systemToken,
                    "X-Authorization-Key": store.state.channel,
                    "Content-Type": "application/json"
                },
                cache: false,
                contentType: false,
                processData: false,
                success: function (response, status, jQxhr) {
                    $('#completePay').html('Complete Payment');
                    vm.loading = false
                    if (response.data.code == 200) {
                            Swal.fire({
                                icon: 'success',
                                title: response.data.message,
                                toast: true,
                                position: 'top-end',
                                showConfirmButton: false,
                                timer: 3000,
                                timerProgressBar: true,
                                didOpen: (toast) => {
                                    toast.addEventListener('mouseenter', Swal.stopTimer)
                                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                                }
                            })
                        vm.hideModal()
                        vm.$parent.getSummaryStats()

                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: response.data.message,
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 5000,
                            timerProgressBar: true,
                            didOpen: (toast) => {
                                toast.addEventListener('mouseenter', Swal.stopTimer)
                                toast.addEventListener('mouseleave', Swal.resumeTimer)
                            }
                        });
                    }
                },
                error: function (jQxhr, status, error) {
                    vm.loading = false
                    $('#completePay').html('Complete Payment');
                    var errorMessage = ""
                    if (jQxhr.responseJSON.data) {
                        errorMessage = jQxhr.responseJSON.data.message;
                        Swal.fire({
                            icon: 'error',
                            title: jQxhr.responseJSON.statusDescription + ": " + errorMessage,
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 5000,
                            timerProgressBar: true,
                            didOpen: (toast) => {
                                toast.addEventListener('mouseenter', Swal.stopTimer)
                                toast.addEventListener('mouseleave', Swal.resumeTimer)
                            }
                        });
                        if (jQxhr.responseJSON.data.code == 401) {
                            vm.$router.push({
                                name: 'login'
                            });
                        }
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: jQxhr.responseJSON.statusDescription + ": " + errorMessage,
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 5000,
                            timerProgressBar: true,
                            didOpen: (toast) => {
                                toast.addEventListener('mouseenter', Swal.stopTimer)
                                toast.addEventListener('mouseleave', Swal.resumeTimer)
                            }
                        });
                    }
                }
            });
        }
    }
}
</script>
