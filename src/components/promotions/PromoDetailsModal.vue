<template>
  <div v-if="show" class="fixed inset-0 bg-gray-800 bg-opacity-75 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-xl shadow-2xl w-full max-w-3xl overflow-hidden">
      <!-- Header with image banner -->
      <div class="relative h-36 bg-gradient-to-r from-indigo-600 to-purple-600 overflow-hidden">
        <img v-if="promo && promo.promo_images" :src="promo.promo_images" alt="Promotion Banner"
          class="w-full h-full object-cover" />

        <!-- Close button -->
        <button @click="$emit('close')"
          class="absolute top-4 right-4 bg-white bg-opacity-20 backdrop-blur-sm p-2 rounded-full hover:bg-opacity-30 transition-all">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <!-- Title overlay -->
        <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
          <h2 class="text-xl font-bold text-white">{{ promo ? promo.promo_name : 'Promotion Details' }}</h2>
          <div class="flex items-center mt-1 space-x-2">
            <span v-if="promo" class="px-1.5 py-0.5 text-xs font-bold rounded-full"
              :class="promo.promo_status === '1' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
              {{ promo.promo_status === "1" ? "Active" : "Inactive" }}
            </span>
            <span v-if="promo" class="px-1.5 py-0.5 text-xs font-bold rounded-full"
              :class="{
                'bg-blue-100 text-blue-800': promo.component === 'SPORTS',
                'bg-purple-100 text-purple-800': promo.component === 'DEPOSIT',
                'bg-yellow-100 text-yellow-800': promo.component === 'CASINO',
                'bg-gray-100 text-gray-800': !['SPORTS', 'DEPOSIT', 'CASINO'].includes(promo.component)
              }">
              {{ promo.component }}
            </span>
            <span v-if="promo" class="text-white text-xs">{{ promo.promo_type }}</span>
          </div>
        </div>
      </div>

      <!-- Content -->
      <div class="p-4">
        <div v-if="promo" class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Left column - Basic Info -->
          <div class="space-y-3">
            <h3 class="text-base font-semibold text-gray-800 border-b pb-1">Basic Information</h3>

            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600 font-medium">Promotion ID:</span>
                <span class="text-gray-800">{{ promo.promo_id }}</span>
              </div>

              <div class="flex justify-between">
                <span class="text-gray-600 font-medium">Bonus {{promo.bonus_type === '1' ? 'Percentage' : 'Amount'}}:</span>
                <span class="text-gray-800 font-semibold">{{promo.bonus_type === '1' ? `${promo.bonus_amount}%` : `KES. ${promo.bonus_amount}`}}</span>
              </div>

              <div class="flex justify-between">
                <span class="text-gray-600 font-medium">Frequency:</span>
                <span class="text-gray-800">{{ promo.frequency }}</span>
              </div>

              <div class="flex justify-between">
                <span class="text-gray-600 font-medium">Expiry Period:</span>
                <span class="text-gray-800">{{ promo.expiry_period_in_hours }} hours</span>
              </div>

              <div class="flex justify-between">
                <span class="text-gray-600 font-medium">Max Win:</span>
                <span class="text-gray-800">${{ promo.max_win }}</span>
              </div>

              <div class="flex justify-between">
                <span class="text-gray-600 font-medium">Max Times:</span>
                <span class="text-gray-800">{{ promo.max_times }}</span>
              </div>
            </div>

            <!-- Dates -->
            <div class="mt-3 pt-2 border-t">
              <h4 class="text-sm font-semibold text-gray-700 mb-1">Validity Period</h4>
              <div class="bg-gray-50 p-2 rounded-lg text-xs">
                <div class="flex items-center mb-1.5">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span class="text-gray-600">Start Date:</span>
                  <span class="ml-auto font-medium">{{ formatDate(promo.starting_date) }}</span>
                </div>
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span class="text-gray-600">End Date:</span>
                  <span class="ml-auto font-medium">{{ formatDate(promo.ending_date) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Right column - Betting Requirements -->
          <div class="space-y-3">
            <h3 class="text-base font-semibold text-gray-800 border-b pb-1">Betting Requirements</h3>

            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600 font-medium">Min Stake:</span>
                <span class="text-gray-800">${{ promo.min_stake }}</span>
              </div>

              <div class="flex justify-between">
                <span class="text-gray-600 font-medium">Min Odds:</span>
                <span class="text-gray-800">{{ promo.min_odds }}</span>
              </div>

              <div class="flex justify-between">
                <span class="text-gray-600 font-medium">Min Odds Per Pick:</span>
                <span class="text-gray-800">{{ promo.min_odds_per_pick || 'N/A' }}</span>
              </div>

              <div class="flex justify-between">
                <span class="text-gray-600 font-medium">Min Selections:</span>
                <span class="text-gray-800">{{ promo.min_selections }}</span>
              </div>

              <div class="flex justify-between">
                <span class="text-gray-600 font-medium">Bet Count:</span>
                <span class="text-gray-800">{{ promo.bet_count }}</span>
              </div>

              <div class="flex justify-between">
                <span class="text-gray-600 font-medium">Deduct Stake:</span>
                <span class="text-gray-800">{{ promo.deduct_stake === '1' ? 'Yes' : 'No' }}</span>
              </div>

              <div class="flex justify-between">
                <span class="text-gray-600 font-medium">Allow Duplicate Events:</span>
                <span class="text-gray-800">{{ promo.allow_duplicate_events === '1' ? 'Yes' : 'No' }}</span>
              </div>

              <div class="flex justify-between">
                <span class="text-gray-600 font-medium">Restrict Withdrawals:</span>
                <span class="text-gray-800">{{ promo.restrict_withdrawals === '1' ? 'Yes' : 'No' }}</span>
              </div>
            </div>

            <!-- Additional Info -->
            <div class="mt-3 pt-2 border-t">
              <h4 class="text-sm font-semibold text-gray-700 mb-1">Additional Information</h4>
              <div class="bg-gray-50 p-2 rounded-lg text-xs">
                <div v-if="promo.promo_url" class="flex items-center mb-1.5">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                  </svg>
                  <a :href="promo.promo_url" target="_blank" class="text-blue-600 hover:underline">
                    Promotion URL
                  </a>
                </div>
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span class="text-gray-600">Created At:</span>
                  <span class="ml-auto font-medium">{{ formatDate(promo.created_at, true) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty state -->
        <div v-else class="py-6 text-center">
          <p class="text-gray-500 text-sm">No promotion details available</p>
        </div>
      </div>

      <!-- Footer -->
      <div class="bg-gray-50 px-4 py-3 flex justify-end space-x-2">
        <button @click="$emit('close')"
          class="px-3 py-1.5 text-sm bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors">
          Close
        </button>
        <button v-if="promo" @click="editPromo"
          class="px-3 py-1.5 text-sm bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors">
          <i class="fa fa-edit mr-1"></i> Edit
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import moment from "moment";

export default {
  name: 'PromoDetailsModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    promo: {
      type: Object,
      default: null
    }
  },
  methods: {
    ...mapActions(["fillPromo"]),

    formatDate(dateString, includeTime = false) {
      if (!dateString) return 'N/A';

      const format = includeTime ? 'MMM DD, YYYY HH:mm' : 'MMM DD, YYYY';
      return moment(dateString).format(format);
    },

    editPromo() {
      if (this.promo) {
        this.fillPromo(this.promo);
        this.$router.push({ name: 'promotions-edit' });
      }
    }
  }
};
</script>

<style scoped>
/* Add any additional styling here */
</style>
