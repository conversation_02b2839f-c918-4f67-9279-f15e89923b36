<template>
  <div class="fixed top-0 bottom-0 left-0 right-0 bg-black bg-opacity-80 z-20"></div>
  <div class="z-30 relative max-w-xl mt-24 bg-white mx-auto rounded-md">
    <div class="px-6 py-3 text-lg font-medium border-b">Configs</div>
    <div class="block px-6 py-3">

      <div class="grid grid-cols-1 gap-4 mb-4">
        <div class="block">
          <label class="text-xs mb-1 block ">Callback URL </label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.callback_url" >
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Feeds URL </label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.kiron_fetch_url" >
        </div>
        <div class="block">
          <label class="text-xs mb-1 block ">Currency List (Separate them by a comma(,))</label>
          <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" v-model="form.currency_list">
        </div>
        <div class="block">
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="block">
              <label class="text-xs mb-1 block">Currency</label>
              <select class="w-full block px-2 text-xs py-2 border rounded-md outline-none" v-model="form.default_currency">
                <option v-for="item in currencies" :value="item.value">{{item.text}}</option>
              </select>
            </div>
            <div class="block">
              <label class="text-xs mb-1 block">Mode</label>
              <select class="w-full block px-2 text-xs py-2 border rounded-md outline-none" v-model="form.client_mode">
                <option v-for="item in modes" :value="item.value">{{item.text}}</option>
              </select>
            </div>
          </div>
        </div>
        <div class="block">
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="block">
              <label class="text-xs mb-1 block ">Cron Time </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number" v-model="form.cron_timer" >
            </div>
            <div class="block">
              <label class="text-xs mb-1 block ">Revenue Rate(%) </label>
              <input class="w-full block px-4 py-2 border bg-white rounded-md outline-none" type="number" v-model="form.revenue_rate" >
            </div>
          </div>
        </div>

      </div>
      <div class="gap-4 block text-sm text-right">
        <button class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" @click="hideModal()">Cancel</button>
      </div>
    </div>
  </div>
</template>



<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
export default {
  data() {
    return {
      loading: false,
      form: {
        default_currency: '',
        currency_list: '',
        callback_url: '',
        kiron_fetch_url: '',
        revenue_rate: '',
        cron_timer: '',
        client_mode: '',
        client_id: this.$store.state.client_id,
        id: ''
      },
      currencies: [{ text: 'KES', value: 'KES'},{ text: 'USD', value: 'USD'},{ text: 'NG', value: 'NG'}],
      modes: [{ text: 'Shared', value: 1},{ text: 'Dedicated', value: 2}],
    }
  },
  components: {
    Loading
  },
  props: {
    item: Object
  },
  mounted() {
    this.form.default_currency = this.item.default_currency
    this.form.callback_url = this.item.callback_url
    this.form.currency_list = this.item.currency_list
    this.form.kiron_fetch_url = this.item.kiron_fetch_url
    this.form.revenue_rate = this.item.revenue_rate
    this.form.cron_timer = this.item.cron_timer
    this.form.client_mode = this.item.client_mode
    this.form.client_id = this.item.id
    this.form.id = this.item.settings_id
  },
  methods: {
    ...mapActions(['updateClientSettings']),
    hideModal() {
      this.$parent.showUpdate = false
    },

    async editRow() {
      let app = this

      const payload = this.form

      app.$swal.fire({
        title: 'Are you sure?',
        text: "Doing this update this client settings!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, update!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async (res) => {
          return await this.updateClientSettings(payload)
        },
      })
          .then(async (result) => {
            console.log("result: " + JSON.stringify(result))
            if (result.value.status === 200) {
              app.$swal.fire('Updated!', result.value.message, 'success')
              app.hideModal()
              await app.$parent.getGames()
            } else {
              app.$swal.fire('Error!', result.value.message, 'error')
            }
          })
    },
  }
}
</script>
