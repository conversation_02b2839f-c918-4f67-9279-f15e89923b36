<template>
  <div class="fixed top-0 bottom-0 left-0 right-0 bg-black bg-opacity-80 z-20"></div>
  <div class="z-30 relative max-w-4xl mt-24 bg-white mx-auto rounded-md">
    <div class="px-6 py-3 text-lg font-medium border-b">View {{item.game_name}} Color Scheme</div>
    <div class="block px-6 py-3">

      <div class="grid grid-cols-6 gap-6 px-6 align-top" v-if="item.color_scheme">
        <div class="col-span-6 h-full overflow-auto">
          <!-- Stats for Customer -->
          <div class="grid grid-cols-4 gap-4">
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.primary_color }">
              <div class="text-xs text-white text-center">Primary Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.secondary_color }">
              <div class="text-xs text-white text-center">Secondary Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.accent_color }">
              <div class="text-xs text-white text-center">Accent Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.active_background }">
              <div class="text-xs text-white text-center">Active BG Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.market_bg_color }">
              <div class="text-xs text-white text-center">Market BG Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.active_color }">
              <div class="text-xs text-white text-center">Active Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.market_button_color }">
              <div class="text-xs text-white text-center">Market Btn Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.market_button_background }">
              <div class="text-xs text-white text-center">Market Btn BG Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.market_wrap_odd_background }">
              <div class="text-xs text-white text-center">Odd Wrapper BG Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.market_wrap_even_background }">
              <div class="text-xs text-white text-center">Even Wrapper BG Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.odd_button_background }">
              <div class="text-xs text-white text-center">Odd Btn Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.border_color_odd_button }">
              <div class="text-xs text-white text-center">Odd Border Btn Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.odd_button_active_background }">
              <div class="text-xs text-white text-center">Odd Btn Active Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.border_color_odd_button_active }">
              <div class="text-xs text-white text-center">Odd Border Btn Active Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.odds_title_color }">
              <div class="text-xs text-white text-center">Odds Title Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.odds_color }">
              <div class="text-xs text-white text-center">Odds Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.odds_title_active_color }">
              <div class="text-xs text-white text-center">Odds Title Active Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.odds_active_color }">
              <div class="text-xs text-white text-center">Odds Active Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.odds_container_background }">
              <div class="text-xs text-white text-center">Odds Container BG Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.my_container_background }">
              <div class="text-xs text-white text-center">Container BG Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.betslip_background }">
              <div class="text-xs text-white text-center">Betslip BG Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.betslip_color }">
              <div class="text-xs text-white text-center">Betslip Color Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.betslip_market_color }">
              <div class="text-xs text-white text-center">Betslip Market Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.betslip_odd_color }">
              <div class="text-xs text-white text-center">Betslip Odd Color</div>
            </div>
            <div class="block px-6 py-4 rounded-lg border shadow-md" :style="{ backgroundColor: item.color_scheme.placebet_button_color }">
              <div class="text-xs text-white text-center">Placebet Btn Color</div>
            </div>
          </div>
        </div>
      </div>
      <div class="px-6 py-3 text-lg font-medium" v-else>No color scheme define for this game</div>
      <div class="gap-4 block text-sm text-right pt-4">
        <button class="inline-block px-4 py-2 bg-neutral-100 border rounded-md ml-2" @click="hideModal()">Close</button>
      </div>
    </div>
  </div>
</template>



<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import {mapActions} from "vuex";
export default {
  data() {
    return {
      loading: false,
    }
  },
  components: {
    Loading
  },
  props: {
    item: Object
  },
  methods: {
    hideModal() {
      this.$parent.showColorScheme = false
    },
  }
}
</script>
