@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');
@import './styles/badges.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

body{
    @apply bg-gray-100 text-gray-700;
    font-family: 'Inter', sans-serif;
}input{
    @apply border-neutral-300 outline-none;
}
label{
    @apply text-neutral-500;
}

.bg-primary{
    background-color: #28a745;
    color: white;
}
.text-primary, .hover\:text-primary:hover{
    color: #111;
}
.text-accent{
    color: yellow;
}
.text-2xs{
    font-size: .75em;
}
.text-3xs{
    font-size: .65em;
}
.text-4xs{
     font-size: .5em;
}


/* Filter card styles */
.filter-card {
    background-color: white;
    transition: all 0.2s ease;
  }
  
  .filter-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transform: translateY(-2px);
  }

/* Date picker styles */
:deep(.dp__input) {
  padding: 0.25rem 2rem;
  font-size: 0.75rem;
  line-height: 1rem;
  border-radius: 0.375rem;
}

:deep(.dp__main) {
  font-size: 0.75rem;
}

/* Transaction type styles */
.type-overdraft {
  @apply text-red-600;
}

.type-payment {
  @apply text-blue-600;
}

.type-deposit {
  @apply text-green-600;
}

.type-refund {
  @apply text-purple-600;
}

.type-default {
  @apply text-gray-600;
}

/* Global Responsive Table Styles */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-responsive table {
  min-width: 600px;
}

/* Mobile table improvements */
@media (max-width: 768px) {
  .table-responsive table {
    font-size: 0.75rem;
    min-width: 500px;
  }

  .table-responsive th,
  .table-responsive td {
    padding: 0.375rem 0.25rem !important;
    white-space: nowrap;
  }

  /* Hide less important columns on mobile */
  .mobile-hidden {
    display: none !important;
  }

  /* Smaller text for mobile */
  .text-xs {
    font-size: 0.65rem !important;
  }

  /* Compact badges on mobile */
  .badge {
    font-size: 0.625rem !important;
    padding: 2px 6px !important;
    min-width: 60px !important;
  }
}

/* Tablet responsive improvements */
@media (min-width: 769px) and (max-width: 1024px) {
  .table-responsive table {
    font-size: 0.8125rem;
  }

  .table-responsive th,
  .table-responsive td {
    padding: 0.5rem 0.375rem !important;
  }

  .tablet-hidden {
    display: none !important;
  }
}

/* Improved scrollbar styling */
.table-responsive::-webkit-scrollbar {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
