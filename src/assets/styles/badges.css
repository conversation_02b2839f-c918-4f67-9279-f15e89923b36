/* Common Badge Styles
-------------------------------------------------- */

/* Badge container to ensure consistent alignment */
.badge-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px;
}

.badge-container.justify-start {
  justify-content: flex-start;
}

.badge-container.justify-end {
  justify-content: flex-end;
}

/* Charges badge */
.charges-badge {
  background-color: #FFAB40; /* Light orange */
  color: #333;
  font-size: 0.75rem;
  min-width: 100px;
  text-align: center;
  padding: 4px 8px;
  border-radius: 16px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.charges-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

/* Base badge style */
.badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.75rem;
  min-width: 100px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  letter-spacing: 0.3px;
}

.badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

/* Transaction/Receipt badges */
.transaction-badge {
  background-color: #e9ecef;
  color: #495057;
  min-width: 120px;
  font-family: monospace;
}

/* Customer badges */
.customer-badge {
  background-color: #f8f9fa;
  color: #212529;
  border: 1px solid #dee2e6;
  min-width: 100px;
}

/* Mobile/Phone badges */
.mobile-badge, .msisdn-badge, .phone-badge {
  background-color: #e9ecef;
  color: #495057;
  font-family: monospace;
  min-width: 120px;
}

/* Amount badges */
.amount-badge {
  background-color: #e3f2fd;
  color: #0d47a1;
  font-weight: 700;
  min-width: 120px;
}

/* Amount badge variations based on value */
.amount-low {
  background-color: #4caf50; /* Green */
  color: white;
}

.amount-medium-low {
  background-color: #8bc34a; /* Light Green */
  color: white;
}

.amount-medium {
  background-color: #ffc107; /* Amber */
  color: #212529;
}

.amount-medium-high {
  background-color: #ff9800; /* Orange */
  color: white;
}

.amount-high {
  background-color: #f44336; /* Red */
  color: white;
}

.amount-very-high {
  background-color: #9c27b0; /* Purple */
  color: white;
}

/* Account badge */
.account-badge {
  background-color: #e8f5e9;
  color: #1b5e20;
  font-family: monospace;
  min-width: 120px;
}

/* Description badge */
.description-badge {
  background-color: #f5f5f5;
  color: #616161;
  font-size: 0.75rem;
  min-width: 150px;
  text-align: left;
}

/* Source badge */
.source-badge {
  background-color: #f3e5f5;
  color: #6a1b9a;
  font-size: 0.75rem;
  min-width: 100px;
}

/* Date badge */
.date-badge {
  background-color: #e8eaf6;
  color: #283593;
  font-size: 0.7rem;
  min-width: 180px;
}

/* Status badges */
.status-badge {
  min-width: 90px;
  color: white;
}

.status-active, .status-success, .status-sent, .status-won {
  background-color: #2ecc71; /* Green */
}

.status-inactive, .status-failed, .status-lost {
  background-color: #e74c3c; /* Red */
}

.status-pending, .status-suspended {
  background-color: #f39c12; /* Orange */
}

.status-default {
  background-color: #95a5a6; /* Gray */
}

/* Transaction type badges */
.transaction-type-badge {
  min-width: 90px;
  color: white;
}

.type-credit {
  background-color: #2ecc71; /* Green */
}

.type-debit {
  background-color: #e74c3c; /* Red */
}

.type-refund {
  background-color: #9b59b6; /* Purple */
}

/* Bet type badges */
.bet-type-badge {
  min-width: 90px;
  color: white;
}

.bet-type-cash {
  background-color: #3498DB; /* Blue */
}

.bet-type-bonus {
  background-color: #9b59b6; /* Purple */
}

.bet-type-free {
  background-color: #2ecc71; /* Green */
}

.bet-type-default {
  background-color: #95a5a6; /* Gray */
}

/* Game badges */
.game-badge {
  min-width: 90px;
  color: white;
}

.game-virtual-league {
  background-color: #00b8d4; /* Cyan */
}

.game-virtual-turbo {
  background-color: #009688; /* Teal */
}

.game-default {
  background-color: #7f8c8d; /* Gray */
}

/* Charges badge */
.charges-badge {
  background-color: #FFAB40; /* Light orange */
  color: #333;
  font-size: 0.75rem;
  min-width: 100px;
  text-align: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.charges-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}


