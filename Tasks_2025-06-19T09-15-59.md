[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:Create Soft Gaming page under Virtual Bets DESCRIPTION:Create a new page for soft_gaming under virtualsbets in sidebar with API call to v1/soft_gaming using AutoTable with limit 100 by default
-[x] NAME:Add Soft Gaming API methods to store DESCRIPTION:Add getSoftGaming API method to store.js for v1/soft_gaming endpoint
-[x] NAME:Update sidebar to include Soft Gaming DESCRIPTION:Add Soft Gaming link under Virtual Bets section in sidebar with proper routing
-[x] NAME:Add Soft Gaming route to router DESCRIPTION:Add route configuration for soft-gaming page in router.js
-[x] NAME:Enhance BetGames with game categories DESCRIPTION:Add game categories dropdown to BetGames by calling games/v1/game_categories API and add category_id param to getGames API
-[x] NAME:Create game category management UI DESCRIPTION:Create UI to add games to a category with dropdown selection and game cards for selection, sending gameids array to updateGames API
-[x] NAME:Enhance AutoTable with sorting functionality DESCRIPTION:Add sorting capabilities to AutoTable component similar to DataTable, including sortable headers and sort indicators
-[x] NAME:Add column reordering to AutoTable DESCRIPTION:Implement drag-and-drop column reordering functionality in AutoTable component
-[x] NAME:Update Soft Gaming page with enhanced table features DESCRIPTION:Update the Soft Gaming page to use the enhanced AutoTable with sorting and column reordering