# Responsive Tables Implementation Guide

## Overview

All tables in the application have been updated with comprehensive responsive features to ensure optimal viewing experience across all device sizes (mobile, tablet, and desktop).

## Key Features Implemented

### 1. Mobile Card View
- **Automatic switching**: Tables automatically switch to card view on mobile devices (≤768px)
- **Clean layout**: Each row becomes a card with label-value pairs
- **Actions preserved**: Action buttons are maintained in mobile cards

### 2. Responsive Column Hiding
- **Mobile hiding**: Columns marked with `responsive: 'hidden-mobile'` are hidden on mobile
- **Tablet hiding**: Columns marked with `responsive: 'hidden-tablet'` are hidden on tablets
- **Priority-based**: Keep most important columns visible on smaller screens

### 3. Enhanced Table Scrolling
- **Smooth scrolling**: Improved horizontal scrolling with momentum on touch devices
- **Custom scrollbars**: Better styled scrollbars for desktop users
- **Minimum widths**: Tables maintain readability with appropriate minimum widths

## How to Use

### Basic DataTable Usage
```vue
<data-table
  :headers="tableHeaders"
  :items="items"
  :has-actions="true"
  :mobile-card-view="true"
  :mobile-breakpoint="768"
  :tablet-breakpoint="1024"
>
  <!-- Your column templates -->
</data-table>
```

### Responsive Column Configuration
```javascript
tableHeaders: [
  { key: 'id', label: 'ID', align: 'center' },
  { key: 'name', label: 'Name', align: 'left' },
  { key: 'email', label: 'Email', align: 'left', responsive: 'hidden-mobile' },
  { key: 'phone', label: 'Phone', align: 'center', responsive: 'hidden-tablet' },
  { key: 'status', label: 'Status', align: 'center' },
  { key: 'created_at', label: 'Date', align: 'left', responsive: 'hidden-mobile' }
]
```

### New Props Available

#### DataTable Component Props
- `mobileCardView` (Boolean, default: true) - Enable/disable mobile card view
- `mobileBreakpoint` (Number, default: 768) - Breakpoint for mobile devices
- `tabletBreakpoint` (Number, default: 1024) - Breakpoint for tablet devices

#### Header Object Properties
- `responsive` (String) - Options: 'hidden-mobile', 'hidden-tablet'

## Implementation Examples

### 1. Customer Transactions Table
```javascript
// Example from transactions_table.vue
tableHeaders: [
  { key: 'transaction_type', label: 'Trxn Type', align: 'center' },
  { key: 'msisdn', label: 'Customer', align: 'center' },
  { key: 'amount', label: 'Amount', align: 'center' },
  { key: 'description', label: 'Description', align: 'left', responsive: 'hidden-mobile' },
  { key: 'source', label: 'Source', align: 'center', responsive: 'hidden-mobile' },
  { key: 'created_at', label: 'Date', align: 'left' }
]
```

### 2. Sports Bets Table
```javascript
// Example from sports_bets.vue
tableHeaders: [
  { key: "customer", label: "Customer", align: "center" },
  { key: "bet_reference", label: "Bet Reference", align: "center" },
  { key: "stake", label: "Stake", align: "center" },
  { key: "total_odd", label: "Total Odd", align: "center", responsive: "hidden-mobile" },
  { key: "possible_win", label: "Pos Win", align: "center" },
  { key: "bet_type", label: "Bet Type", align: "center", responsive: "hidden-mobile" },
  { key: "status", label: "Status", align: "center" },
  { key: "date", label: "Date", align: "center", responsive: "hidden-mobile" }
]
```

## CSS Classes Added

### Global Responsive Classes
- `.table-responsive` - Applied to table containers
- `.mobile-hidden` - Hides elements on mobile
- `.tablet-hidden` - Hides elements on tablet
- `.hidden-mobile` - Column-specific mobile hiding
- `.hidden-tablet` - Column-specific tablet hiding

### Mobile Card Styles
- `.mobile-card-view` - Container for mobile cards
- `.mobile-card` - Individual card styling
- `.mobile-card-row` - Row within a card
- `.mobile-card-label` - Label styling in cards
- `.mobile-card-value` - Value styling in cards
- `.mobile-card-actions` - Actions container in cards

## Breakpoints

- **Mobile**: ≤768px
- **Tablet**: 769px - 1024px
- **Desktop**: >1024px

## Best Practices

### Column Priority (Mobile)
1. **Always show**: ID, Name, Status, Primary actions
2. **Hide on mobile**: Descriptions, secondary dates, less critical data
3. **Hide on tablet**: Tertiary information, extra metadata

### Performance Considerations
- Tables automatically detect screen size changes
- Responsive behavior is handled via CSS and computed properties
- No performance impact on desktop users

## Migration Guide

### Existing Tables
1. Add `responsive` property to headers where appropriate
2. Test on mobile devices
3. Adjust column priorities based on user needs

### New Tables
1. Use the updated DataTable component
2. Configure responsive properties from the start
3. Test across all breakpoints

## Browser Support

- **Modern browsers**: Full support with smooth animations
- **iOS Safari**: Touch scrolling optimized
- **Android Chrome**: Hardware acceleration enabled
- **Desktop**: Enhanced scrollbar styling

## Troubleshooting

### Common Issues
1. **Cards not showing**: Check `mobileCardView` prop is true
2. **Columns not hiding**: Verify `responsive` property spelling
3. **Scrolling issues**: Ensure container has proper overflow settings

### Debug Tips
- Use browser dev tools to test different screen sizes
- Check console for any JavaScript errors
- Verify CSS classes are being applied correctly
