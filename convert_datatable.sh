#!/bin/bash

# Script to convert DataTable to AutoTable in Vue files

# Get list of files that still use data-table
files=$(grep -r -l "data-table" --include="*.vue" src/)

echo "Converting DataTable to AutoTable in the following files:"
echo "$files"

for file in $files; do
    echo "Processing: $file"
    
    # Replace import statements
    sed -i '' 's/import { DataTable,/import { AutoTable,/g' "$file"
    sed -i '' 's/import {DataTable,/import {AutoTable,/g' "$file"
    sed -i '' 's/, DataTable,/, AutoTable,/g' "$file"
    sed -i '' 's/,DataTable,/,AutoTable,/g' "$file"
    
    # Replace component registrations
    sed -i '' 's/DataTable,/AutoTable,/g' "$file"
    
    # Replace template tags
    sed -i '' 's/<data-table/<auto-table/g' "$file"
    sed -i '' 's/<\/data-table>/<\/auto-table>/g' "$file"
    
    # Replace :items with :data
    sed -i '' 's/:items=/:data=/g' "$file"
    
    echo "Converted: $file"
done

echo "Conversion complete!"
